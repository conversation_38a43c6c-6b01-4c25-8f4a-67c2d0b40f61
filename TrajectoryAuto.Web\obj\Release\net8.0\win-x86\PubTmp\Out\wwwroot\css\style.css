/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 应用容器 */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* 头部样式 */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    background: rgba(255,255,255,0.2);
    font-size: 0.875rem;
}

/* 主要内容区域 */
.app-main {
    display: flex;
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 80px); /* 视口高度减去头部高度 */
}

/* 场景管理面板 */
.scene-panel {
    width: 240px;
    background: white;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 8px rgba(0,0,0,0.1);
    margin-right: 8px;
}

/* 通道管理面板 */
.channel-panel {
    width: 260px;
    background: white;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 8px rgba(0,0,0,0.1);
    margin-right: 12px;
}

.panel-header {
    padding: 1rem 1.25rem;
    border-bottom: 2px solid #667eea;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    position: relative;
}

.panel-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.panel-header h2 {
    font-size: 1.1rem;
    color: #333;
    font-weight: 600;
    margin: 0;
}

/* 画布区域 */
.canvas-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
    overflow: hidden;
    height: 100%;
    border-radius: 12px 0 0 0;
    box-shadow: -2px 0 12px rgba(0,0,0,0.08);
    margin-left: 4px;
}

.canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
}

.canvas-toolbar {
    padding: 0.75rem 1.5rem;
    border-bottom: 2px solid #e8ecf0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    border-radius: 12px 0 0 0;
}

.tool-group, .control-group {
    display: flex;
    gap: 0.75rem;
}

.tool-btn {
    padding: 0.75rem 1.25rem;
    border: 2px solid #e8ecf0;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.tool-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.tool-btn:hover {
    background: #f8f9ff;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.tool-btn:hover::before {
    left: 100%;
}

.tool-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.tool-btn.active::before {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
}

.canvas-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

#trajectoryCanvas {
    border: 1px solid #ddd;
    cursor: crosshair;
    display: block;
    width: 100%;
    height: calc(100% - 2rem);
    /* 防止选中蓝色高亮 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* 防止触摸设备上的高亮 */
    -webkit-tap-highlight-color: transparent;
    margin: 0 auto;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.coordinate-display {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.875rem;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #d91a72 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #e8590c 100%);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    border-radius: 6px;
}

/* 列表样式 */
.scene-list, .channel-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.75rem;
    background: #fafbfc;
    margin-bottom: 0;
}

/* 面板操作按钮 */
.panel-actions {
    padding: 0.75rem;
    border-top: 1px solid #e8ecf0;
    background: white;
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.btn-edit {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
    color: white;
}

.btn-edit:hover {
    background: linear-gradient(135deg, #138496 0%, #1aa179 100%);
}

.btn-delete {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #c82333 0%, #d91a72 100%);
}

.scene-item.selected, .channel-item.selected {
    border: 2px solid #667eea;
    background-color: #f0f4ff;
}

.scene-item, .channel-item {
    padding: 1rem;
    margin-bottom: 0.75rem;
    border: 1px solid #e8ecf0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.scene-item::before, .channel-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.scene-item:hover, .channel-item:hover {
    background: #f8f9ff;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.scene-item:hover::before, .channel-item:hover::before {
    transform: scaleY(1);
}

.scene-item.active, .channel-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.scene-item.active::before, .channel-item.active::before {
    background: rgba(255,255,255,0.3);
    transform: scaleY(1);
}

.scene-item h4, .channel-item h4 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: inherit;
}

.scene-item p, .channel-item p {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
    line-height: 1.4;
}

.scene-item.active p, .channel-item.active p {
    opacity: 0.9;
}

/* 轨迹参数面板 */
.trajectory-params {
    padding: 1.25rem;
    border-top: 2px solid #e8ecf0;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    margin-top: auto;
}

.trajectory-params h3 {
    font-size: 1.1rem;
    margin-bottom: 1.25rem;
    color: #333;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.trajectory-params h3::before {
    content: '⚙️';
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.param-group {
    margin-bottom: 1.25rem;
}

.param-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #555;
    font-weight: 500;
}

.param-group input[type="number"] {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e8ecf0;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: white;
}

.param-group input[type="number"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.param-group input[type="checkbox"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
    accent-color: #667eea;
}

.param-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.param-group label:has(input[type="checkbox"]):hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.2s ease;
    overflow-y: auto;
    padding: 1rem;
    box-sizing: border-box;
}

.modal.show {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    opacity: 1;
    padding-top: 5vh;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 680px;
    transform: translateY(-10px);
    transition: transform 0.2s ease, opacity 0.2s ease;
    opacity: 0;
    position: relative;
    overflow: hidden;
    border: none;
    display: flex;
    flex-direction: column;
}

.modal.show .modal-content {
    transform: translateY(0);
    opacity: 1;
}

.scene-modal .modal-content {
    max-width: 900px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(135deg, #4a6cf7 0%, #6a11cb 100%);
    color: white;
    position: relative;
    border: none;
    border-radius: 10px 10px 0 0;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4a6cf7, #6a11cb, #4a6cf7);
    background-size: 200% 100%;
    animation: gradientBG 3s ease infinite;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.modal-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    line-height: 1.4;
}

.modal-header h3 i {
    margin-right: 10px;
    color: #4a6cf7;
    font-size: 1.3em;
}

.close {
    font-size: 1.5rem;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1;
    padding: 0.5rem;
    margin: -0.5rem -0.5rem -0.5rem 1rem;
    border-radius: 4px;
    background: transparent;
    border: none;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.modal-body {
    padding: 0.5rem 1.5rem 0.5rem;
    font-size: 0.9rem;
    line-height: 1.3;
    color: #4a5568;
    flex: 1;
    overflow-y: auto;
    max-height: 70vh;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
    margin-top: 0rem;
}

/* 场景表单样式 - 优化版 */
.scene-form {
    --input-height: 2rem;
    --input-padding: 0.3rem 0.6rem;
    --input-radius: 4px;
    --input-border: 1px solid #e2e8f0;
    --input-focus-border: #4a6cf7;
    --input-focus-shadow: 0 0 0 2px rgba(74, 108, 247, 0.15);
    --label-color: #4a5568;
    --label-margin: 0 0.5rem 0 0;
    --form-group-margin: 0 0 0.6rem 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 0.9rem;
}

.scene-form .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    padding: 0;
    align-items: flex-start;
    flex-wrap: wrap;
}

.scene-form .form-group {
    margin: var(--form-group-margin);
    position: relative;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    flex: 1;
    min-width: 160px;
}

.scene-form .form-group:hover {
    transform: translateY(-1px);
}

.scene-form label {
    margin: var(--label-margin);
    font-weight: 500;
    font-size: 0.85rem;
    color: var(--label-color);
    transition: all 0.15s ease;
    position: relative;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 60px;
    text-align: right;
}

.scene-form label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: #4a6cf7;
    border-radius: 2px;
    opacity: 0;
    transition: all 0.2s ease;
}

.scene-form .form-group:focus-within label::before {
    opacity: 1;
}

/* 统一所有输入框的样式 */
.scene-form input[type="text"],
.scene-form input[type="number"],
.scene-form input[type="file"],
.scene-form .form-control {
    width: 100%;
    min-width: 100px;
    height: var(--input-height);
    padding: var(--input-padding);
    border: var(--input-border);
    border-radius: var(--input-radius);
    font-size: 0.85rem;
    color: #2d3748;
    background-color: #fff;
    transition: all 0.15s ease;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    background-clip: padding-box;
    appearance: none;
    -webkit-appearance: none;
    line-height: 1.2;
    flex: 1;
    box-sizing: border-box;
}

.scene-form .form-control:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
    outline: none;
}

.scene-form .form-control:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    outline: none;
}

/* 数字输入框样式 */
.scene-form input[type="number"] {
    -moz-appearance: textfield;
    padding-right: 0.5rem;
}

.scene-form input[type="number"]::-webkit-outer-spin-button,
.scene-form input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* 复选框样式 */
.scene-form .form-check {
    display: flex;
    align-items: center;
    height: var(--input-height);
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    width: auto;
}

.form-check-input {
    margin: 0;
    width: 1.2em;
    height: 1.2em;
}

.scene-form .form-check:hover {
    background-color: #f8fafc;
}

.scene-form .form-check-input {
    margin: 0 0.75rem 0 0;
    width: 1.2rem;
    height: 1.2rem;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
    appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    background-color: #fff;
}

.scene-form .form-check-input:checked {
    background-color: var(--input-focus-border);
    border-color: var(--input-focus-border);
}

.scene-form .form-check-input:checked::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 0.8rem;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    line-height: 1;
}

.scene-form .form-check-label {
    margin: 0;
    font-weight: normal;
    font-size: 0.95rem;
    color: #4a5568;
    cursor: pointer;
    user-select: none;
}

/* 文件输入样式 */
.scene-form input[type="file"] {
    padding: 0.5rem 0;
    line-height: 1.2;
    background: none;
    border: none;
    box-shadow: none;
}

.scene-form input[type="file"]:focus {
    box-shadow: none;
}

/* 按钮样式 */
/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, 
                border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    margin: 0 0.25rem;
}

.btn i {
    margin-right: 0.25rem;
}

.btn-primary {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    color: #212529;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.btn-secondary:hover {
    background-color: #d1d7dc;
    border-color: #c5cbd0;
}

/* 确保按钮在模态框底部右对齐 */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 0.8rem 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
    gap: 0.8rem;
}

.modal-footer .btn {
    margin-left: 0.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .scene-form .form-group {
        min-width: 100%;
    }
    
    .modal-content {
        padding: 1.5rem;
    }
    
    .modal-header h3 {
        font-size: 1.25rem;
    }
    
    .btn {
        padding: 0.6rem 1.25rem;
        font-size: 0.9rem;
    }
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #555;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.875rem;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .scene-panel {
        width: 220px;
    }
    
    .channel-panel {
        width: 240px;
    }
}

@media (max-width: 1024px) {
    .scene-panel {
        width: 200px;
    }
    
    .channel-panel {
        width: 220px;
    }
    
    .canvas-toolbar {
        padding: 1rem;
    }
    
    .tool-group, .control-group {
        gap: 0.5rem;
    }
    
    .tool-btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 768px) {
    .app-main {
        flex-direction: column;
        gap: 0;
    }
    
    .scene-panel, .channel-panel {
        width: 100%;
        height: 180px;
        margin-right: 0;
        border-radius: 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .canvas-panel {
        flex: 1;
        min-height: 500px;
        border-radius: 0;
        margin-left: 0;
    }
    
    .canvas-toolbar {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
        border-radius: 0;
    }
    
    .tool-group, .control-group {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .tool-btn, .btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }
    
    .panel-header {
        padding: 0.75rem 1rem;
    }
    
    .panel-header h2 {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .scene-panel, .channel-panel {
        height: 160px;
    }
    
    .canvas-toolbar {
        padding: 0.75rem;
    }
    
    .tool-btn, .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .btn-small {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
    }
}

/* 轨迹设置区域样式 */
.trajectory-settings {
    display: flex;
    align-items: center;
    margin-right: 15px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    border-radius: 4px;
}

.setting-label {
    margin-right: 5px;
    font-size: 0.9rem;
    color: #e0e0e0;
    white-space: nowrap;
}

.setting-input {
    width: 60px !important;
    height: 28px;
    padding: 2px 5px;
    font-size: 0.9rem;
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #ccc;
}

.setting-check {
    display: inline-block;
    margin-left: 10px;
}

.setting-check .form-check-input {
    margin-top: 0;
}

.setting-check .form-check-label {
    font-size: 0.9rem;
    color: #e0e0e0;
    margin-left: 5px;
    cursor: pointer;
}
.control-group, .scene-control-group {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.control-label {
    margin-right: 5px;
    font-weight: bold;
    font-size: 0.9em;
}

/* 确保所有按钮大小一致 */
.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    line-height: 1.2;
}

