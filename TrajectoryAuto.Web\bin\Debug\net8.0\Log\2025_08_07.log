﻿#Software: TrajectoryAuto.Web
#ProcessID: 17400 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:48:46.0460000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
00:27:22.008  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
00:27:22.014  1 N - NewLife组件核心库 ©2002-2024 NewLife
00:27:22.014  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
00:27:22.014  1 N - TrajectoryAuto.Web 
00:27:22.014  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
00:27:22.014  1 N - NewLife数据中间件 ©2002-2024 NewLife
00:27:22.014  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
00:27:22.458  1 N - [TrajectoryAuto]待检查数据表：Scenes
00:27:22.472  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
00:27:22.514  1 N - [TrajectoryAuto] select * from sqlite_master
00:27:22.522  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
00:27:22.523  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
00:27:22.594  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
00:27:22.600  1 N - [TrajectoryAuto] Select Count(*) From Scenes
00:27:22.615  1 N - [TrajectoryAuto]待检查数据表：Channels
00:27:22.615  1 N - [TrajectoryAuto] select * from sqlite_master
00:27:22.625  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
00:27:22.626  1 N - [TrajectoryAuto] Select Count(*) From Channels
00:27:22.637  1 N - [TrajectoryAuto]待检查数据表：Trajectories
00:27:22.638  1 N - [TrajectoryAuto] select * from sqlite_master
00:27:22.647  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 92
00:27:22.647  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
00:27:22.658  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
00:27:22.658  1 N - [TrajectoryAuto] select * from sqlite_master
00:27:22.667  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 19,701
00:27:22.668  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
00:27:22.701  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 11588 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:50:18.0780000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
00:28:54.028  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
00:28:54.033  1 N - NewLife组件核心库 ©2002-2024 NewLife
00:28:54.033  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
00:28:54.033  1 N - TrajectoryAuto.Web 
00:28:54.033  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
00:28:54.033  1 N - NewLife数据中间件 ©2002-2024 NewLife
00:28:54.033  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
00:28:54.539  1 N - [TrajectoryAuto]待检查数据表：Scenes
00:28:54.546  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
00:28:54.567  1 N - [TrajectoryAuto] select * from sqlite_master
00:28:54.577  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
00:28:54.578  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
00:28:54.633  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
00:28:54.638  1 N - [TrajectoryAuto] Select Count(*) From Scenes
00:28:54.651  1 N - [TrajectoryAuto]待检查数据表：Channels
00:28:54.652  1 N - [TrajectoryAuto] select * from sqlite_master
00:28:54.660  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
00:28:54.661  1 N - [TrajectoryAuto] Select Count(*) From Channels
00:28:54.673  1 N - [TrajectoryAuto]待检查数据表：Trajectories
00:28:54.674  1 N - [TrajectoryAuto] select * from sqlite_master
00:28:54.684  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
00:28:54.684  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
00:28:54.694  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
00:28:54.695  1 N - [TrajectoryAuto] select * from sqlite_master
00:28:54.703  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
00:28:54.704  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
00:28:54.715  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 19980 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:31:26.1250000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:10:02.077  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:10:02.080  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:10:02.080  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:10:02.081  1 N - TrajectoryAuto.Web 
01:10:02.081  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:10:02.081  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:10:02.081  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:10:02.405  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:10:02.413  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:10:02.440  1 N - [TrajectoryAuto] select * from sqlite_master
01:10:02.450  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:10:02.451  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:10:02.523  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
01:10:02.529  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:10:02.540  1 N - [TrajectoryAuto]待检查数据表：Channels
01:10:02.541  1 N - [TrajectoryAuto] select * from sqlite_master
01:10:02.548  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
01:10:02.549  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:10:02.558  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:10:02.559  1 N - [TrajectoryAuto] select * from sqlite_master
01:10:02.567  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
01:10:02.568  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:10:02.577  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:10:02.578  1 N - [TrajectoryAuto] select * from sqlite_master
01:10:02.586  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
01:10:02.586  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:10:02.626  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21460 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:44:14.0930000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:22:50.049  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:22:50.053  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:22:50.053  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:22:50.054  1 N - TrajectoryAuto.Web 
01:22:50.054  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:22:50.054  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:22:50.054  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:22:50.397  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:22:50.403  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:22:50.421  1 N - [TrajectoryAuto] select * from sqlite_master
01:22:50.429  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:22:50.430  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:22:50.488  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
01:22:50.493  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:22:50.505  1 N - [TrajectoryAuto]待检查数据表：Channels
01:22:50.505  1 N - [TrajectoryAuto] select * from sqlite_master
01:22:50.513  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
01:22:50.514  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:22:50.524  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:22:50.524  1 N - [TrajectoryAuto] select * from sqlite_master
01:22:50.532  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
01:22:50.533  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:22:50.542  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:22:50.543  1 N - [TrajectoryAuto] select * from sqlite_master
01:22:50.551  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
01:22:50.551  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:22:50.597  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 22268 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:55:25.3900000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:34:01.352  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:34:01.357  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:34:01.357  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:34:01.357  1 N - TrajectoryAuto.Web 
01:34:01.357  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:34:01.357  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:34:01.357  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:34:01.815  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:34:01.822  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:34:01.844  1 N - [TrajectoryAuto] select * from sqlite_master
01:34:01.855  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:34:01.855  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:34:01.914  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
01:34:01.918  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:34:01.931  1 N - [TrajectoryAuto]待检查数据表：Channels
01:34:01.932  1 N - [TrajectoryAuto] select * from sqlite_master
01:34:01.942  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
01:34:01.943  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:34:01.957  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:34:01.958  1 N - [TrajectoryAuto] select * from sqlite_master
01:34:01.969  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
01:34:01.969  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:34:01.980  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:34:01.980  1 N - [TrajectoryAuto] select * from sqlite_master
01:34:01.990  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
01:34:01.991  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:34:02.015  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 12936 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:03:20.2810000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:41:56.216  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:41:56.225  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:41:56.226  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:41:56.226  1 N - TrajectoryAuto.Web 
01:41:56.226  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:41:56.226  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:41:56.227  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:41:56.825  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:41:56.835  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:41:56.870  1 N - [TrajectoryAuto] select * from sqlite_master
01:41:56.887  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:41:56.888  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:41:56.996  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
01:41:57.002  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:41:57.018  1 N - [TrajectoryAuto]待检查数据表：Channels
01:41:57.018  1 N - [TrajectoryAuto] select * from sqlite_master
01:41:57.031  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
01:41:57.032  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:41:57.047  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:41:57.047  1 N - [TrajectoryAuto] select * from sqlite_master
01:41:57.059  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
01:41:57.060  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:41:57.073  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:41:57.074  1 N - [TrajectoryAuto] select * from sqlite_master
01:41:57.085  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
01:41:57.086  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:41:57.114  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 6276 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:08:42.9210000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:47:18.871  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:47:18.876  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:47:18.876  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:47:18.876  1 N - TrajectoryAuto.Web 
01:47:18.876  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:47:18.877  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:47:18.877  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:47:19.374  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:47:19.383  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:47:19.407  1 N - [TrajectoryAuto] select * from sqlite_master
01:47:19.417  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:47:19.419  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:47:19.490  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
01:47:19.496  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:47:19.510  1 N - [TrajectoryAuto]待检查数据表：Channels
01:47:19.511  1 N - [TrajectoryAuto] select * from sqlite_master
01:47:19.520  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
01:47:19.521  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:47:19.532  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:47:19.533  1 N - [TrajectoryAuto] select * from sqlite_master
01:47:19.544  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
01:47:19.544  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:47:19.555  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:47:19.555  1 N - [TrajectoryAuto] select * from sqlite_master
01:47:19.565  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
01:47:19.565  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:47:19.592  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9676 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:13:39.9680000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:52:15.928  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:52:15.932  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:52:15.932  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:52:15.933  1 N - TrajectoryAuto.Web 
01:52:15.933  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:52:15.933  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:52:15.933  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:52:16.459  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:52:16.467  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:52:16.492  1 N - [TrajectoryAuto] select * from sqlite_master
01:52:16.503  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:52:16.504  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:52:16.586  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
01:52:16.593  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:52:16.613  1 N - [TrajectoryAuto]待检查数据表：Channels
01:52:16.614  1 N - [TrajectoryAuto] select * from sqlite_master
01:52:16.627  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
01:52:16.628  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:52:16.643  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:52:16.643  1 N - [TrajectoryAuto] select * from sqlite_master
01:52:16.655  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
01:52:16.655  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:52:16.667  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:52:16.668  1 N - [TrajectoryAuto] select * from sqlite_master
01:52:16.677  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
01:52:16.677  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:52:16.711  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 7644 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:17:40.4530000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:56:16.402  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:56:16.409  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:56:16.409  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:56:16.409  1 N - TrajectoryAuto.Web 
01:56:16.409  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:56:16.409  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:56:16.410  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:56:17.216  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:56:17.226  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:56:17.256  1 N - [TrajectoryAuto] select * from sqlite_master
01:56:17.269  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:56:17.269  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:56:17.361  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
01:56:17.369  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:56:17.387  1 N - [TrajectoryAuto]待检查数据表：Channels
01:56:17.388  1 N - [TrajectoryAuto] select * from sqlite_master
01:56:17.397  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
01:56:17.399  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:56:17.415  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:56:17.417  1 N - [TrajectoryAuto] select * from sqlite_master
01:56:17.429  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
01:56:17.430  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:56:17.442  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:56:17.443  1 N - [TrajectoryAuto] select * from sqlite_master
01:56:17.455  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
01:56:17.456  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:56:17.484  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 17768 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:20:50.5460000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:59:26.504  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:59:26.509  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:59:26.509  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:59:26.510  1 N - TrajectoryAuto.Web 
01:59:26.510  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:59:26.510  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:59:26.510  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:59:26.879  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:59:26.885  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:59:26.914  1 N - [TrajectoryAuto] select * from sqlite_master
01:59:26.921  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:59:26.922  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:59:26.992  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
01:59:26.997  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:59:27.010  1 N - [TrajectoryAuto]待检查数据表：Channels
01:59:27.011  1 N - [TrajectoryAuto] select * from sqlite_master
01:59:27.020  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
01:59:27.021  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:59:27.031  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:59:27.032  1 N - [TrajectoryAuto] select * from sqlite_master
01:59:27.041  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
01:59:27.041  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:59:27.050  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:59:27.051  1 N - [TrajectoryAuto] select * from sqlite_master
01:59:27.058  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
01:59:27.059  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:59:27.086  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 18808 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:08:38.2500000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
09:49:58.202  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
09:49:58.207  1 N - NewLife组件核心库 ©2002-2024 NewLife
09:49:58.208  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
09:49:58.208  1 N - TrajectoryAuto.Web 
09:49:58.208  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
09:49:58.208  1 N - NewLife数据中间件 ©2002-2024 NewLife
09:49:58.208  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
09:49:58.624  1 N - [TrajectoryAuto]待检查数据表：Scenes
09:49:58.630  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
09:49:58.651  1 N - [TrajectoryAuto] select * from sqlite_master
09:49:58.657  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
09:49:58.658  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
09:49:58.709  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
09:49:58.714  1 N - [TrajectoryAuto] Select Count(*) From Scenes
09:49:58.723  1 N - [TrajectoryAuto]待检查数据表：Channels
09:49:58.724  1 N - [TrajectoryAuto] select * from sqlite_master
09:49:58.730  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
09:49:58.731  1 N - [TrajectoryAuto] Select Count(*) From Channels
09:49:58.738  1 N - [TrajectoryAuto]待检查数据表：Trajectories
09:49:58.739  1 N - [TrajectoryAuto] select * from sqlite_master
09:49:58.746  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
09:49:58.746  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
09:49:58.753  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
09:49:58.754  1 N - [TrajectoryAuto] select * from sqlite_master
09:49:58.760  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
09:49:58.761  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
09:49:58.785  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 19504 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:52:09.8750000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
10:33:29.836  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
10:33:29.839  1 N - NewLife组件核心库 ©2002-2024 NewLife
10:33:29.840  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
10:33:29.840  1 N - TrajectoryAuto.Web 
10:33:29.840  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
10:33:29.840  1 N - NewLife数据中间件 ©2002-2024 NewLife
10:33:29.840  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
10:33:30.335  1 N - [TrajectoryAuto]待检查数据表：Scenes
10:33:30.341  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
10:33:30.361  1 N - [TrajectoryAuto] select * from sqlite_master
10:33:30.371  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
10:33:30.372  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
10:33:30.421  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
10:33:30.426  1 N - [TrajectoryAuto] Select Count(*) From Scenes
10:33:30.438  1 N - [TrajectoryAuto]待检查数据表：Channels
10:33:30.439  1 N - [TrajectoryAuto] select * from sqlite_master
10:33:30.445  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
10:33:30.446  1 N - [TrajectoryAuto] Select Count(*) From Channels
10:33:30.455  1 N - [TrajectoryAuto]待检查数据表：Trajectories
10:33:30.456  1 N - [TrajectoryAuto] select * from sqlite_master
10:33:30.463  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
10:33:30.464  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
10:33:30.472  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
10:33:30.473  1 N - [TrajectoryAuto] select * from sqlite_master
10:33:30.479  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
10:33:30.480  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
10:33:30.511  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21572 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:28:57.5460000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:10:17.467  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
11:10:17.475  1 N - NewLife组件核心库 ©2002-2024 NewLife
11:10:17.477  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
11:10:17.477  1 N - TrajectoryAuto.Web 
11:10:17.477  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
11:10:17.478  1 N - NewLife数据中间件 ©2002-2024 NewLife
11:10:17.478  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。

#Software: TrajectoryAuto.Web
#ProcessID: 21872 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:30:03.6870000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:11:23.568  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
11:11:23.582  1 N - NewLife组件核心库 ©2002-2024 NewLife
11:11:23.583  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
11:11:23.584  1 N - TrajectoryAuto.Web 
11:11:23.584  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
11:11:23.584  1 N - NewLife数据中间件 ©2002-2024 NewLife
11:11:23.585  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
11:11:25.314  1 N - [TrajectoryAuto]待检查数据表：Scenes
11:11:25.337  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
11:11:25.493  1 N - [TrajectoryAuto] select * from sqlite_master
11:11:25.507  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
11:11:25.508  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
11:11:25.595  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
11:11:25.603  1 N - [TrajectoryAuto] Select Count(*) From Scenes
11:11:25.619  1 N - [TrajectoryAuto]待检查数据表：Channels
11:11:25.620  1 N - [TrajectoryAuto] select * from sqlite_master
11:11:25.630  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
11:11:25.631  1 N - [TrajectoryAuto] Select Count(*) From Channels
11:11:25.644  1 N - [TrajectoryAuto]待检查数据表：Trajectories
11:11:25.644  1 N - [TrajectoryAuto] select * from sqlite_master
11:11:25.661  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
11:11:25.662  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
11:11:25.674  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
11:11:25.675  1 N - [TrajectoryAuto] select * from sqlite_master
11:11:25.685  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
11:11:25.688  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
11:11:25.704  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
13:08:12.962 11 N T 任务 [5]TimerX.CopyNow (500ms) 耗时过长 1,850ms，建议使用异步任务Async=true

#Software: TrajectoryAuto.Web
#ProcessID: 24396 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:30:35.9840000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
14:11:55.948  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
14:11:55.951  1 N - NewLife组件核心库 ©2002-2024 NewLife
14:11:55.951  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
14:11:55.951  1 N - TrajectoryAuto.Web 
14:11:55.951  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
14:11:55.952  1 N - NewLife数据中间件 ©2002-2024 NewLife
14:11:55.952  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
14:11:56.473  1 N - [TrajectoryAuto]待检查数据表：Scenes
14:11:56.480  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
14:11:56.506  1 N - [TrajectoryAuto] select * from sqlite_master
14:11:56.516  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
14:11:56.517  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
14:11:56.576  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
14:11:56.582  1 N - [TrajectoryAuto] Select Count(*) From Scenes
14:11:56.595  1 N - [TrajectoryAuto]待检查数据表：Channels
14:11:56.595  1 N - [TrajectoryAuto] select * from sqlite_master
14:11:56.603  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
14:11:56.604  1 N - [TrajectoryAuto] Select Count(*) From Channels
14:11:56.614  1 N - [TrajectoryAuto]待检查数据表：Trajectories
14:11:56.614  1 N - [TrajectoryAuto] select * from sqlite_master
14:11:56.622  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
14:11:56.623  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
14:11:56.633  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
14:11:56.634  1 N - [TrajectoryAuto] select * from sqlite_master
14:11:56.642  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
14:11:56.642  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
14:11:56.686  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 27964 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:02:37.9060000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
14:43:57.864  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
14:43:57.869  1 N - NewLife组件核心库 ©2002-2024 NewLife
14:43:57.870  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
14:43:57.870  1 N - TrajectoryAuto.Web 
14:43:57.870  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
14:43:57.870  1 N - NewLife数据中间件 ©2002-2024 NewLife
14:43:57.870  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
14:43:58.608  1 N - [TrajectoryAuto]待检查数据表：Scenes
14:43:58.625  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
14:43:58.673  1 N - [TrajectoryAuto] select * from sqlite_master
14:43:58.707  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
14:43:58.708  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
14:43:58.805  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
14:43:58.811  1 N - [TrajectoryAuto] Select Count(*) From Scenes
14:43:58.828  1 N - [TrajectoryAuto]待检查数据表：Channels
14:43:58.828  1 N - [TrajectoryAuto] select * from sqlite_master
14:43:58.839  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
14:43:58.840  1 N - [TrajectoryAuto] Select Count(*) From Channels
14:43:58.854  1 N - [TrajectoryAuto]待检查数据表：Trajectories
14:43:58.855  1 N - [TrajectoryAuto] select * from sqlite_master
14:43:58.866  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
14:43:58.867  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
14:43:58.879  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
14:43:58.880  1 N - [TrajectoryAuto] select * from sqlite_master
14:43:58.890  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
14:43:58.891  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
14:43:58.941  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21356 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:41:31.1090000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
15:22:51.060  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
15:22:51.065  1 N - NewLife组件核心库 ©2002-2024 NewLife
15:22:51.066  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
15:22:51.067  1 N - TrajectoryAuto.Web 
15:22:51.067  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
15:22:51.067  1 N - NewLife数据中间件 ©2002-2024 NewLife
15:22:51.067  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
15:22:51.617  1 N - [TrajectoryAuto]待检查数据表：Scenes
15:22:51.626  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
15:22:51.654  1 N - [TrajectoryAuto] select * from sqlite_master
15:22:51.665  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
15:22:51.666  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
15:22:51.739  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
15:22:51.745  1 N - [TrajectoryAuto] Select Count(*) From Scenes
15:22:51.760  1 N - [TrajectoryAuto]待检查数据表：Channels
15:22:51.761  1 N - [TrajectoryAuto] select * from sqlite_master
15:22:51.770  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
15:22:51.770  1 N - [TrajectoryAuto] Select Count(*) From Channels
15:22:51.780  1 N - [TrajectoryAuto]待检查数据表：Trajectories
15:22:51.781  1 N - [TrajectoryAuto] select * from sqlite_master
15:22:51.790  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
15:22:51.791  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
15:22:51.801  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
15:22:51.801  1 N - [TrajectoryAuto] select * from sqlite_master
15:22:51.810  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
15:22:51.811  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
15:22:51.872  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23880 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:42:39.1560000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
15:23:59.117  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
15:23:59.121  1 N - NewLife组件核心库 ©2002-2024 NewLife
15:23:59.122  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
15:23:59.122  1 N - TrajectoryAuto.Web 
15:23:59.122  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
15:23:59.122  1 N - NewLife数据中间件 ©2002-2024 NewLife
15:23:59.122  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
15:23:59.664  1 N - [TrajectoryAuto]待检查数据表：Scenes
15:23:59.673  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
15:23:59.699  1 N - [TrajectoryAuto] select * from sqlite_master
15:23:59.709  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
15:23:59.710  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
15:23:59.786  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
15:23:59.792  1 N - [TrajectoryAuto] Select Count(*) From Scenes
15:23:59.806  1 N - [TrajectoryAuto]待检查数据表：Channels
15:23:59.807  1 N - [TrajectoryAuto] select * from sqlite_master
15:23:59.816  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
15:23:59.817  1 N - [TrajectoryAuto] Select Count(*) From Channels
15:23:59.829  1 N - [TrajectoryAuto]待检查数据表：Trajectories
15:23:59.830  1 N - [TrajectoryAuto] select * from sqlite_master
15:23:59.839  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
15:23:59.840  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
15:23:59.851  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
15:23:59.852  1 N - [TrajectoryAuto] select * from sqlite_master
15:23:59.860  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
15:23:59.861  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
15:23:59.915  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 8440 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:06:07.2340000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
15:47:27.185  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
15:47:27.191  1 N - NewLife组件核心库 ©2002-2024 NewLife
15:47:27.192  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
15:47:27.192  1 N - TrajectoryAuto.Web 
15:47:27.192  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
15:47:27.192  1 N - NewLife数据中间件 ©2002-2024 NewLife
15:47:27.192  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
15:47:27.694  1 N - [TrajectoryAuto]待检查数据表：Scenes
15:47:27.707  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
15:47:27.746  1 N - [TrajectoryAuto] select * from sqlite_master
15:47:27.758  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
15:47:27.758  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
15:47:27.857  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
15:47:27.864  1 N - [TrajectoryAuto] Select Count(*) From Scenes
15:47:27.879  1 N - [TrajectoryAuto]待检查数据表：Channels
15:47:27.880  1 N - [TrajectoryAuto] select * from sqlite_master
15:47:27.890  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
15:47:27.891  1 N - [TrajectoryAuto] Select Count(*) From Channels
15:47:27.904  1 N - [TrajectoryAuto]待检查数据表：Trajectories
15:47:27.904  1 N - [TrajectoryAuto] select * from sqlite_master
15:47:27.914  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 80
15:47:27.915  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
15:47:27.927  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
15:47:27.927  1 N - [TrajectoryAuto] select * from sqlite_master
15:47:27.936  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,503
15:47:27.937  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
15:47:27.985  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 25408 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:08:14.5780000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
15:49:34.516  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
15:49:34.525  1 N - NewLife组件核心库 ©2002-2024 NewLife
15:49:34.526  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
15:49:34.526  1 N - TrajectoryAuto.Web 
15:49:34.526  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
15:49:34.527  1 N - NewLife数据中间件 ©2002-2024 NewLife
15:49:34.527  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
15:49:35.200  1 N - [TrajectoryAuto]待检查数据表：Scenes
15:49:35.208  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
15:49:35.237  1 N - [TrajectoryAuto] select * from sqlite_master
15:49:35.249  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
15:49:35.249  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
15:49:35.332  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
15:49:35.340  1 N - [TrajectoryAuto] Select Count(*) From Scenes
15:49:35.355  1 N - [TrajectoryAuto]待检查数据表：Channels
15:49:35.356  1 N - [TrajectoryAuto] select * from sqlite_master
15:49:35.367  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
15:49:35.368  1 N - [TrajectoryAuto] Select Count(*) From Channels
15:49:35.380  1 N - [TrajectoryAuto]待检查数据表：Trajectories
15:49:35.381  1 N - [TrajectoryAuto] select * from sqlite_master
15:49:35.392  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
15:49:35.393  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
15:49:35.402  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
15:49:35.403  1 N - [TrajectoryAuto] select * from sqlite_master
15:49:35.411  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
15:49:35.413  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
15:49:35.423  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 27748 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:13:05.1560000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
15:54:25.107  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
15:54:25.110  1 N - NewLife组件核心库 ©2002-2024 NewLife
15:54:25.111  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
15:54:25.111  1 N - TrajectoryAuto.Web 
15:54:25.111  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
15:54:25.111  1 N - NewLife数据中间件 ©2002-2024 NewLife
15:54:25.111  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
15:54:25.495  1 N - [TrajectoryAuto]待检查数据表：Scenes
15:54:25.501  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
15:54:25.523  1 N - [TrajectoryAuto] select * from sqlite_master
15:54:25.530  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
15:54:25.531  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
15:54:25.584  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
15:54:25.590  1 N - [TrajectoryAuto] Select Count(*) From Scenes
15:54:25.600  1 N - [TrajectoryAuto]待检查数据表：Channels
15:54:25.601  1 N - [TrajectoryAuto] select * from sqlite_master
15:54:25.608  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
15:54:25.609  1 N - [TrajectoryAuto] Select Count(*) From Channels
15:54:25.618  1 N - [TrajectoryAuto]待检查数据表：Trajectories
15:54:25.619  1 N - [TrajectoryAuto] select * from sqlite_master
15:54:25.627  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
15:54:25.627  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
15:54:25.636  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
15:54:25.637  1 N - [TrajectoryAuto] select * from sqlite_master
15:54:25.644  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
15:54:25.645  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
15:54:25.699  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 29812 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:13:43.3590000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
15:55:03.314  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
15:55:03.319  1 N - NewLife组件核心库 ©2002-2024 NewLife
15:55:03.320  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
15:55:03.320  1 N - TrajectoryAuto.Web 
15:55:03.320  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
15:55:03.320  1 N - NewLife数据中间件 ©2002-2024 NewLife
15:55:03.320  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
15:55:03.852  1 N - [TrajectoryAuto]待检查数据表：Scenes
15:55:03.860  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
15:55:03.889  1 N - [TrajectoryAuto] select * from sqlite_master
15:55:03.900  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
15:55:03.901  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
15:55:03.997  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
15:55:04.004  1 N - [TrajectoryAuto] Select Count(*) From Scenes
15:55:04.019  1 N - [TrajectoryAuto]待检查数据表：Channels
15:55:04.020  1 N - [TrajectoryAuto] select * from sqlite_master
15:55:04.029  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
15:55:04.030  1 N - [TrajectoryAuto] Select Count(*) From Channels
15:55:04.041  1 N - [TrajectoryAuto]待检查数据表：Trajectories
15:55:04.042  1 N - [TrajectoryAuto] select * from sqlite_master
15:55:04.053  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
15:55:04.053  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
15:55:04.063  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
15:55:04.063  1 N - [TrajectoryAuto] select * from sqlite_master
15:55:04.072  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
15:55:04.072  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
15:55:04.083  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 5324 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:17:01.0310000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
15:58:20.986  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
15:58:20.990  1 N - NewLife组件核心库 ©2002-2024 NewLife
15:58:20.990  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
15:58:20.990  1 N - TrajectoryAuto.Web 
15:58:20.990  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
15:58:20.990  1 N - NewLife数据中间件 ©2002-2024 NewLife
15:58:20.990  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
15:58:21.354  1 N - [TrajectoryAuto]待检查数据表：Scenes
15:58:21.360  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
15:58:21.380  1 N - [TrajectoryAuto] select * from sqlite_master
15:58:21.389  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
15:58:21.390  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
15:58:21.446  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
15:58:21.452  1 N - [TrajectoryAuto] Select Count(*) From Scenes
15:58:21.462  1 N - [TrajectoryAuto]待检查数据表：Channels
15:58:21.463  1 N - [TrajectoryAuto] select * from sqlite_master
15:58:21.472  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
15:58:21.472  1 N - [TrajectoryAuto] Select Count(*) From Channels
15:58:21.482  1 N - [TrajectoryAuto]待检查数据表：Trajectories
15:58:21.483  1 N - [TrajectoryAuto] select * from sqlite_master
15:58:21.490  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
15:58:21.491  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
15:58:21.499  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
15:58:21.499  1 N - [TrajectoryAuto] select * from sqlite_master
15:58:21.505  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
15:58:21.506  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
15:58:21.515  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 2920 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:28:11.8590000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:09:31.808  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:09:31.816  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:09:31.817  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:09:31.817  1 N - TrajectoryAuto.Web 
16:09:31.817  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:09:31.818  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:09:31.818  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:09:32.495  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:09:32.503  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:09:32.535  1 N - [TrajectoryAuto] select * from sqlite_master
16:09:32.549  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:09:32.550  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:09:32.656  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:09:32.666  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:09:32.689  1 N - [TrajectoryAuto]待检查数据表：Channels
16:09:32.690  1 N - [TrajectoryAuto] select * from sqlite_master
16:09:32.702  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:09:32.703  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:09:32.716  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:09:32.716  1 N - [TrajectoryAuto] select * from sqlite_master
16:09:32.727  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:09:32.728  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:09:32.738  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:09:32.739  1 N - [TrajectoryAuto] select * from sqlite_master
16:09:32.749  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:09:32.749  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:09:32.792  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23460 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:42:35.7650000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:23:55.703  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:23:55.712  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:23:55.713  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:23:55.713  1 N - TrajectoryAuto.Web 
16:23:55.713  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:23:55.713  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:23:55.713  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:23:56.340  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:23:56.348  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:23:56.376  1 N - [TrajectoryAuto] select * from sqlite_master
16:23:56.386  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:23:56.387  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:23:56.464  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:23:56.470  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:23:56.485  1 N - [TrajectoryAuto]待检查数据表：Channels
16:23:56.486  1 N - [TrajectoryAuto] select * from sqlite_master
16:23:56.496  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:23:56.497  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:23:56.509  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:23:56.510  1 N - [TrajectoryAuto] select * from sqlite_master
16:23:56.521  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:23:56.521  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:23:56.533  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:23:56.533  1 N - [TrajectoryAuto] select * from sqlite_master
16:23:56.543  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:23:56.543  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:23:56.588  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 28700 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:43:41.9840000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:25:01.935  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:25:01.943  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:25:01.944  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:25:01.944  1 N - TrajectoryAuto.Web 
16:25:01.944  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:25:01.944  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:25:01.944  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:25:02.466  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:25:02.474  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:25:02.498  1 N - [TrajectoryAuto] select * from sqlite_master
16:25:02.509  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:25:02.510  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:25:02.582  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:25:02.589  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:25:02.603  1 N - [TrajectoryAuto]待检查数据表：Channels
16:25:02.604  1 N - [TrajectoryAuto] select * from sqlite_master
16:25:02.612  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:25:02.613  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:25:02.623  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:25:02.624  1 N - [TrajectoryAuto] select * from sqlite_master
16:25:02.634  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:25:02.635  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:25:02.644  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:25:02.645  1 N - [TrajectoryAuto] select * from sqlite_master
16:25:02.653  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:25:02.654  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:25:02.697  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 30384 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:47:48.6090000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:29:08.561  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:29:08.566  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:29:08.567  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:29:08.567  1 N - TrajectoryAuto.Web 
16:29:08.567  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:29:08.567  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:29:08.567  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:29:09.066  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:29:09.075  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:29:09.105  1 N - [TrajectoryAuto] select * from sqlite_master
16:29:09.118  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:29:09.118  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:29:09.223  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:29:09.232  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:29:09.250  1 N - [TrajectoryAuto]待检查数据表：Channels
16:29:09.250  1 N - [TrajectoryAuto] select * from sqlite_master
16:29:09.262  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:29:09.262  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:29:09.276  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:29:09.277  1 N - [TrajectoryAuto] select * from sqlite_master
16:29:09.289  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:29:09.290  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:29:09.301  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:29:09.302  1 N - [TrajectoryAuto] select * from sqlite_master
16:29:09.312  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:29:09.313  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:29:09.361  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 27956 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:53:09.2810000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:34:29.222  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:34:29.231  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:34:29.232  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:34:29.232  1 N - TrajectoryAuto.Web 
16:34:29.232  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:34:29.232  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:34:29.232  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:34:29.845  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:34:29.889  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:34:30.000  1 N - [TrajectoryAuto] select * from sqlite_master
16:34:30.015  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:34:30.016  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:34:30.112  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:34:30.119  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:34:30.142  1 N - [TrajectoryAuto]待检查数据表：Channels
16:34:30.142  1 N - [TrajectoryAuto] select * from sqlite_master
16:34:30.153  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:34:30.153  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:34:30.170  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:34:30.170  1 N - [TrajectoryAuto] select * from sqlite_master
16:34:30.181  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:34:30.182  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:34:30.193  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:34:30.193  1 N - [TrajectoryAuto] select * from sqlite_master
16:34:30.201  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:34:30.202  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:34:30.248  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 4000 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:57:22.0620000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:38:42.011  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:38:42.016  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:38:42.016  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:38:42.017  1 N - TrajectoryAuto.Web 
16:38:42.017  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:38:42.017  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:38:42.017  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:38:42.519  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:38:42.556  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:38:42.632  1 N - [TrajectoryAuto] select * from sqlite_master
16:38:42.642  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:38:42.643  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:38:42.725  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:38:42.731  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:38:42.748  1 N - [TrajectoryAuto]待检查数据表：Channels
16:38:42.749  1 N - [TrajectoryAuto] select * from sqlite_master
16:38:42.758  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:38:42.759  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:38:42.768  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:38:42.769  1 N - [TrajectoryAuto] select * from sqlite_master
16:38:42.779  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:38:42.780  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:38:42.789  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:38:42.790  1 N - [TrajectoryAuto] select * from sqlite_master
16:38:42.798  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:38:42.798  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:38:42.845  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9828 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:02:34.8590000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:43:54.814  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:43:54.819  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:43:54.819  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:43:54.819  1 N - TrajectoryAuto.Web 
16:43:54.819  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:43:54.820  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:43:54.820  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:43:55.381  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:43:55.389  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:43:55.419  1 N - [TrajectoryAuto] select * from sqlite_master
16:43:55.430  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:43:55.431  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:43:55.515  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:43:55.522  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:43:55.538  1 N - [TrajectoryAuto]待检查数据表：Channels
16:43:55.539  1 N - [TrajectoryAuto] select * from sqlite_master
16:43:55.549  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:43:55.549  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:43:55.561  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:43:55.562  1 N - [TrajectoryAuto] select * from sqlite_master
16:43:55.575  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:43:55.575  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:43:55.590  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:43:55.590  1 N - [TrajectoryAuto] select * from sqlite_master
16:43:55.600  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:43:55.600  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:43:55.650  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 30260 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:06:00.9840000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:47:20.933  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:47:20.937  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:47:20.938  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:47:20.938  1 N - TrajectoryAuto.Web 
16:47:20.938  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:47:20.938  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:47:20.938  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:47:21.375  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:47:21.382  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:47:21.411  1 N - [TrajectoryAuto] select * from sqlite_master
16:47:21.424  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:47:21.425  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:47:21.502  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:47:21.507  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:47:21.521  1 N - [TrajectoryAuto]待检查数据表：Channels
16:47:21.521  1 N - [TrajectoryAuto] select * from sqlite_master
16:47:21.533  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:47:21.534  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:47:21.544  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:47:21.544  1 N - [TrajectoryAuto] select * from sqlite_master
16:47:21.553  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:47:21.554  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:47:21.564  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:47:21.565  1 N - [TrajectoryAuto] select * from sqlite_master
16:47:21.573  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:47:21.574  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:47:21.618  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 30196 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:09:00.4530000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:50:20.396  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:50:20.402  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:50:20.402  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:50:20.403  1 N - TrajectoryAuto.Web 
16:50:20.403  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:50:20.403  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:50:20.403  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:50:21.080  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:50:21.094  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:50:21.140  1 N - [TrajectoryAuto] select * from sqlite_master
16:50:21.153  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:50:21.154  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:50:21.258  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:50:21.267  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:50:21.286  1 N - [TrajectoryAuto]待检查数据表：Channels
16:50:21.287  1 N - [TrajectoryAuto] select * from sqlite_master
16:50:21.298  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:50:21.299  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:50:21.311  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:50:21.312  1 N - [TrajectoryAuto] select * from sqlite_master
16:50:21.324  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:50:21.324  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:50:21.337  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:50:21.337  1 N - [TrajectoryAuto] select * from sqlite_master
16:50:21.348  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:50:21.349  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:50:21.396  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 6552 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:11:34.5310000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:52:54.482  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:52:54.488  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:52:54.489  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:52:54.489  1 N - TrajectoryAuto.Web 
16:52:54.489  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:52:54.489  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:52:54.489  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:52:55.111  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:52:55.120  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:52:55.166  1 N - [TrajectoryAuto] select * from sqlite_master
16:52:55.181  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:52:55.182  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:52:55.292  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:52:55.302  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:52:55.321  1 N - [TrajectoryAuto]待检查数据表：Channels
16:52:55.323  1 N - [TrajectoryAuto] select * from sqlite_master
16:52:55.334  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:52:55.335  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:52:55.349  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:52:55.350  1 N - [TrajectoryAuto] select * from sqlite_master
16:52:55.361  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:52:55.362  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:52:55.373  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:52:55.373  1 N - [TrajectoryAuto] select * from sqlite_master
16:52:55.383  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:52:55.385  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:52:55.435  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 20576 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:11:50.1400000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:53:10.102  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:53:10.107  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:53:10.108  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:53:10.108  1 N - TrajectoryAuto.Web 
16:53:10.108  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:53:10.108  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:53:10.108  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:53:10.636  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:53:10.649  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:53:10.687  1 N - [TrajectoryAuto] select * from sqlite_master
16:53:10.699  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:53:10.700  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:53:10.794  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:53:10.800  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:53:10.818  1 N - [TrajectoryAuto]待检查数据表：Channels
16:53:10.818  1 N - [TrajectoryAuto] select * from sqlite_master
16:53:10.828  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:53:10.829  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:53:10.841  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:53:10.842  1 N - [TrajectoryAuto] select * from sqlite_master
16:53:10.852  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:53:10.853  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:53:10.865  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:53:10.865  1 N - [TrajectoryAuto] select * from sqlite_master
16:53:10.876  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:53:10.876  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:53:10.889  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 25356 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:16:09.6710000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:57:29.609  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:57:29.621  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:57:29.622  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:57:29.622  1 N - TrajectoryAuto.Web 
16:57:29.622  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:57:29.622  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:57:29.622  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:57:31.216  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:57:31.379  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:57:31.886  1 N - [TrajectoryAuto] select * from sqlite_master
16:57:31.914  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:57:31.915  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:57:32.206  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:57:32.401  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:57:32.592  1 N - [TrajectoryAuto]待检查数据表：Channels
16:57:32.687  1 N - [TrajectoryAuto] select * from sqlite_master
16:57:32.758  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:57:32.788  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:57:32.940  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:57:33.012  1 N - [TrajectoryAuto] select * from sqlite_master
16:57:33.034  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:57:33.034  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:57:33.049  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:57:33.050  1 N - [TrajectoryAuto] select * from sqlite_master
16:57:33.062  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:57:33.062  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:57:33.123  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 30372 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:16:53.5310000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:58:13.469  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:58:13.479  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:58:13.480  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:58:13.480  1 N - TrajectoryAuto.Web 
16:58:13.480  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:58:13.481  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:58:13.481  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:58:14.358  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:58:14.370  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:58:14.419  1 N - [TrajectoryAuto] select * from sqlite_master
16:58:14.437  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:58:14.437  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:58:14.581  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:58:14.590  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:58:14.610  1 N - [TrajectoryAuto]待检查数据表：Channels
16:58:14.610  1 N - [TrajectoryAuto] select * from sqlite_master
16:58:14.622  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
16:58:14.623  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:58:14.638  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:58:14.638  1 N - [TrajectoryAuto] select * from sqlite_master
16:58:14.654  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
16:58:14.655  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:58:14.672  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:58:14.672  1 N - [TrajectoryAuto] select * from sqlite_master
16:58:14.688  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
16:58:14.688  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:58:14.721  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 28232 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:20:06.5620000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:01:26.513  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:01:26.521  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:01:26.522  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:01:26.523  1 N - TrajectoryAuto.Web 
17:01:26.523  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:01:26.523  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:01:26.523  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:01:27.134  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:01:27.145  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:01:27.195  1 N - [TrajectoryAuto] select * from sqlite_master
17:01:27.208  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:01:27.209  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:01:27.334  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:01:27.341  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:01:27.356  1 N - [TrajectoryAuto]待检查数据表：Channels
17:01:27.357  1 N - [TrajectoryAuto] select * from sqlite_master
17:01:27.366  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:01:27.367  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:01:27.379  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:01:27.380  1 N - [TrajectoryAuto] select * from sqlite_master
17:01:27.391  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:01:27.392  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:01:27.403  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:01:27.404  1 N - [TrajectoryAuto] select * from sqlite_master
17:01:27.414  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:01:27.415  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:01:27.463  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 18376 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:22:48.1710000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:04:08.114  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:04:08.120  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:04:08.121  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:04:08.121  1 N - TrajectoryAuto.Web 
17:04:08.121  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:04:08.121  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:04:08.121  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:04:08.768  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:04:08.778  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:04:08.810  1 N - [TrajectoryAuto] select * from sqlite_master
17:04:08.823  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:04:08.823  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:04:08.908  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:04:08.914  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:04:08.931  1 N - [TrajectoryAuto]待检查数据表：Channels
17:04:08.932  1 N - [TrajectoryAuto] select * from sqlite_master
17:04:08.942  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:04:08.943  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:04:08.958  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:04:08.959  1 N - [TrajectoryAuto] select * from sqlite_master
17:04:08.974  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:04:08.974  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:04:08.988  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:04:08.988  1 N - [TrajectoryAuto] select * from sqlite_master
17:04:09.000  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:04:09.000  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:04:09.049  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21452 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:31:14.0460000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:12:33.963  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:12:33.980  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:12:33.983  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:12:33.983  1 N - TrajectoryAuto.Web 
17:12:33.983  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:12:33.983  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:12:33.984  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:12:35.632  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:12:35.648  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:12:35.705  1 N - [TrajectoryAuto] select * from sqlite_master
17:12:35.735  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:12:35.736  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:12:35.936  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:12:35.952  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:12:35.995  1 N - [TrajectoryAuto]待检查数据表：Channels
17:12:35.996  1 N - [TrajectoryAuto] select * from sqlite_master
17:12:36.016  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:12:36.017  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:12:36.036  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:12:36.037  1 N - [TrajectoryAuto] select * from sqlite_master
17:12:36.053  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:12:36.054  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:12:36.077  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:12:36.077  1 N - [TrajectoryAuto] select * from sqlite_master
17:12:36.104  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:12:36.105  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:12:36.168  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23456 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:39:35.4060000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:20:55.339  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:20:55.352  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:20:55.353  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:20:55.353  1 N - TrajectoryAuto.Web 
17:20:55.353  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:20:55.354  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:20:55.354  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:20:56.743  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:20:56.765  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:20:56.842  1 N - [TrajectoryAuto] select * from sqlite_master
17:20:56.868  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:20:56.868  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:20:57.037  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:20:57.056  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:20:57.087  1 N - [TrajectoryAuto]待检查数据表：Channels
17:20:57.088  1 N - [TrajectoryAuto] select * from sqlite_master
17:20:57.109  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:20:57.112  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:20:57.133  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:20:57.134  1 N - [TrajectoryAuto] select * from sqlite_master
17:20:57.154  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:20:57.155  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:20:57.177  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:20:57.177  1 N - [TrajectoryAuto] select * from sqlite_master
17:20:57.192  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:20:57.193  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:20:57.259  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 4408 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:45:07.4060000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:26:27.335  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:26:27.345  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:26:27.349  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:26:27.350  1 N - TrajectoryAuto.Web 
17:26:27.350  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:26:27.350  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:26:27.350  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:26:28.578  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:26:28.598  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:26:28.682  1 N - [TrajectoryAuto] select * from sqlite_master
17:26:28.709  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:26:28.710  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:26:28.901  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:26:28.913  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:26:28.939  1 N - [TrajectoryAuto]待检查数据表：Channels
17:26:28.940  1 N - [TrajectoryAuto] select * from sqlite_master
17:26:28.956  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:26:28.956  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:26:29.161  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:26:29.165  1 N - [TrajectoryAuto] select * from sqlite_master
17:26:29.185  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:26:29.187  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:26:29.223  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:26:29.224  1 N - [TrajectoryAuto] select * from sqlite_master
17:26:29.254  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:26:29.255  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:26:29.317  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 14724 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:49:43.6400000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:31:03.549  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:31:03.563  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:31:03.564  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:31:03.565  1 N - TrajectoryAuto.Web 
17:31:03.565  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:31:03.565  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:31:03.565  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:31:04.864  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:31:04.883  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:31:04.959  1 N - [TrajectoryAuto] select * from sqlite_master
17:31:04.985  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:31:04.986  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:31:05.125  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:31:05.134  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:31:05.158  1 N - [TrajectoryAuto]待检查数据表：Channels
17:31:05.159  1 N - [TrajectoryAuto] select * from sqlite_master
17:31:05.179  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:31:05.179  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:31:05.202  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:31:05.203  1 N - [TrajectoryAuto] select * from sqlite_master
17:31:05.219  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:31:05.219  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:31:05.235  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:31:05.236  1 N - [TrajectoryAuto] select * from sqlite_master
17:31:05.250  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:31:05.250  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:31:05.309  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 2308 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:50:29.6560000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:31:49.598  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:31:49.605  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:31:49.606  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:31:49.607  1 N - TrajectoryAuto.Web 
17:31:49.607  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:31:49.607  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:31:49.607  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:31:50.343  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:31:50.353  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:31:50.386  1 N - [TrajectoryAuto] select * from sqlite_master
17:31:50.400  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:31:50.401  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:31:50.482  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:31:50.488  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:31:50.505  1 N - [TrajectoryAuto]待检查数据表：Channels
17:31:50.506  1 N - [TrajectoryAuto] select * from sqlite_master
17:31:50.519  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:31:50.520  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:31:50.534  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:31:50.534  1 N - [TrajectoryAuto] select * from sqlite_master
17:31:50.546  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:31:50.546  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:31:50.560  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:31:50.560  1 N - [TrajectoryAuto] select * from sqlite_master
17:31:50.572  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:31:50.573  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:31:50.623  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 30020 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:52:45.9060000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:34:05.834  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:34:05.845  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:34:05.847  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:34:05.847  1 N - TrajectoryAuto.Web 
17:34:05.847  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:34:05.847  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:34:05.847  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:34:07.309  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:34:07.334  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:34:07.496  1 N - [TrajectoryAuto] select * from sqlite_master
17:34:07.514  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:34:07.515  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:34:07.651  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:34:07.662  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:34:07.684  1 N - [TrajectoryAuto]待检查数据表：Channels
17:34:07.685  1 N - [TrajectoryAuto] select * from sqlite_master
17:34:07.700  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:34:07.700  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:34:07.731  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:34:07.731  1 N - [TrajectoryAuto] select * from sqlite_master
17:34:07.744  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:34:07.745  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:34:07.759  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:34:07.760  1 N - [TrajectoryAuto] select * from sqlite_master
17:34:07.772  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:34:07.773  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:34:07.841  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
17:34:29.148  5 Y TP 慢SQL[4,463ms] [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='d82f7789-8a4c-40eb-9730-b660ca0530bb'
17:34:34.576  5 Y TP 慢SQL[2,130ms] [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='fed18fc1-39bc-4411-a972-586cf9c1f483'

#Software: TrajectoryAuto.Web
#ProcessID: 30172 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 08:09:52.8750000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:51:12.826  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:51:12.831  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:51:12.832  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:51:12.832  1 N - TrajectoryAuto.Web 
17:51:12.832  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:51:12.833  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:51:12.833  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:51:13.505  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:51:13.520  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:51:13.570  1 N - [TrajectoryAuto] select * from sqlite_master
17:51:13.583  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:51:13.584  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:51:13.669  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:51:13.675  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:51:13.692  1 N - [TrajectoryAuto]待检查数据表：Channels
17:51:13.693  1 N - [TrajectoryAuto] select * from sqlite_master
17:51:13.704  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
17:51:13.704  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:51:13.716  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:51:13.717  1 N - [TrajectoryAuto] select * from sqlite_master
17:51:13.728  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
17:51:13.729  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:51:13.741  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:51:13.742  1 N - [TrajectoryAuto] select * from sqlite_master
17:51:13.751  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
17:51:13.752  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:51:13.800  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 29416 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 08:30:34.6560000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:11:54.604  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
18:11:54.608  1 N - NewLife组件核心库 ©2002-2024 NewLife
18:11:54.608  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
18:11:54.609  1 N - TrajectoryAuto.Web 
18:11:54.609  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
18:11:54.609  1 N - NewLife数据中间件 ©2002-2024 NewLife
18:11:54.609  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
18:11:55.145  1 N - [TrajectoryAuto]待检查数据表：Scenes
18:11:55.156  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
18:11:55.189  1 N - [TrajectoryAuto] select * from sqlite_master
18:11:55.198  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
18:11:55.198  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
18:11:55.273  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
18:11:55.279  1 N - [TrajectoryAuto] Select Count(*) From Scenes
18:11:55.293  1 N - [TrajectoryAuto]待检查数据表：Channels
18:11:55.294  1 N - [TrajectoryAuto] select * from sqlite_master
18:11:55.303  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
18:11:55.304  1 N - [TrajectoryAuto] Select Count(*) From Channels
18:11:55.318  1 N - [TrajectoryAuto]待检查数据表：Trajectories
18:11:55.318  1 N - [TrajectoryAuto] select * from sqlite_master
18:11:55.330  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
18:11:55.330  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
18:11:55.343  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
18:11:55.344  1 N - [TrajectoryAuto] select * from sqlite_master
18:11:55.355  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
18:11:55.356  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
18:11:55.400  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 11192 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 09:01:14.9530000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:42:34.898  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
18:42:34.908  1 N - NewLife组件核心库 ©2002-2024 NewLife
18:42:34.909  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
18:42:34.909  1 N - TrajectoryAuto.Web 
18:42:34.910  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
18:42:34.910  1 N - NewLife数据中间件 ©2002-2024 NewLife
18:42:34.910  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
18:42:35.913  1 N - [TrajectoryAuto]待检查数据表：Scenes
18:42:35.929  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
18:42:35.984  1 N - [TrajectoryAuto] select * from sqlite_master
18:42:36.000  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
18:42:36.000  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
18:42:36.107  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
18:42:36.115  1 N - [TrajectoryAuto] Select Count(*) From Scenes
18:42:36.131  1 N - [TrajectoryAuto]待检查数据表：Channels
18:42:36.132  1 N - [TrajectoryAuto] select * from sqlite_master
18:42:36.142  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
18:42:36.142  1 N - [TrajectoryAuto] Select Count(*) From Channels
18:42:36.156  1 N - [TrajectoryAuto]待检查数据表：Trajectories
18:42:36.156  1 N - [TrajectoryAuto] select * from sqlite_master
18:42:36.169  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
18:42:36.171  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
18:42:36.182  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
18:42:36.182  1 N - [TrajectoryAuto] select * from sqlite_master
18:42:36.194  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
18:42:36.195  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
18:42:36.246  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 440 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 09:08:44.2340000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:50:04.179  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
18:50:04.188  1 N - NewLife组件核心库 ©2002-2024 NewLife
18:50:04.188  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
18:50:04.189  1 N - TrajectoryAuto.Web 
18:50:04.189  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
18:50:04.189  1 N - NewLife数据中间件 ©2002-2024 NewLife
18:50:04.189  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
18:50:04.963  1 N - [TrajectoryAuto]待检查数据表：Scenes
18:50:04.977  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
18:50:05.023  1 N - [TrajectoryAuto] select * from sqlite_master
18:50:05.043  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
18:50:05.044  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
18:50:05.175  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
18:50:05.186  1 N - [TrajectoryAuto] Select Count(*) From Scenes
18:50:05.205  1 N - [TrajectoryAuto]待检查数据表：Channels
18:50:05.205  1 N - [TrajectoryAuto] select * from sqlite_master
18:50:05.344  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
18:50:05.345  1 N - [TrajectoryAuto] Select Count(*) From Channels
18:50:05.397  1 N - [TrajectoryAuto]待检查数据表：Trajectories
18:50:05.398  1 N - [TrajectoryAuto] select * from sqlite_master
18:50:05.413  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
18:50:05.415  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
18:50:05.430  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
18:50:05.430  1 N - [TrajectoryAuto] select * from sqlite_master
18:50:05.443  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
18:50:05.463  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
18:50:05.517  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 14028 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:32:04.2810000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
19:59:43.280  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
19:59:43.290  1 N - NewLife组件核心库 ©2002-2024 NewLife
19:59:43.292  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
19:59:43.292  1 N - TrajectoryAuto.Web 
19:59:43.292  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
19:59:43.292  1 N - NewLife数据中间件 ©2002-2024 NewLife
19:59:43.292  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
19:59:44.383  1 N - [TrajectoryAuto]待检查数据表：Scenes
19:59:44.399  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
19:59:44.456  1 N - [TrajectoryAuto] select * from sqlite_master
19:59:44.478  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
19:59:44.479  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
19:59:44.625  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
19:59:44.636  1 N - [TrajectoryAuto] Select Count(*) From Scenes
19:59:44.658  1 N - [TrajectoryAuto]待检查数据表：Channels
19:59:44.659  1 N - [TrajectoryAuto] select * from sqlite_master
19:59:44.674  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
19:59:44.674  1 N - [TrajectoryAuto] Select Count(*) From Channels
19:59:44.691  1 N - [TrajectoryAuto]待检查数据表：Trajectories
19:59:44.691  1 N - [TrajectoryAuto] select * from sqlite_master
19:59:44.706  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
19:59:44.706  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
19:59:44.721  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
19:59:44.721  1 N - [TrajectoryAuto] select * from sqlite_master
19:59:44.734  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
19:59:44.735  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
19:59:44.793  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9408 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:34:46.2180000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:02:25.230  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:02:25.242  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:02:25.245  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:02:25.245  1 N - TrajectoryAuto.Web 
20:02:25.245  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:02:25.245  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:02:25.245  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:02:26.071  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:02:26.089  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:02:26.173  1 N - [TrajectoryAuto] select * from sqlite_master
20:02:26.184  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:02:26.185  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:02:26.258  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:02:26.263  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:02:26.290  1 N - [TrajectoryAuto]待检查数据表：Channels
20:02:26.291  1 N - [TrajectoryAuto] select * from sqlite_master
20:02:26.301  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:02:26.301  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:02:26.325  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:02:26.325  1 N - [TrajectoryAuto] select * from sqlite_master
20:02:26.337  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:02:26.337  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:02:26.348  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:02:26.349  1 N - [TrajectoryAuto] select * from sqlite_master
20:02:26.357  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:02:26.357  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:02:26.369  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
20:04:09.836  7 Y TP 慢SQL[3,697ms] [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='c7c46fb9-9f3b-40c6-9202-9fc5b996c127'

#Software: TrajectoryAuto.Web
#ProcessID: 9320 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:45:01.2500000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:12:40.268  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:12:40.273  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:12:40.273  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:12:40.273  1 N - TrajectoryAuto.Web 
20:12:40.273  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:12:40.273  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:12:40.274  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:12:40.630  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:12:40.637  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:12:40.662  1 N - [TrajectoryAuto] select * from sqlite_master
20:12:40.672  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:12:40.673  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:12:40.749  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:12:40.756  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:12:40.769  1 N - [TrajectoryAuto]待检查数据表：Channels
20:12:40.770  1 N - [TrajectoryAuto] select * from sqlite_master
20:12:40.778  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:12:40.779  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:12:40.790  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:12:40.790  1 N - [TrajectoryAuto] select * from sqlite_master
20:12:40.799  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:12:40.800  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:12:40.808  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:12:40.809  1 N - [TrajectoryAuto] select * from sqlite_master
20:12:40.816  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:12:40.817  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:12:40.827  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 15156 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:50:51.3280000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:18:30.357  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:18:30.361  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:18:30.361  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:18:30.361  1 N - TrajectoryAuto.Web 
20:18:30.361  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:18:30.362  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:18:30.362  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:18:30.912  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:18:30.919  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:18:30.944  1 N - [TrajectoryAuto] select * from sqlite_master
20:18:30.955  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:18:30.956  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:18:31.017  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:18:31.022  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:18:31.034  1 N - [TrajectoryAuto]待检查数据表：Channels
20:18:31.035  1 N - [TrajectoryAuto] select * from sqlite_master
20:18:31.044  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:18:31.044  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:18:31.055  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:18:31.056  1 N - [TrajectoryAuto] select * from sqlite_master
20:18:31.114  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:18:31.114  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:18:31.128  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:18:31.128  1 N - [TrajectoryAuto] select * from sqlite_master
20:18:31.138  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:18:31.139  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:18:31.151  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 844 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:58:39.7340000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:26:18.757  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:26:18.760  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:26:18.761  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:26:18.761  1 N - TrajectoryAuto.Web 
20:26:18.761  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:26:18.761  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:26:18.761  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:26:19.092  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:26:19.098  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:26:19.117  1 N - [TrajectoryAuto] select * from sqlite_master
20:26:19.124  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:26:19.125  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:26:19.181  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:26:19.187  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:26:19.198  1 N - [TrajectoryAuto]待检查数据表：Channels
20:26:19.199  1 N - [TrajectoryAuto] select * from sqlite_master
20:26:19.205  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:26:19.206  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:26:19.214  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:26:19.215  1 N - [TrajectoryAuto] select * from sqlite_master
20:26:19.222  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:26:19.223  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:26:19.231  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:26:19.232  1 N - [TrajectoryAuto] select * from sqlite_master
20:26:19.238  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:26:19.239  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:26:19.249  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 12948 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:03:36.9370000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:31:15.958  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:31:15.961  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:31:15.962  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:31:15.962  1 N - TrajectoryAuto.Web 
20:31:15.962  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:31:15.962  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:31:15.962  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:31:16.445  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:31:16.451  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:31:16.473  1 N - [TrajectoryAuto] select * from sqlite_master
20:31:16.483  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:31:16.483  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:31:16.536  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:31:16.541  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:31:16.552  1 N - [TrajectoryAuto]待检查数据表：Channels
20:31:16.553  1 N - [TrajectoryAuto] select * from sqlite_master
20:31:16.560  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:31:16.561  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:31:16.569  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:31:16.570  1 N - [TrajectoryAuto] select * from sqlite_master
20:31:16.578  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:31:16.578  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:31:16.586  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:31:16.586  1 N - [TrajectoryAuto] select * from sqlite_master
20:31:16.593  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:31:16.594  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:31:16.602  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 2212 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:09:20.7340000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:36:59.752  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:36:59.756  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:36:59.757  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:36:59.757  1 N - TrajectoryAuto.Web 
20:36:59.757  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:36:59.757  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:36:59.757  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:37:00.296  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:37:00.304  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:37:00.328  1 N - [TrajectoryAuto] select * from sqlite_master
20:37:00.339  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:37:00.340  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:37:00.401  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:37:00.406  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:37:00.418  1 N - [TrajectoryAuto]待检查数据表：Channels
20:37:00.419  1 N - [TrajectoryAuto] select * from sqlite_master
20:37:00.427  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:37:00.428  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:37:00.437  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:37:00.438  1 N - [TrajectoryAuto] select * from sqlite_master
20:37:00.446  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:37:00.447  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:37:00.456  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:37:00.457  1 N - [TrajectoryAuto] select * from sqlite_master
20:37:00.464  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:37:00.465  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:37:00.474  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 16284 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:16:23.7030000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:44:02.713  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:44:02.724  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:44:02.725  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:44:02.726  1 N - TrajectoryAuto.Web 
20:44:02.726  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:44:02.726  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:44:02.726  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:44:03.652  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:44:03.666  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:44:03.702  1 N - [TrajectoryAuto] select * from sqlite_master
20:44:03.718  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:44:03.719  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:44:03.816  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:44:03.823  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:44:03.839  1 N - [TrajectoryAuto]待检查数据表：Channels
20:44:03.839  1 N - [TrajectoryAuto] select * from sqlite_master
20:44:03.850  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:44:03.852  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:44:03.864  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:44:03.864  1 N - [TrajectoryAuto] select * from sqlite_master
20:44:03.877  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:44:03.877  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:44:03.890  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:44:03.890  1 N - [TrajectoryAuto] select * from sqlite_master
20:44:03.902  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:44:03.902  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:44:03.916  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9660 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:19:17.5460000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:46:56.572  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:46:56.576  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:46:56.577  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:46:56.577  1 N - TrajectoryAuto.Web 
20:46:56.577  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:46:56.577  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:46:56.577  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:46:57.126  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:46:57.131  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:46:57.152  1 N - [TrajectoryAuto] select * from sqlite_master
20:46:57.162  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:46:57.163  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:46:57.222  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:46:57.227  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:46:57.240  1 N - [TrajectoryAuto]待检查数据表：Channels
20:46:57.240  1 N - [TrajectoryAuto] select * from sqlite_master
20:46:57.248  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:46:57.249  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:46:57.257  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:46:57.258  1 N - [TrajectoryAuto] select * from sqlite_master
20:46:57.267  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:46:57.267  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:46:57.275  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:46:57.276  1 N - [TrajectoryAuto] select * from sqlite_master
20:46:57.284  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:46:57.285  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:46:57.293  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 4080 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:21:51
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:49:30.032  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:49:30.038  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:49:30.038  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:49:30.039  1 N - TrajectoryAuto.Web 
20:49:30.039  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:49:30.039  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:49:30.039  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:49:30.458  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:49:30.463  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:49:30.483  1 N - [TrajectoryAuto] select * from sqlite_master
20:49:30.492  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:49:30.493  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:49:30.547  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:49:30.552  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:49:30.563  1 N - [TrajectoryAuto]待检查数据表：Channels
20:49:30.564  1 N - [TrajectoryAuto] select * from sqlite_master
20:49:30.571  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:49:30.572  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:49:30.580  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:49:30.582  1 N - [TrajectoryAuto] select * from sqlite_master
20:49:30.590  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:49:30.590  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:49:30.598  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:49:30.599  1 N - [TrajectoryAuto] select * from sqlite_master
20:49:30.605  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:49:30.606  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:49:30.616  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 19716 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:22:25.3900000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:50:04.420  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:50:04.424  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:50:04.424  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:50:04.424  1 N - TrajectoryAuto.Web 
20:50:04.424  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:50:04.425  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:50:04.425  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:50:04.865  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:50:04.871  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:50:04.892  1 N - [TrajectoryAuto] select * from sqlite_master
20:50:04.902  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:50:04.903  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:50:04.956  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:50:04.960  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:50:04.971  1 N - [TrajectoryAuto]待检查数据表：Channels
20:50:04.971  1 N - [TrajectoryAuto] select * from sqlite_master
20:50:04.979  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:50:04.980  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:50:04.988  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:50:04.989  1 N - [TrajectoryAuto] select * from sqlite_master
20:50:04.996  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:50:04.997  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:50:05.005  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:50:05.006  1 N - [TrajectoryAuto] select * from sqlite_master
20:50:05.013  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:50:05.013  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:50:05.021  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 2088 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:30:23.5310000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:58:02.504  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:58:02.515  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:58:02.517  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:58:02.517  1 N - TrajectoryAuto.Web 
20:58:02.517  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:58:02.517  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:58:02.517  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:58:04.632  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:58:04.732  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:58:04.924  1 N - [TrajectoryAuto] select * from sqlite_master
20:58:04.954  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:58:04.955  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:58:05.207  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
20:58:05.362  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:58:05.509  1 N - [TrajectoryAuto]待检查数据表：Channels
20:58:05.511  1 N - [TrajectoryAuto] select * from sqlite_master
20:58:05.526  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
20:58:05.526  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:58:05.580  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:58:05.593  1 N - [TrajectoryAuto] select * from sqlite_master
20:58:05.609  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
20:58:05.610  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:58:05.836  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:58:05.842  1 N - [TrajectoryAuto] select * from sqlite_master
20:58:06.068  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
20:58:06.143  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:58:06.185  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21588 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:55:01.5000000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:22:40.531  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:22:40.535  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:22:40.536  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:22:40.536  1 N - TrajectoryAuto.Web 
21:22:40.536  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:22:40.536  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:22:40.536  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:22:40.991  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:22:41.028  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:22:41.100  1 N - [TrajectoryAuto] select * from sqlite_master
21:22:41.110  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:22:41.111  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:22:41.175  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:22:41.181  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:22:41.193  1 N - [TrajectoryAuto]待检查数据表：Channels
21:22:41.194  1 N - [TrajectoryAuto] select * from sqlite_master
21:22:41.201  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:22:41.201  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:22:41.211  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:22:41.211  1 N - [TrajectoryAuto] select * from sqlite_master
21:22:41.219  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:22:41.220  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:22:41.228  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:22:41.228  1 N - [TrajectoryAuto] select * from sqlite_master
21:22:41.234  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:22:41.235  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:22:41.244  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 3376 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:56:40.7810000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:24:19.805  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:24:19.809  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:24:19.810  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:24:19.810  1 N - TrajectoryAuto.Web 
21:24:19.810  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:24:19.810  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:24:19.810  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:24:20.201  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:24:20.209  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:24:20.229  1 N - [TrajectoryAuto] select * from sqlite_master
21:24:20.236  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:24:20.236  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:24:20.292  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:24:20.296  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:24:20.307  1 N - [TrajectoryAuto]待检查数据表：Channels
21:24:20.307  1 N - [TrajectoryAuto] select * from sqlite_master
21:24:20.314  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:24:20.315  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:24:20.323  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:24:20.324  1 N - [TrajectoryAuto] select * from sqlite_master
21:24:20.331  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:24:20.332  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:24:20.339  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:24:20.340  1 N - [TrajectoryAuto] select * from sqlite_master
21:24:20.346  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:24:20.347  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:24:20.356  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 12372 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:00:19.5930000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:27:58.601  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:27:58.613  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:27:58.615  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:27:58.615  1 N - TrajectoryAuto.Web 
21:27:58.615  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:27:58.615  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:27:58.615  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:27:59.180  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:27:59.192  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:27:59.224  1 N - [TrajectoryAuto] select * from sqlite_master
21:27:59.236  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:27:59.238  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:27:59.351  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:27:59.362  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:27:59.405  1 N - [TrajectoryAuto]待检查数据表：Channels
21:27:59.406  1 N - [TrajectoryAuto] select * from sqlite_master
21:27:59.419  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:27:59.420  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:27:59.434  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:27:59.435  1 N - [TrajectoryAuto] select * from sqlite_master
21:27:59.448  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:27:59.449  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:27:59.463  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:27:59.463  1 N - [TrajectoryAuto] select * from sqlite_master
21:27:59.474  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:27:59.474  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:27:59.489  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 19220 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:07:51.1870000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:35:30.197  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:35:30.209  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:35:30.210  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:35:30.210  1 N - TrajectoryAuto.Web 
21:35:30.210  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:35:30.210  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:35:30.211  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:35:30.989  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:35:31.019  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:35:31.194  1 N - [TrajectoryAuto] select * from sqlite_master
21:35:31.281  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:35:31.284  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:35:31.438  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:35:31.453  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:35:31.476  1 N - [TrajectoryAuto]待检查数据表：Channels
21:35:31.477  1 N - [TrajectoryAuto] select * from sqlite_master
21:35:31.498  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:35:31.499  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:35:31.514  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:35:31.514  1 N - [TrajectoryAuto] select * from sqlite_master
21:35:31.529  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:35:31.529  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:35:31.543  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:35:31.544  1 N - [TrajectoryAuto] select * from sqlite_master
21:35:31.559  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:35:31.562  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:35:31.620  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21436 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:11:34.6090000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:39:13.631  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:39:13.634  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:39:13.635  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:39:13.635  1 N - TrajectoryAuto.Web 
21:39:13.635  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:39:13.635  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:39:13.635  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:39:13.981  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:39:13.987  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:39:14.005  1 N - [TrajectoryAuto] select * from sqlite_master
21:39:14.013  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:39:14.014  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:39:14.071  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:39:14.076  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:39:14.087  1 N - [TrajectoryAuto]待检查数据表：Channels
21:39:14.088  1 N - [TrajectoryAuto] select * from sqlite_master
21:39:14.095  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:39:14.096  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:39:14.104  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:39:14.105  1 N - [TrajectoryAuto] select * from sqlite_master
21:39:14.112  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:39:14.113  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:39:14.120  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:39:14.121  1 N - [TrajectoryAuto] select * from sqlite_master
21:39:14.127  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:39:14.128  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:39:14.168  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21360 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:17:19.4840000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:44:58.507  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:44:58.512  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:44:58.512  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:44:58.512  1 N - TrajectoryAuto.Web 
21:44:58.512  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:44:58.512  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:44:58.512  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:44:59.015  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:44:59.025  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:44:59.052  1 N - [TrajectoryAuto] select * from sqlite_master
21:44:59.063  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:44:59.063  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:44:59.137  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:44:59.144  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:44:59.157  1 N - [TrajectoryAuto]待检查数据表：Channels
21:44:59.158  1 N - [TrajectoryAuto] select * from sqlite_master
21:44:59.166  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:44:59.167  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:44:59.178  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:44:59.179  1 N - [TrajectoryAuto] select * from sqlite_master
21:44:59.187  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:44:59.188  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:44:59.197  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:44:59.198  1 N - [TrajectoryAuto] select * from sqlite_master
21:44:59.207  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:44:59.207  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:44:59.219  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21440 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:21:19.8120000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:48:58.840  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:48:58.844  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:48:58.844  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:48:58.845  1 N - TrajectoryAuto.Web 
21:48:58.845  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:48:58.845  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:48:58.845  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:48:59.232  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:48:59.238  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:48:59.256  1 N - [TrajectoryAuto] select * from sqlite_master
21:48:59.264  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:48:59.265  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:48:59.321  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:48:59.326  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:48:59.336  1 N - [TrajectoryAuto]待检查数据表：Channels
21:48:59.336  1 N - [TrajectoryAuto] select * from sqlite_master
21:48:59.345  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:48:59.345  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:48:59.353  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:48:59.354  1 N - [TrajectoryAuto] select * from sqlite_master
21:48:59.361  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:48:59.361  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:48:59.369  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:48:59.369  1 N - [TrajectoryAuto] select * from sqlite_master
21:48:59.376  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:48:59.377  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:48:59.385  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 20788 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:26:52.7180000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:54:31.730  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:54:31.742  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:54:31.744  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:54:31.744  1 N - TrajectoryAuto.Web 
21:54:31.744  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:54:31.745  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:54:31.745  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:54:32.269  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:54:32.319  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:54:32.415  1 N - [TrajectoryAuto] select * from sqlite_master
21:54:32.447  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:54:32.447  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:54:32.550  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:54:32.560  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:54:32.577  1 N - [TrajectoryAuto]待检查数据表：Channels
21:54:32.578  1 N - [TrajectoryAuto] select * from sqlite_master
21:54:32.590  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:54:32.590  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:54:32.604  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:54:32.604  1 N - [TrajectoryAuto] select * from sqlite_master
21:54:32.617  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:54:32.617  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:54:32.628  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:54:32.629  1 N - [TrajectoryAuto] select * from sqlite_master
21:54:32.640  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:54:32.640  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:54:32.655  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 19624 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:30:41.8430000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:58:20.867  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:58:20.872  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:58:20.872  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:58:20.873  1 N - TrajectoryAuto.Web 
21:58:20.873  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:58:20.873  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:58:20.873  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:58:21.233  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:58:21.240  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:58:21.267  1 N - [TrajectoryAuto] select * from sqlite_master
21:58:21.276  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:58:21.277  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:58:21.340  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
21:58:21.346  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:58:21.358  1 N - [TrajectoryAuto]待检查数据表：Channels
21:58:21.358  1 N - [TrajectoryAuto] select * from sqlite_master
21:58:21.365  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
21:58:21.365  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:58:21.375  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:58:21.375  1 N - [TrajectoryAuto] select * from sqlite_master
21:58:21.383  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
21:58:21.383  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:58:21.393  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:58:21.393  1 N - [TrajectoryAuto] select * from sqlite_master
21:58:21.402  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
21:58:21.403  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:58:21.414  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23252 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:35:02.9210000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:02:41.957  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:02:41.961  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:02:41.961  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:02:41.961  1 N - TrajectoryAuto.Web 
22:02:41.961  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:02:41.961  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:02:41.961  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。

#Software: TrajectoryAuto.Web
#ProcessID: 6512 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:35:25.6400000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:03:04.674  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:03:04.677  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:03:04.678  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:03:04.678  1 N - TrajectoryAuto.Web 
22:03:04.678  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:03:04.678  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:03:04.678  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:03:05.029  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:03:05.035  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:03:05.054  1 N - [TrajectoryAuto] select * from sqlite_master
22:03:05.062  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:03:05.062  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:03:05.117  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
22:03:05.122  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:03:05.132  1 N - [TrajectoryAuto]待检查数据表：Channels
22:03:05.133  1 N - [TrajectoryAuto] select * from sqlite_master
22:03:05.141  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
22:03:05.142  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:03:05.149  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:03:05.150  1 N - [TrajectoryAuto] select * from sqlite_master
22:03:05.157  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
22:03:05.158  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:03:05.166  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:03:05.166  1 N - [TrajectoryAuto] select * from sqlite_master
22:03:05.173  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
22:03:05.174  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:03:05.182  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 6660 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:41:17.6560000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:08:56.685  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:08:56.689  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:08:56.689  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:08:56.689  1 N - TrajectoryAuto.Web 
22:08:56.689  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:08:56.689  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:08:56.689  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:08:57.063  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:08:57.069  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:08:57.088  1 N - [TrajectoryAuto] select * from sqlite_master
22:08:57.096  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:08:57.097  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:08:57.152  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
22:08:57.157  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:08:57.170  1 N - [TrajectoryAuto]待检查数据表：Channels
22:08:57.170  1 N - [TrajectoryAuto] select * from sqlite_master
22:08:57.179  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
22:08:57.179  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:08:57.191  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:08:57.192  1 N - [TrajectoryAuto] select * from sqlite_master
22:08:57.199  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
22:08:57.200  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:08:57.207  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:08:57.207  1 N - [TrajectoryAuto] select * from sqlite_master
22:08:57.214  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
22:08:57.214  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:08:57.222  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 2484 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:46:45.1250000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:14:24.111  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:14:24.124  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:14:24.126  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:14:24.126  1 N - TrajectoryAuto.Web 
22:14:24.126  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:14:24.126  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:14:24.127  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:14:25.681  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:14:25.708  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:14:25.886  1 N - [TrajectoryAuto] select * from sqlite_master
22:14:25.902  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:14:25.902  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:14:26.076  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
22:14:26.088  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:14:26.110  1 N - [TrajectoryAuto]待检查数据表：Channels
22:14:26.110  1 N - [TrajectoryAuto] select * from sqlite_master
22:14:26.124  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
22:14:26.124  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:14:26.592  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:14:26.592  1 N - [TrajectoryAuto] select * from sqlite_master
22:14:26.626  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
22:14:26.626  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:14:26.639  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:14:26.640  1 N - [TrajectoryAuto] select * from sqlite_master
22:14:26.651  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
22:14:26.652  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:14:26.715  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 20420 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:53:12.0620000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:20:50.894  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:20:50.929  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:20:50.952  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:20:50.952  1 N - TrajectoryAuto.Web 
22:20:50.952  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:20:50.953  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:20:50.953  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:20:53.031  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:20:53.053  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:20:53.200  1 N - [TrajectoryAuto] select * from sqlite_master
22:20:53.215  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:20:53.216  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:20:53.324  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
22:20:53.340  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:20:53.388  1 N - [TrajectoryAuto]待检查数据表：Channels
22:20:53.389  1 N - [TrajectoryAuto] select * from sqlite_master
22:20:53.400  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
22:20:53.401  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:20:53.416  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:20:53.417  1 N - [TrajectoryAuto] select * from sqlite_master
22:20:53.430  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
22:20:53.430  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:20:53.460  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:20:53.460  1 N - [TrajectoryAuto] select * from sqlite_master
22:20:53.489  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
22:20:53.489  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:20:53.502  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 10344 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:57:14.9680000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:24:53.979  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:24:53.987  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:24:53.988  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:24:53.988  1 N - TrajectoryAuto.Web 
22:24:53.988  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:24:53.988  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:24:53.988  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:24:54.971  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:24:54.984  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:24:55.061  1 N - [TrajectoryAuto] select * from sqlite_master
22:24:55.069  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:24:55.070  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:24:55.130  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
22:24:55.136  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:24:55.147  1 N - [TrajectoryAuto]待检查数据表：Channels
22:24:55.148  1 N - [TrajectoryAuto] select * from sqlite_master
22:24:55.154  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
22:24:55.155  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:24:55.163  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:24:55.163  1 N - [TrajectoryAuto] select * from sqlite_master
22:24:55.171  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
22:24:55.171  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:24:55.191  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:24:55.192  1 N - [TrajectoryAuto] select * from sqlite_master
22:24:55.199  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
22:24:55.200  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:24:55.209  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 24480 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:02:44.6090000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:30:23.625  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:30:23.633  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:30:23.634  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:30:23.634  1 N - TrajectoryAuto.Web 
22:30:23.634  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:30:23.635  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:30:23.635  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:30:24.606  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:30:24.620  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:30:24.703  1 N - [TrajectoryAuto] select * from sqlite_master
22:30:24.712  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:30:24.713  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:30:24.767  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
22:30:24.773  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:30:24.784  1 N - [TrajectoryAuto]待检查数据表：Channels
22:30:24.785  1 N - [TrajectoryAuto] select * from sqlite_master
22:30:24.791  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
22:30:24.792  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:30:24.801  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:30:24.802  1 N - [TrajectoryAuto] select * from sqlite_master
22:30:24.821  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
22:30:24.821  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:30:24.828  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:30:24.829  1 N - [TrajectoryAuto] select * from sqlite_master
22:30:24.836  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
22:30:24.837  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:30:24.846  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23804 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:33:30.5310000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:01:09.557  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:01:09.561  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:01:09.561  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:01:09.561  1 N - TrajectoryAuto.Web 
23:01:09.561  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:01:09.561  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:01:09.561  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:01:09.655  1 N - System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.) (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 113
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

23:01:09.657  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 22856 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:33:42.6870000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:01:21.719  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:01:21.723  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:01:21.724  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:01:21.724  1 N - TrajectoryAuto.Web 
23:01:21.724  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:01:21.724  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:01:21.724  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:01:21.810  1 N - System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.) (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 113
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

23:01:21.811  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 19536 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:33:54.1090000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:01:33.124  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:01:33.132  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:01:33.133  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:01:33.133  1 N - TrajectoryAuto.Web 
23:01:33.133  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:01:33.133  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:01:33.133  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:01:33.730  1 N - System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.) (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 113
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

23:01:33.732  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 8544 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:46:58.8900000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:14:37.908  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:14:37.919  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:14:37.920  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:14:37.920  1 N - TrajectoryAuto.Web 
23:14:37.920  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:14:37.921  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:14:37.921  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:14:38.098  1 N - System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.) (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 113
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

23:14:38.100  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 15276 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:48:40.1090000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:16:19.145  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:16:19.148  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:16:19.149  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:16:19.149  1 N - TrajectoryAuto.Web 
23:16:19.149  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:16:19.149  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:16:19.149  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:16:19.239  1 N - System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.) (Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ITrajectoryService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.XCodeTrajectoryService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 113
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: TrajectoryAuto.Core.Interfaces.ICommunicationService Lifetime: Scoped ImplementationType: TrajectoryAuto.Infrastructure.Services.CommunicationService': Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'TrajectoryAuto.Infrastructure.Services.DirectUdpService' while attempting to activate 'TrajectoryAuto.Infrastructure.Services.CommunicationService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

23:16:19.240  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 3288 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:50:32.5460000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:18:11.570  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:18:11.574  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:18:11.574  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:18:11.574  1 N - TrajectoryAuto.Web 
23:18:11.574  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:18:11.574  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:18:11.574  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:18:11.950  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:18:11.996  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:18:12.065  1 N - [TrajectoryAuto] select * from sqlite_master
23:18:12.071  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:18:12.072  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:18:12.150  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:18:12.159  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:18:12.179  1 N - [TrajectoryAuto]待检查数据表：Channels
23:18:12.188  1 N - [TrajectoryAuto] select * from sqlite_master
23:18:12.203  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:18:12.206  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:18:12.225  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:18:12.227  1 N - [TrajectoryAuto] select * from sqlite_master
23:18:12.238  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:18:12.239  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:18:12.250  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:18:12.250  1 N - [TrajectoryAuto] select * from sqlite_master
23:18:12.258  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:18:12.259  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:18:12.332  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 14872 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:53:07.6250000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:20:46.655  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:20:46.659  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:20:46.660  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:20:46.660  1 N - TrajectoryAuto.Web 
23:20:46.660  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:20:46.660  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:20:46.660  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:20:47.053  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:20:47.059  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:20:47.083  1 N - [TrajectoryAuto] select * from sqlite_master
23:20:47.102  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:20:47.104  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:20:47.207  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:20:47.216  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:20:47.233  1 N - [TrajectoryAuto]待检查数据表：Channels
23:20:47.234  1 N - [TrajectoryAuto] select * from sqlite_master
23:20:47.244  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:20:47.244  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:20:47.257  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:20:47.258  1 N - [TrajectoryAuto] select * from sqlite_master
23:20:47.270  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:20:47.271  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:20:47.281  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:20:47.281  1 N - [TrajectoryAuto] select * from sqlite_master
23:20:47.289  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:20:47.289  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:20:47.361  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 20364 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:54:23.1250000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:22:02.147  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:22:02.152  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:22:02.152  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:22:02.153  1 N - TrajectoryAuto.Web 
23:22:02.153  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:22:02.153  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:22:02.153  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:22:02.715  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:22:02.729  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:22:02.768  1 N - [TrajectoryAuto] select * from sqlite_master
23:22:02.783  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:22:02.784  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:22:02.874  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:22:02.881  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:22:02.900  1 N - [TrajectoryAuto]待检查数据表：Channels
23:22:02.900  1 N - [TrajectoryAuto] select * from sqlite_master
23:22:02.911  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:22:02.911  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:22:02.925  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:22:02.926  1 N - [TrajectoryAuto] select * from sqlite_master
23:22:02.939  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:22:02.940  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:22:02.952  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:22:02.953  1 N - [TrajectoryAuto] select * from sqlite_master
23:22:02.963  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:22:02.963  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:22:03.017  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 22916 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:04:17.0310000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:31:56.059  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:31:56.064  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:31:56.065  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:31:56.065  1 N - TrajectoryAuto.Web 
23:31:56.065  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:31:56.065  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:31:56.065  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:31:56.519  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:31:56.525  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:31:56.544  1 N - [TrajectoryAuto] select * from sqlite_master
23:31:56.554  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:31:56.554  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:31:56.622  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:31:56.630  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:31:56.643  1 N - [TrajectoryAuto]待检查数据表：Channels
23:31:56.644  1 N - [TrajectoryAuto] select * from sqlite_master
23:31:56.651  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:31:56.652  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:31:56.662  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:31:56.662  1 N - [TrajectoryAuto] select * from sqlite_master
23:31:56.669  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:31:56.670  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:31:56.679  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:31:56.679  1 N - [TrajectoryAuto] select * from sqlite_master
23:31:56.686  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:31:56.686  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:31:56.728  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 6940 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:08:29.3430000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:36:08.366  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:36:08.370  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:36:08.371  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:36:08.371  1 N - TrajectoryAuto.Web 
23:36:08.371  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:36:08.371  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:36:08.371  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:36:09.270  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:36:09.288  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:36:09.381  1 N - [TrajectoryAuto] select * from sqlite_master
23:36:09.390  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:36:09.390  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:36:09.464  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:36:09.470  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:36:09.482  1 N - [TrajectoryAuto]待检查数据表：Channels
23:36:09.482  1 N - [TrajectoryAuto] select * from sqlite_master
23:36:09.490  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:36:09.491  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:36:09.502  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:36:09.502  1 N - [TrajectoryAuto] select * from sqlite_master
23:36:09.513  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:36:09.514  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:36:09.536  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:36:09.536  1 N - [TrajectoryAuto] select * from sqlite_master
23:36:09.543  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:36:09.544  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:36:09.588  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9764 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:13:14.4680000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:40:53.486  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:40:53.491  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:40:53.491  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:40:53.492  1 N - TrajectoryAuto.Web 
23:40:53.492  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:40:53.492  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:40:53.492  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:40:54.370  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:40:54.386  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:40:54.508  1 N - [TrajectoryAuto] select * from sqlite_master
23:40:54.517  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:40:54.518  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:40:54.589  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:40:54.594  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:40:54.607  1 N - [TrajectoryAuto]待检查数据表：Channels
23:40:54.607  1 N - [TrajectoryAuto] select * from sqlite_master
23:40:54.614  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:40:54.615  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:40:54.635  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:40:54.636  1 N - [TrajectoryAuto] select * from sqlite_master
23:40:54.644  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:40:54.645  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:40:54.653  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:40:54.654  1 N - [TrajectoryAuto] select * from sqlite_master
23:40:54.661  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:40:54.662  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:40:54.707  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23568 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:17:02.3280000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:44:41.350  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:44:41.354  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:44:41.355  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:44:41.355  1 N - TrajectoryAuto.Web 
23:44:41.355  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:44:41.355  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:44:41.355  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:44:41.768  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:44:41.775  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:44:41.796  1 N - [TrajectoryAuto] select * from sqlite_master
23:44:41.804  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:44:41.805  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:44:41.868  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:44:41.874  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:44:41.885  1 N - [TrajectoryAuto]待检查数据表：Channels
23:44:41.886  1 N - [TrajectoryAuto] select * from sqlite_master
23:44:41.896  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:44:41.897  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:44:41.906  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:44:41.907  1 N - [TrajectoryAuto] select * from sqlite_master
23:44:41.914  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:44:41.915  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:44:41.923  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:44:41.923  1 N - [TrajectoryAuto] select * from sqlite_master
23:44:41.930  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:44:41.931  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:44:41.981  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 22944 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:18:38.9210000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:46:17.955  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:46:17.959  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:46:17.959  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:46:17.959  1 N - TrajectoryAuto.Web 
23:46:17.959  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:46:17.959  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:46:17.959  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:46:18.356  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:46:18.362  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:46:18.386  1 N - [TrajectoryAuto] select * from sqlite_master
23:46:18.393  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:46:18.394  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:46:18.452  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:46:18.457  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:46:18.470  1 N - [TrajectoryAuto]待检查数据表：Channels
23:46:18.470  1 N - [TrajectoryAuto] select * from sqlite_master
23:46:18.478  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:46:18.479  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:46:18.487  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:46:18.488  1 N - [TrajectoryAuto] select * from sqlite_master
23:46:18.495  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:46:18.496  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:46:18.503  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:46:18.504  1 N - [TrajectoryAuto] select * from sqlite_master
23:46:18.511  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:46:18.512  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:46:18.521  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9652 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:22:45.0930000
#Date: 2025-08-07
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:50:24.120  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:50:24.124  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:50:24.124  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:50:24.124  1 N - TrajectoryAuto.Web 
23:50:24.124  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:50:24.124  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:50:24.124  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:50:24.509  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:50:24.515  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:50:24.533  1 N - [TrajectoryAuto] select * from sqlite_master
23:50:24.541  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:50:24.541  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:50:24.594  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:50:24.599  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:50:24.610  1 N - [TrajectoryAuto]待检查数据表：Channels
23:50:24.611  1 N - [TrajectoryAuto] select * from sqlite_master
23:50:24.618  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:50:24.619  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:50:24.630  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:50:24.631  1 N - [TrajectoryAuto] select * from sqlite_master
23:50:24.638  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
23:50:24.639  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:50:24.652  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:50:24.653  1 N - [TrajectoryAuto] select * from sqlite_master
23:50:24.662  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
23:50:24.662  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:50:24.712  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
