<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="NewLife.XCode" Version="11.10.2024.301" />
    <PackageReference Include="NewLife.Core" Version="10.10.2024.301" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TrajectoryAuto.Core\TrajectoryAuto.Core.csproj" />
  </ItemGroup>

</Project>
