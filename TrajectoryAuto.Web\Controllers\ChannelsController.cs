using Microsoft.AspNetCore.Mvc;
using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Interfaces;

namespace TrajectoryAuto.Web.Controllers;

/// <summary>
/// 通道控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ChannelsController : ControllerBase
{
    private readonly IChannelService _channelService;

    public ChannelsController(IChannelService channelService)
    {
        _channelService = channelService;
    }

    /// <summary>
    /// 获取场景的所有通道
    /// </summary>
    /// <param name="sceneId"></param>
    /// <returns></returns>
    [HttpGet("scene/{sceneId}")]
    public async Task<ActionResult<List<Channel>>> GetChannelsByScene(Guid sceneId)
    {
        var channels = await _channelService.GetChannelsBySceneIdAsync(sceneId);
        return Ok(channels);
    }

    /// <summary>
    /// 根据ID获取通道
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<Channel>> GetChannel(Guid id)
    {
        var channel = await _channelService.GetChannelByIdAsync(id);
        if (channel == null)
            return NotFound("通道不存在");

        return Ok(channel);
    }

    /// <summary>
    /// 创建通道
    /// </summary>
    /// <param name="channel"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<Channel>> CreateChannel([FromBody] Channel channel)
    {
        try
        {
            var createdChannel = await _channelService.CreateChannelAsync(channel);
            return CreatedAtAction(nameof(GetChannel), new { id = createdChannel.Id }, createdChannel);
        }
        catch (Exception ex)
        {
            return BadRequest($"创建通道失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新通道
    /// </summary>
    /// <param name="id"></param>
    /// <param name="channel"></param>
    /// <returns></returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<Channel>> UpdateChannel(Guid id, [FromBody] Channel channel)
    {
        if (id != channel.Id)
            return BadRequest("通道ID不匹配");

        try
        {
            var updatedChannel = await _channelService.UpdateChannelAsync(channel);
            return Ok(updatedChannel);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return BadRequest($"更新通道失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除通道
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteChannel(Guid id)
    {
        var result = await _channelService.DeleteChannelAsync(id);
        if (!result)
            return NotFound("通道不存在");

        return NoContent();
    }

    /// <summary>
    /// 设置活动通道
    /// </summary>
    /// <param name="channelId"></param>
    /// <param name="sceneId"></param>
    /// <returns></returns>
    [HttpPost("{channelId}/activate")]
    public async Task<ActionResult> SetActiveChannel(Guid channelId, [FromQuery] Guid sceneId)
    {
        var result = await _channelService.SetActiveChannelAsync(channelId, sceneId);
        if (!result)
            return BadRequest("设置活动通道失败");

        return Ok(new { message = "活动通道设置成功" });
    }
}