using Microsoft.AspNetCore.Mvc;
using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Interfaces;

namespace TrajectoryAuto.Web.Controllers;

/// <summary>
/// 轨迹控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TrajectoriesController : ControllerBase
{
    private readonly ITrajectoryService _trajectoryService;

    public TrajectoriesController(ITrajectoryService trajectoryService)
    {
        _trajectoryService = trajectoryService;
    }

    /// <summary>
    /// 获取通道的所有轨迹
    /// </summary>
    /// <param name="channelId"></param>
    /// <returns></returns>
    [HttpGet("channel/{channelId}")]
    public async Task<ActionResult<List<Trajectory>>> GetTrajectoriesByChannel(Guid channelId)
    {
        var trajectories = await _trajectoryService.GetTrajectoriesByChannelIdAsync(channelId);
        return Ok(trajectories);
    }

    /// <summary>
    /// 根据ID获取轨迹
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<Trajectory>> GetTrajectory(Guid id)
    {
        var trajectory = await _trajectoryService.GetTrajectoryByIdAsync(id);
        if (trajectory == null)
            return NotFound("轨迹不存在");

        return Ok(trajectory);
    }

    /// <summary>
    /// 创建轨迹
    /// </summary>
    /// <param name="trajectory"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<Trajectory>> CreateTrajectory([FromBody] Trajectory trajectory)
    {
        try
        {
            var createdTrajectory = await _trajectoryService.CreateTrajectoryAsync(trajectory);
            return CreatedAtAction(nameof(GetTrajectory), new { id = createdTrajectory.Id }, createdTrajectory);
        }
        catch (Exception ex)
        {
            return BadRequest($"创建轨迹失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新轨迹
    /// </summary>
    /// <param name="id"></param>
    /// <param name="trajectory"></param>
    /// <returns></returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<Trajectory>> UpdateTrajectory(Guid id, [FromBody] Trajectory trajectory)
    {
        if (id != trajectory.Id)
            return BadRequest("轨迹ID不匹配");

        try
        {
            var updatedTrajectory = await _trajectoryService.UpdateTrajectoryAsync(trajectory);
            return Ok(updatedTrajectory);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return BadRequest($"更新轨迹失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除轨迹
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteTrajectory(Guid id)
    {
        var result = await _trajectoryService.DeleteTrajectoryAsync(id);
        if (!result)
            return NotFound("轨迹不存在");

        return NoContent();
    }

    /// <summary>
    /// 清除通道的所有轨迹
    /// </summary>
    /// <param name="channelId"></param>
    /// <returns></returns>
    [HttpDelete("channel/{channelId}/clear")]
    public async Task<ActionResult> ClearChannelTrajectories(Guid channelId)
    {
        var result = await _trajectoryService.ClearChannelTrajectoriesAsync(channelId);
        if (!result)
            return BadRequest("清除轨迹失败");

        return Ok(new { message = "通道轨迹已清除" });
    }

    /// <summary>
    /// 播放轨迹
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPost("{id}/play")]
    public async Task<ActionResult> PlayTrajectory(Guid id)
    {
        var result = await _trajectoryService.PlayTrajectoryAsync(id);
        if (!result)
            return BadRequest("播放轨迹失败");

        return Ok(new { message = "轨迹播放已开始" });
    }

    /// <summary>
    /// 停止轨迹播放
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPost("{id}/stop")]
    public async Task<ActionResult> StopTrajectory(Guid id)
    {
        var result = await _trajectoryService.StopTrajectoryAsync(id);
        if (!result)
            return BadRequest("停止轨迹失败");

        return Ok(new { message = "轨迹播放已停止" });
    }

    /// <summary>
    /// 获取轨迹播放状态
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id}/status")]
    public async Task<ActionResult> GetPlaybackStatus(Guid id)
    {
        var status = await _trajectoryService.GetPlaybackStatusAsync(id);
        return Ok(new { trajectoryId = id, status });
    }
}