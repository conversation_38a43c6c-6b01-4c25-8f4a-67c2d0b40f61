@{
    ViewData["Title"] = "舞台轨迹自动化系统";
}

<div class="app-main">
    <!-- 场景管理面板 -->
    <aside class="scene-panel">
        <div class="panel-header">
            <h2>场景管理</h2>
            <button id="createSceneBtn" class="btn btn-small">新建场景</button>
        </div>
        <div class="scene-list" id="sceneList">
            <!-- 场景列表将通过AJAX动态加载 -->
        </div>
        <div class="panel-actions" id="sceneActions" style="display: none;">
            <button class="btn btn-small btn-edit" id="editSceneBtn" title="编辑场景"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-small btn-delete" id="deleteSceneBtn" title="删除场景"><i class="fas fa-trash"></i> 删除</button>
        </div>
    </aside>

    <!-- 通道管理面板 -->
    <aside class="channel-panel">
        <div class="panel-header">
            <h2>通道管理</h2>
            <button id="createChannelBtn" class="btn btn-small">新建通道</button>
        </div>
        <div class="channel-list" id="channelList">
            <!-- 通道列表将通过AJAX动态加载 -->
        </div>
        <div class="panel-actions" id="channelActions" style="display: none;">
            <button class="btn btn-small btn-edit" id="editChannelBtn" title="编辑通道"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-small btn-delete" id="deleteChannelBtn" title="删除通道"><i class="fas fa-trash"></i> 删除</button>
        </div>
    </aside>

    <!-- 画布区域 -->
    <section class="canvas-panel">
        <div class="canvas-container">
            <div class="canvas-toolbar">
    <div class="tool-group">
        <button id="selectTool" class="tool-btn active" data-tool="select" title="选择工具">
            <i class="fas fa-mouse-pointer me-1"></i><span>选择</span>
        </button>
        <button id="circleTool" class="tool-btn" data-tool="circle" title="圆形轨迹">
            <i class="fas fa-circle me-1"></i><span>圆形</span>
        </button>
        <button id="rectangleTool" class="tool-btn" data-tool="rectangle" title="矩形轨迹">
            <i class="fas fa-square me-1"></i><span>矩形</span>
        </button>
        <button id="polygonTool" class="tool-btn" data-tool="polygon" title="多边形轨迹">
            <i class="fas fa-draw-polygon me-1"></i><span>多边形</span>
        </button>
        <button id="freeDrawTool" class="tool-btn" data-tool="freedraw" title="自由绘制">
            <i class="fas fa-pencil-alt me-1"></i><span>自由绘制</span>
        </button>
        <button id="realTimeTool" class="tool-btn" data-tool="realtime" title="实时轨迹">
            <i class="fas fa-satellite-dish me-1"></i><span>实时</span>
        </button>
    </div>
    <div class="trajectory-actions" style="display: none;" id="trajectoryActions">
        <button id="editTrajectoryBtn" class="btn btn-small btn-edit" title="编辑轨迹"><i class="fas fa-edit me-1"></i>编辑</button>
        <button id="deleteTrajectoryBtn" class="btn btn-small btn-delete" title="删除轨迹"><i class="fas fa-trash me-1"></i>删除</button>
    </div>
    <div class="control-group">
        <span class="control-label">通道控制：</span>
        <button id="playBtn" class="btn btn-sm btn-success"><i class="fas fa-play me-1"></i>播放</button>
        <button id="stopBtn" class="btn btn-sm btn-danger"><i class="fas fa-stop me-1"></i>停止</button>
        <button id="clearBtn" class="btn btn-sm btn-warning"><i class="fas fa-trash-alt me-1"></i>清除</button>
    </div>
    <div class="control-group">
        <span class="control-label">场景控制：</span>
        <button id="playSceneBtn" class="btn btn-sm btn-success"><i class="fas fa-play me-1"></i>播放</button>
        <button id="stopSceneBtn" class="btn btn-sm btn-danger"><i class="fas fa-stop me-1"></i>停止</button>
    </div>
</div>

            <div class="canvas-wrapper">
                <canvas id="trajectoryCanvas" width="800" height="600" unselectable="on" onselectstart="return false;" style="-webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; -webkit-tap-highlight-color: transparent;"></canvas>
                <div id="coordinateDisplay" class="coordinate-display">坐标: (0, 0)</div>
            </div>
        </div>
    </section>
</div>

<!-- 场景模态对话框 -->
<div id="sceneModal" class="modal">
    <div class="modal-content scene-modal">
        <div class="modal-header">
            <h3 id="sceneModalTitle"><i class="fas fa-layer-group me-2"></i>新建场景</h3>
            <span class="close" id="sceneModalClose">&times;</span>
        </div>
        <div class="modal-body">
            <form id="sceneForm" class="scene-form">
                @Html.AntiForgeryToken()
                <div class="form-row">
                    <div class="form-group">
                        <label for="sceneName">场景名称</label>
                        <input type="text" id="sceneName" name="sceneName" class="form-control" required>
                    </div> 
                </div>
                <div class="form-row"> 
                    <div class="form-group">
                        <label for="sceneWidth">宽度(px)</label>
                        <input type="number" id="sceneWidth" name="sceneWidth" class="form-control" value="800" min="100" max="2000">
                    </div>
                    <div class="form-group">
                        <label for="sceneHeight">高度(px)</label>
                        <input type="number" id="sceneHeight" name="sceneHeight" class="form-control" value="600" min="100" max="1500">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="originX">原点X</label>
                        <input type="number" id="originX" name="originX" class="form-control" value="0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="originY">原点Y</label>
                        <input type="number" id="originY" name="originY" class="form-control" value="0" step="0.1">
                    </div> 
                </div>
                <div class="form-row"> 
                    <div class="form-group">
                        <label for="scaleX">X轴缩放</label>
                        <input type="number" id="scaleX" name="scaleX" class="form-control" value="1.0" step="0.1" min="0.1">
                    </div>
                    <div class="form-group">
                        <label for="scaleY">Y轴缩放</label>
                        <input type="number" id="scaleY" name="scaleY" class="form-control" value="1.0" step="0.1" min="0.1">
                    </div>
                </div>
                <div class="form-row"> 
                    <div class="form-group">
                        <label for="invertY">Y轴反转</label>
                        <div class="form-check">
                            <input type="checkbox" id="invertY" name="invertY" class="form-check-input">
                            <label class="form-check-label" for="invertY">启用</label>
                        </div>
                    </div> 
                </div>
                <div class="form-row"> 
                    <div class="form-group">
                        <label for="backgroundImage">背景图片</label>
                        <input type="file" id="backgroundImage" name="backgroundImage" class="form-control" accept="image/*">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="serverIp">服务器IP</label>
                        <input type="text" id="serverIp" name="serverIp" class="form-control" value="127.0.0.1">
                    </div>
                    <div class="form-group">
                        <label for="serverPort">服务器端口</label>
                        <input type="number" id="serverPort" name="serverPort" class="form-control" value="8080" min="1" max="65535">
                    </div> 
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="sceneCancelBtn"><i class="fas fa-times me-1"></i>取消</button>
            <button type="button" class="btn btn-primary" id="sceneSaveBtn"><i class="fas fa-save me-1"></i>保存</button>
        </div>
    </div>
</div>

<!-- 通道模态对话框 -->
<div id="channelModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="channelModalTitle"><i class="fas fa-project-diagram me-2"></i>新建通道</h3>
            <span class="close" id="channelModalClose">&times;</span>
        </div>
        <div class="modal-body">
            <form id="channelForm">
                @Html.AntiForgeryToken() 
                
                <div class="channel-form-row">
                    <div class="channel-form-group">
                        <label for="channelName">通道名称:</label>
                        <input type="text" id="channelName" name="channelName" class="form-control" required>
                    </div>
                    <div class="channel-form-group">
                        <label for="channelNumber">通道编号:</label>
                        <input type="number" id="channelNumber" name="channelNumber" class="form-control" min="1" max="999" required>
                    </div>
                </div>
                
                <div class="channel-form-row">
                    <div class="channel-form-group">
                        <label for="Address">通道地址:</label>
                        <input type="text" id="Address" name="Address" class="form-control"  required>
                    </div>
                </div>
                
                <div class="channel-form-row">
                    <div class="channel-form-group">
                        <label for="channelIp">IP地址:</label>
                        <input type="text" id="channelIp" name="channelIp" class="form-control" value="127.0.0.1">
                    </div>
                    <div class="channel-form-group">
                        <label for="channelPort">端口:</label>
                        <input type="number" id="channelPort" name="channelPort" class="form-control" value="8080" min="1" max="65535">
                    </div>
                </div>
                
                <div class="channel-form-row">
                    <div class="channel-form-group">
                        <label for="UseZAxis">启用Z轴:</label>
                        <div class="form-check">
                            <input type="checkbox" id="UseZAxis" name="UseZAxis" class="form-check-input">
                        </div>
                    </div>
                </div>
                
                <div class="channel-form-row">
                    <div class="channel-form-group">
                        <label for="ZAxisMin">Z轴范围:</label>
                        <div class="z-axis-range">
                            <input type="number" id="ZAxisMin" name="ZAxisMin" class="form-control" value="0" step="0.1" placeholder="最小值">
                            <span>-</span>
                            <input type="number" id="ZAxisMax" name="ZAxisMax" class="form-control" value="0" step="0.1" placeholder="最大值">
                        </div>
                    </div>
                </div>
                
                <div class="channel-form-row">
                    <div class="channel-form-group">
                        <label for="channelColor">颜色:</label>
                        <input type="color" id="channelColor" name="channelColor" value="#FF0000">
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="channelCancelBtn"><i class="fas fa-times me-1"></i>取消</button>
            <button type="button" class="btn btn-primary" id="channelSaveBtn"><i class="fas fa-save me-1"></i>保存</button>
        </div>
    </div>
</div>

<!-- 轨迹设置模态对话框 -->
<div id="trajectoryModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="trajectoryModalTitle"><i class="fas fa-route me-2"></i>轨迹设置</h3>
            <span class="close" id="trajectoryModalClose">&times;</span>
        </div>
        <div class="modal-body">
            <form id="trajectoryForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="modalTrajectoryName">轨迹名称:</label>
                        <input type="text" id="modalTrajectoryName" name="modalTrajectoryName" class="form-control">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="modalTrajectoryDuration">持续时间(秒):</label>
                        <input type="number" id="modalTrajectoryDuration" name="modalTrajectoryDuration" class="form-control" min="1" max="60" value="10">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="modalTrajectoryPointCount">点位数量:</label>
                        <input type="number" id="modalTrajectoryPointCount" name="modalTrajectoryPointCount" class="form-control" min="2" max="1000" value="300">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="modalTrajectoryLoop">循环:</label>
                        <div class="form-check">
                            <input type="checkbox" id="modalTrajectoryLoop" name="modalTrajectoryLoop" class="form-check-input">
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="modalTrajectoryReverse">逆向播放:</label>
                        <div class="form-check">
                            <input type="checkbox" id="modalTrajectoryReverse" name="modalTrajectoryReverse" class="form-check-input">
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer"> 
            <button type="button" class="btn btn-secondary" id="trajectoryCancelBtn"><i class="fas fa-times me-1"></i>取消</button>
            <button type="button" class="btn btn-primary" id="trajectorySaveBtn"><i class="fas fa-save me-1"></i>保存</button>
        </div>
    </div>
</div>

@section Scripts {
    <!-- 在 body 底部添加这些脚本 -->
    <script src="~/js/canvas.js"></script>
    <script src="~/js/sceneManager.js"></script>
    <script src="~/js/channelManager.js"></script>
    <script src="~/js/uiManager.js"></script>
    <script src="~/js/trajectoryManager.js"></script>
    <script src="~/js/playbackManager.js"></script>
    <script src="~/js/app.js"></script>
}
