# 多场景并发播放API使用示例

## 功能概述

现在系统支持多场景同时播放，每个场景可以独立控制播放状态，并且可以单独控制通道级别的播放。

## 核心特性

1. **多场景并发播放** - 不同场景可以同时播放，互不干扰
2. **智能重复请求处理** - 已在播放的场景/通道会忽略重复的播放请求
3. **层次化控制** - 支持场景级别和通道级别的独立停止
4. **状态查询** - 提供详细的播放状态查询接口

## API端点说明

### 播放控制API

#### 1. 播放场景下的所有轨迹
```javascript
// POST /api/trajectoryplayback/play-scene
const playSceneResponse = await fetch('/api/trajectoryplayback/play-scene', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ sceneId: "your-scene-guid" })
});

// 响应示例
{
    "success": true,
    "message": "场景 xxx 的 5 个轨迹开始播放",
    "trajectoryCount": 5,
    "channelCount": 2,
    "isAlreadyPlaying": false  // 如果场景已在播放则为true
}
```

#### 2. 播放通道下的所有轨迹
```javascript
// POST /api/trajectoryplayback/play-channel
const playChannelResponse = await fetch('/api/trajectoryplayback/play-channel', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ channelId: "your-channel-guid" })
});

// 响应示例
{
    "success": true,
    "message": "通道 xxx 的 3 个轨迹开始播放",
    "trajectoryCount": 3,
    "sceneId": "scene-guid",
    "isAlreadyPlaying": false
}
```

### 停止控制API

#### 3. 停止场景播放
```javascript
// POST /api/trajectoryplayback/stop-scene/{sceneId}
const stopSceneResponse = await fetch(`/api/trajectoryplayback/stop-scene/${sceneId}`, {
    method: 'POST'
});

// 响应示例
{
    "success": true,
    "message": "已停止场景 xxx 的播放",
    "stoppedTrajectoryCount": 5,
    "stoppedChannelCount": 2,
    "wasPlaying": true
}
```

#### 4. 停止通道播放
```javascript
// POST /api/trajectoryplayback/stop-channel/{channelId}
const stopChannelResponse = await fetch(`/api/trajectoryplayback/stop-channel/${channelId}`, {
    method: 'POST'
});

// 响应示例
{
    "success": true,
    "message": "已停止通道 xxx 的播放",
    "stoppedTrajectoryCount": 3,
    "wasPlaying": true
}
```

### 状态查询API

#### 5. 获取全局播放状态
```javascript
// GET /api/trajectoryplayback/status
const statusResponse = await fetch('/api/trajectoryplayback/status');
const status = await statusResponse.json();

// 响应示例
{
    "success": true,
    "isGlobalStopRequested": false,
    "activeSceneCount": 2,
    "totalActiveTrajectories": 8,
    "scenes": [
        {
            "sceneId": "scene1-guid",
            "isActive": true,
            "channelCount": 2,
            "channels": [
                {
                    "channelId": "channel1-guid",
                    "isActive": true,
                    "trajectoryCount": 3
                }
            ]
        }
    ]
}
```

#### 6. 获取场景播放状态
```javascript
// GET /api/trajectoryplayback/status/scene/{sceneId}
const sceneStatusResponse = await fetch(`/api/trajectoryplayback/status/scene/${sceneId}`);
const sceneStatus = await sceneStatusResponse.json();

// 响应示例
{
    "success": true,
    "sceneId": "scene-guid",
    "isActive": true,
    "isStopRequested": false,
    "activeChannelCount": 2,
    "activeChannels": ["channel1-guid", "channel2-guid"]
}
```

#### 7. 获取通道播放状态
```javascript
// GET /api/trajectoryplayback/status/channel/{channelId}
const channelStatusResponse = await fetch(`/api/trajectoryplayback/status/channel/${channelId}`);
const channelStatus = await channelStatusResponse.json();

// 响应示例
{
    "success": true,
    "channelId": "channel-guid",
    "isActive": true,
    "isStopRequested": false
}
```

## 前端集成建议

### 1. 场景播放按钮逻辑
```javascript
async function playScene(sceneId) {
    try {
        // 先检查场景状态
        const statusResponse = await fetch(`/api/trajectoryplayback/status/scene/${sceneId}`);
        const status = await statusResponse.json();
        
        if (status.isActive) {
            console.log('场景已在播放中');
            return;
        }
        
        // 开始播放场景
        const playResponse = await fetch('/api/trajectoryplayback/play-scene', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sceneId: sceneId })
        });
        
        const result = await playResponse.json();
        if (result.success) {
            console.log(`场景播放开始: ${result.message}`);
            updateScenePlayButton(sceneId, true); // 更新按钮状态
        }
    } catch (error) {
        console.error('播放场景失败:', error);
    }
}

async function stopScene(sceneId) {
    try {
        const response = await fetch(`/api/trajectoryplayback/stop-scene/${sceneId}`, {
            method: 'POST'
        });
        
        const result = await response.json();
        if (result.success) {
            console.log(`场景播放停止: ${result.message}`);
            updateScenePlayButton(sceneId, false); // 更新按钮状态
        }
    } catch (error) {
        console.error('停止场景失败:', error);
    }
}
```

### 2. 通道播放按钮逻辑
```javascript
async function playChannel(channelId) {
    try {
        const response = await fetch('/api/trajectoryplayback/play-channel', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ channelId: channelId })
        });
        
        const result = await response.json();
        if (result.success && !result.isAlreadyPlaying) {
            console.log(`通道播放开始: ${result.message}`);
            updateChannelPlayButton(channelId, true);
        }
    } catch (error) {
        console.error('播放通道失败:', error);
    }
}

async function stopChannel(channelId) {
    try {
        const response = await fetch(`/api/trajectoryplayback/stop-channel/${channelId}`, {
            method: 'POST'
        });
        
        const result = await response.json();
        if (result.success) {
            console.log(`通道播放停止: ${result.message}`);
            updateChannelPlayButton(channelId, false);
        }
    } catch (error) {
        console.error('停止通道失败:', error);
    }
}
```

### 3. 状态监控
```javascript
// 定期检查播放状态
setInterval(async () => {
    try {
        const response = await fetch('/api/trajectoryplayback/status');
        const status = await response.json();
        
        if (status.success) {
            updateGlobalStatus(status);
            
            // 更新各场景的按钮状态
            status.scenes.forEach(scene => {
                updateScenePlayButton(scene.sceneId, scene.isActive);
                
                // 更新各通道的按钮状态
                scene.channels.forEach(channel => {
                    updateChannelPlayButton(channel.channelId, channel.isActive);
                });
            });
        }
    } catch (error) {
        console.error('获取状态失败:', error);
    }
}, 2000); // 每2秒检查一次
```

## 重要说明

1. **并发播放**: 不同场景可以同时播放，不会相互影响
2. **重复请求**: 对已在播放的场景/通道发送播放请求会被忽略，返回`isAlreadyPlaying: true`
3. **层次化停止**: 停止场景会自动停止该场景下的所有通道和轨迹
4. **状态一致性**: 建议定期查询状态以保持前端按钮状态与后端一致
5. **错误处理**: 所有API都有完整的错误处理，建议在前端也添加相应的错误处理逻辑

## 兼容性

现有的API端点（如`/api/trajectoryplayback/play`、`/api/trajectoryplayback/stop-all`）仍然可以正常使用，新功能完全向后兼容。
