<?xml version="1.0" encoding="utf-8"?>
<XCode>
  <!--调试-->
  <Debug>true</Debug>
  <!--输出SQL。是否输出SQL语句，默认启用-->
  <ShowSQL>true</ShowSQL>
  <!--SQL目录。设置SQL输出的单独目录，默认为空，SQL输出到当前日志中。生产环境建议输出到站点外单独的SqlLog目录-->
  <SQLPath></SQLPath>
  <!--SQL执行时间。跟踪SQL执行时间，大于该阀值将输出日志，默认1000毫秒-->
  <TraceSQLTime>1000</TraceSQLTime>
  <!--SQL最大长度。输出日志时的SQL最大长度，超长截断，默认4096，不截断用0-->
  <SQLMaxLength>4096</SQLMaxLength>
  <!--参数化添删改查。默认关闭-->
  <UseParameter>false</UseParameter>
  <!--批大小。用于批量操作数据，抽取、删除、备份、恢复，默认5000-->
  <BatchSize>5000</BatchSize>
  <!--命令超时。查询执行超时时间，默认0秒不限制-->
  <CommandTimeout>0</CommandTimeout>
  <!--失败重试。执行命令超时后的重试次数，默认0不重试-->
  <RetryOnFailure>0</RetryOnFailure>
  <!--数据层缓存。根据sql做缓存，默认0秒-->
  <DataCacheExpire>0</DataCacheExpire>
  <!--实体缓存过期。整表缓存实体列表，默认10秒-->
  <EntityCacheExpire>10</EntityCacheExpire>
  <!--单对象缓存过期。按主键缓存实体，默认10秒-->
  <SingleCacheExpire>10</SingleCacheExpire>
  <!--扩展属性过期。扩展属性Extends缓存，默认10秒-->
  <ExtendExpire>10</ExtendExpire>
  <!--字段缓存过期。缓存表中分类型字段的分组数据，默认3600秒-->
  <FieldCacheExpire>3600</FieldCacheExpire>
  <!--反向工程。Off 关闭；ReadOnly 只读不执行；On 打开，仅新建；Full 完全，修改删除-->
  <Migration>On</Migration>
  <!--表名称、字段名大小写格式。Default 根据模型生成;Upper 全大写;Lower 全小写;Underline下划线-->
  <NameFormat>Default</NameFormat>
  <!--快速统计最小数据量。默认1000万，在对数据表进行无条件 count 时，先进行快速统计。如果快速统计的结果大于该值，则使用快速统计的结果。反之则进行 count(*) 操作获取精确统计。-->
  <FastCountMin>10000000</FastCountMin>
</XCode>