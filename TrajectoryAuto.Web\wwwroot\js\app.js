// 应用程序主要逻辑
class TrajectoryApp {
    constructor() {
        this.connection = null;
        this.currentScene = null;
        this.currentChannel = null;
        this.scenes = [];
        this.channels = [];
        this.trajectories = [];
        
        // 初始化管理器
        this.sceneManager = new SceneManager(this);
        this.channelManager = new ChannelManager(this);
        this.uiManager = new UIManager(this);
        
        // 轨迹管理器
        this.trajectoryManager = {
            loadTrajectories: async (channelId) => {
                try {
                    // 首先清除当前轨迹数据
                    this.trajectories = [];
                    
                    // 如果有画布管理器，先清除画布上的轨迹
                    if (window.canvasManager) {
                        // 清除画布上的轨迹
                        window.canvasManager.trajectories = [];
                        // 重绘画布，只保留背景
                        window.canvasManager.redrawCanvas();
                    }
                    
                    // 修正API路径，与后端控制器匹配
                    const response = await fetch(`/api/trajectories/channel/${channelId}`);
                    if (!response.ok) {
                        // 请求轨迹数据失败
                        const errorText = await response.text();
                        throw new Error(`加载轨迹失败: ${response.status}`);
                    }
                    
                    const trajectories = await response.json();
                    // 从通道加载轨迹数据
                    
                    // 更新应用的轨迹数组
                    this.trajectories = trajectories;
                    
                    // 如果有画布管理器，绘制轨迹
                    if (window.canvasManager && trajectories.length > 0) {
                        // 开始绘制轨迹
                        window.canvasManager.trajectories = trajectories;
                        window.canvasManager.redrawCanvas();
                    }
                    
                    return trajectories;
                } catch (error) {
                    console.error('加载轨迹失败:', error);
                    // 返回空数组而不是抛出错误，避免中断正常流程
                    return [];
                }
            },
            drawTrajectories: (trajectories) => {
                // 实现绘制轨迹的逻辑
                console.log('绘制轨迹:', trajectories.length, '条轨迹');
                
                // 如果有画布管理器，绘制轨迹
                if (window.canvasManager) {
                    window.canvasManager.trajectories = trajectories;
                    window.canvasManager.redrawCanvas();
                }
            },
            clearTrajectories: () => {
                console.log('清除所有轨迹');
                // 清空轨迹数组
                this.trajectories = [];
                
                // 如果有画布管理器，清除画布上的轨迹
                if (window.canvasManager) {
                    window.canvasManager.trajectories = [];
                    window.canvasManager.redrawCanvas();
                }
                
                // 重绘画布
                if (window.canvasManager) {
                    window.canvasManager.redrawCanvas();
                }
            }
        };
        this.canvasManager = null;
        
        // 初始化应用
        this.init();
        
        console.log('应用初始化完成', { 
            sceneManager: this.sceneManager, 
            channelManager: this.channelManager 
        });
    }

    async init() {
        console.log('开始初始化应用');
        this.setupEventListeners();
        this.setupSignalR();
        await this.loadScenes();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 连接按钮
        document.getElementById('connectBtn').addEventListener('click', () => {
            this.toggleConnection();
        });

        // 场景管理
        const createSceneBtn = document.getElementById('createSceneBtn');
        if (createSceneBtn) {
            console.log('找到创建场景按钮', createSceneBtn);
            createSceneBtn.addEventListener('click', (e) => {
                console.log('创建场景按钮被点击');
                e.preventDefault();
                if (this.sceneManager) {
                    console.log('this.sceneManager存在', this.sceneManager);
                    this.sceneManager.showSceneModal();
                } else {
                    console.error('this.sceneManager不存在');
                }
            });
        } else {
            console.error('找不到创建场景按钮');
        }
        
        // 确保场景模态框的保存和取消按钮事件正确绑定
        const sceneSaveBtn = document.getElementById('sceneSaveBtn');
        if (sceneSaveBtn) {
            sceneSaveBtn.addEventListener('click', () => {
                console.log('保存场景按钮被点击');
                this.sceneManager.saveScene();
            });
        }

        const sceneCancelBtn = document.getElementById('sceneCancelBtn');
        if (sceneCancelBtn) {
            sceneCancelBtn.addEventListener('click', () => {
                console.log('取消场景按钮被点击');
                this.sceneManager.hideSceneModal();
            });
        }

        const sceneModalClose = document.getElementById('sceneModalClose');
        if (sceneModalClose) {
            sceneModalClose.addEventListener('click', () => {
                console.log('关闭场景模态框按钮被点击');
                this.sceneManager.hideSceneModal();
            });
        }

        // 场景列表点击事件
        document.getElementById('sceneList').addEventListener('click', (e) => {
            const sceneItem = e.target.closest('.scene-item');
            if (sceneItem) {
                // 移除其他项的选中状态
                document.querySelectorAll('.scene-item').forEach(item => {
                    item.classList.remove('selected');
                });
                // 添加当前项的选中状态
                sceneItem.classList.add('selected');
                // 显示操作按钮
                document.getElementById('sceneActions').style.display = 'flex';
                
                // 存储当前选中的场景ID
                this.selectedSceneId = sceneItem.dataset.id;
            }
        });

        // 场景操作按钮已在uiManager.js中绑定，这里不需要重复绑定
        // 避免重复绑定导致多次请求
        // 删除场景按钮的点击事件已在uiManager.js中绑定，这里不再重复绑定
        // 注释下面的代码以避免重复调用
        /*
        document.getElementById('deleteSceneBtn').addEventListener('click', () => {
            if (this.selectedSceneId) {
                if (confirm('确定要删除这个场景吗？此操作不可恢复。')) {
                    this.deleteScene(this.selectedSceneId);
                }
            }
        });
        */

        // 注释掉通道列表的事件委托处理，因为我们已经在renderChannels中为每个通道项添加了点击事件
        // 这里的事件处理会与单独的通道项点击事件冲突
        /*
        document.getElementById('channelList').addEventListener('click', async (e) => {
            const channelItem = e.target.closest('.channel-item');
            if (channelItem) {
                // 移除其他项的选中状态
                document.querySelectorAll('.channel-item').forEach(item => {
                    item.classList.remove('selected');
                });
                // 添加当前项的选中状态
                channelItem.classList.add('selected');
                // 显示操作按钮
                document.getElementById('channelActions').style.display = 'flex';
                
                // 存储当前选中的通道ID
                this.selectedChannelId = channelItem.dataset.id;
                
                // 重要：调用channelManager的selectChannel方法来实际选择通道
                try {
                    // 查找对应的通道对象
                    const channelId = channelItem.dataset.id;
                    const channel = this.channels.find(c => c.id === channelId);
                    
                    if (channel) {
                        console.log('点击选择通道:', channel);
                        // 调用channelManager的selectChannel方法
                        await this.channelManager.selectChannel(channel);
                    } else {
                        console.error('找不到通道对象:', channelId);
                    }
                } catch (error) {
                    console.error('选择通道时出错:', error);
                }
            }
        });
        */

        // 通道操作按钮
        document.getElementById('editChannelBtn').addEventListener('click', () => {
            if (this.selectedChannelId) {
                this.editChannel(this.selectedChannelId);
            }
        });

        // 注意：删除通道按钮的事件绑定已移至 uiManager.js 中处理
        // 避免重复绑定导致删除操作执行两次



        // 通道管理
        document.getElementById('createChannelBtn').addEventListener('click', () => {
            this.showChannelModal();
        });

        // 注意：保存通道按钮的事件绑定已移至 uiManager.js 中处理
        // 避免重复绑定导致保存操作执行两次

        document.getElementById('channelCancelBtn').addEventListener('click', () => {
            this.hideChannelModal();
        });

        document.getElementById('channelModalClose').addEventListener('click', () => {
            this.hideChannelModal();
        });

        // 注意：playBtn和stopBtn的事件监听器在trajectoryManager.js中处理
        // trajectoryManager.js中的playAllTrajectories()方法会调用后台的play-channel API
        // trajectoryManager.js中的stopPlayback()方法会调用后台的stop-all API

        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearTrajectories();
        });

        // 工具选择
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 获取按钮元素，无论点击的是按钮本身、图标还是文本
                const button = e.target.closest('.tool-btn');
                if (button && button.dataset.tool) {
                    this.selectTool(button.dataset.tool);
                }
            });
        });

        // 模态框点击外部关闭
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    // 设置SignalR连接
    async setupSignalR() {
        try {
            // 使用最新版的SignalR客户端库创建连接
            this.connection = new signalR.HubConnectionBuilder()
                .withUrl("/trajectoryHub")
                .withAutomaticReconnect([0, 2000, 5000, 10000, 15000, 30000]) // 自动重连策略
                .configureLogging(signalR.LogLevel.Debug) // 设置日志级别为调试模式
                .build();
            this.connection.serverTimeoutInMilliseconds = 60000; // 60 秒
            // 添加连接状态事件处理
            this.connection.onreconnecting(error => {
                console.log("正在尝试重新连接:", error);
                this.updateConnectionStatus("正在重连...");
            });

            this.connection.onreconnected(connectionId => {
                console.log("重新连接成功:", connectionId);
                this.updateConnectionStatus("已连接");
            });

            this.connection.onclose(error => {
                console.log("连接已关闭:", error);
                this.updateConnectionStatus("连接已关闭");
                
                // 如果自动重连失败，手动尝试重连
                setTimeout(() => {
                    console.log("尝试重新建立连接...");
                    this.setupSignalR();
                }, 10000);
            });

            // 监听服务器消息
            this.connection.on("CoordinateDataSent", (data) => {
                console.log("坐标数据发送结果:", data);
                if (data && typeof data.success !== 'undefined') {
                    this.updateConnectionStatus(data.success ? "已连接" : "发送失败");
                }
            });

            this.connection.on("TrajectoryPlaybackChanged", (data) => {
                console.log("轨迹播放状态变化:", data);
                if (data && data.trajectoryId && data.status) {
                    this.updatePlaybackStatus(data.trajectoryId, data.status);
                }
            });

            this.connection.on("PlaybackStatusResponse", (data) => {
                console.log("播放状态响应:", data);
                if (data && data.trajectoryId && data.status) {
                    this.updatePlaybackStatus(data.trajectoryId, data.status);
                }
            });
            
            // 启动连接并添加详细的错误处理
            try {
                await this.connection.start();
                console.log("SignalR连接已建立");
                this.updateConnectionStatus("已连接");
            } catch (err) {
                console.error("SignalR连接失败:", err);
                // 输出更详细的错误信息
                if (err && err.message) {
                    console.error("错误消息:", err.message);
                }
                if (err && err.stack) {
                    console.error("错误堆栈:", err.stack);
                }
                
                this.updateConnectionStatus("连接失败");
                
                // 延迟重试
                setTimeout(() => {
                    console.log("尝试重新建立连接...");
                    this.setupSignalR();
                }, 5000);
            }
        } catch (outerErr) {
            console.error("SignalR初始化失败:", outerErr);
            this.updateConnectionStatus("初始化失败");
        }
    }

    // 切换连接状态
    async toggleConnection() {
        if (this.connection.state === signalR.HubConnectionState.Connected) {
            await this.connection.stop();
            this.updateConnectionStatus("未连接");
        } else {
            try {
                await this.connection.start();
                this.updateConnectionStatus("已连接");
            } catch (err) {
                console.error("连接失败:", err);
                this.updateConnectionStatus("连接失败");
            }
        }
    }

    // 更新连接状态显示
    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connectionStatus');
        statusElement.textContent = status;
        
        // 更新按钮文本
        const connectBtn = document.getElementById('connectBtn');
        connectBtn.textContent = status === "已连接" ? "断开连接" : "连接服务器";
    }

    // 加载场景列表
    async loadScenes() {
        try {
            this.scenes = await this.sceneManager.loadScenes();
        } catch (error) {
            console.error('加载场景失败:', error);
        }
    }

    // 渲染场景列表
    renderScenes() {
        // 先调用sceneManager的渲染方法
        this.sceneManager.renderScenes();
        
        // 添加点击事件
        const sceneItems = document.querySelectorAll('.scene-item');
        sceneItems.forEach(item => {
            // 移除可能存在的旧事件监听器
            const newItem = item.cloneNode(true);
            item.parentNode.replaceChild(newItem, item);
            
            newItem.addEventListener('click', (e) => {
                e.stopPropagation();
                // 如果点击的是操作按钮，不触发场景选择
                if (e.target.closest('.btn-edit, .btn-delete')) {
                    return;
                }
                const sceneId = newItem.dataset.id;
                const scene = this.scenes.find(s => s.id === sceneId);
                if (scene) {
                    this.selectScene(scene);
                }
            });
        });
    }

    // 选择场景
    async selectScene(scene) {
        if (!scene) return;
        
        this.currentScene = scene;
        this.selectedSceneId = scene.id;
        
        // 更新UI状态
        document.querySelectorAll('.scene-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        const selectedItem = document.querySelector(`[data-id="${scene.id}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
            // 显示操作按钮
            document.getElementById('sceneActions').style.display = 'flex';
        }
        
        // 调用SceneManager中的selectScene方法
        await this.sceneManager.selectScene(scene);
    }

    // 加载通道列表
    async loadChannels(sceneId) {
        try {
            const response = await fetch(`/api/channels/scene/${sceneId}`);
            this.channels = await response.json();
            this.renderChannels();
        } catch (error) {
            console.error('加载通道失败:', error);
        }
    }

    // 渲染通道列表
    renderChannels() {
        const channelList = document.getElementById('channelList');
        channelList.innerHTML = '';

        this.channels.forEach(channel => {
            const channelItem = document.createElement('div');
            channelItem.className = 'channel-item';
            channelItem.dataset.id = channel.id;
            
            if (channel.isActive) {
                channelItem.classList.add('active');
                this.currentChannel = channel;
            }

            channelItem.innerHTML = `
                <h4>${channel.name} (${channel.channelNumber})</h4>
                <p>IP: ${channel.ipAddress}:${channel.port}</p>
                <p style="color: ${channel.color}">颜色: ${channel.color}</p>
            `;

            // 使用事件委托处理点击
            channelItem.addEventListener('click', async (e) => {
                e.stopPropagation();
                // 如果点击的是操作按钮，不触发通道选择
                if (e.target.closest('.btn-edit, .btn-delete')) {
                    return;
                }
                console.log('点击选择通道:', channel.id, channel.name);
                await this.selectChannel(channel);
            });

            channelList.appendChild(channelItem);
        });
    }

    // 选择通道
    async selectChannel(channel) {
        try {
            await fetch(`/api/channels/${channel.id}/activate?sceneId=${this.currentScene.id}`, {
                method: 'POST'
            });

            this.currentChannel = channel;
            
            // 更新UI状态
            document.querySelectorAll('.channel-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            const selectedItem = document.querySelector(`[data-id="${channel.id}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
                // 显示操作按钮
                document.getElementById('channelActions').style.display = 'flex';
                this.selectedChannelId = channel.id;
            }

            // 加载通道的轨迹
            await this.loadTrajectories(channel.id);
        } catch (error) {
            console.error('选择通道失败:', error);
        }
    }

    // 加载轨迹列表
    async loadTrajectories(channelId) {
        try {
            const timestamp = new Date().getTime();
            console.log(`[${timestamp}] 开始加载通道 ${channelId} 的轨迹`);
            const response = await fetch(`/api/trajectories/channel/${channelId}`);
            
            if (!response.ok) {
                console.error(`[${timestamp}] 加载轨迹失败，状态码: ${response.status}`);
                const errorText = await response.text();
                console.error('[${timestamp}] 错误详情:', errorText);
                return [];
            }
            
            this.trajectories = await response.json();
            console.log(`[${timestamp}] 加载到 ${this.trajectories.length} 条轨迹，通道ID: ${channelId}`);
            
            // 更新画布管理器的轨迹数组
            if (window.canvasManager) {
                console.log(`[${timestamp}] 更新画布管理器的轨迹数组`);
                window.canvasManager.trajectories = [...this.trajectories];
                
                // 确保画布可见
                const canvas = document.getElementById('trajectoryCanvas');
                if (canvas) {
                    canvas.style.visibility = 'visible';
                    canvas.style.display = 'block';
                    console.log(`[${timestamp}] 确保画布可见性`);
                }
                
                // 立即重绘画布显示所有轨迹
                console.log(`[${timestamp}] 立即重绘画布显示所有轨迹`);
                window.canvasManager.redrawCanvas();
                
                // 使用多个延迟时间的重绘，确保在各种情况下都能正确显示
                const delayTimes = [50, 150, 300, 500];
                delayTimes.forEach((delay, index) => {
                    setTimeout(() => {
                        console.log(`[${timestamp}] 第${index+1}次延迟重绘画布(${delay}ms)，确保轨迹显示`);
                        window.canvasManager.redrawCanvas();
                    }, delay);
                });
            } else {
                console.error(`[${timestamp}] 画布管理器未初始化`);
            }
            
            return this.trajectories;
        } catch (error) {
            console.error('加载轨迹失败:', error);
            return [];
        }
    }

    // 显示场景模态框
    showSceneModal(scene = null) {
        this.sceneManager.showSceneModal(scene);
    }

    // 隐藏场景模态框
    hideSceneModal() {
        this.sceneManager.hideSceneModal();
    }

    // 编辑场景
    async editScene(sceneId) {
        try {
            await this.sceneManager.editScene(sceneId);
        } catch (error) {
            console.error('编辑场景出错:', error);
        }
    }
    
    // 删除场景
    async deleteScene(sceneId) {
        try {
            await this.sceneManager.deleteScene(sceneId);
            // 清除选中状态
            this.selectedSceneId = null;
            document.getElementById('sceneActions').style.display = 'none';
            // 重新加载场景列表
            await this.loadScenes();
        } catch (error) {
            console.error('删除场景出错:', error);
        }
    }
    
    // 显示场景模态框（编辑或新建）
    showSceneModal(scene = null) {
        this.sceneManager.showSceneModal(scene);
    }

    // 上传背景图片
    async uploadBackgroundImage(sceneId, file) {
        try {
            return await this.sceneManager.uploadBackgroundImage(sceneId, file);
        } catch (error) {
            console.error('上传背景图片失败:', error);
        }
    }

    // 显示通道模态框
    showChannelModal(channel = null) {
        if (!this.currentScene) {
            alert('请先选择一个场景');
            return;
        }

        const modal = document.getElementById('channelModal');
        const title = document.getElementById('channelModalTitle');
        const form = document.getElementById('channelForm');

        if (channel) {
            title.textContent = '编辑通道';
            document.getElementById('channelName').value = channel.name;
            document.getElementById('channelNumber').value = channel.channelNumber;
            document.getElementById('channelIp').value = channel.ipAddress;
            document.getElementById('channelPort').value = channel.port;
            document.getElementById('channelColor').value = channel.color;
        } else {
            title.textContent = '新建通道';
            form.reset();
            // 设置默认通道编号
            document.getElementById('channelNumber').value = this.channels.length + 1;
        }

        modal.style.display = 'block';
    }

    // 隐藏通道模态框
    hideChannelModal() {
        document.getElementById('channelModal').style.display = 'none';
    }

    // 编辑通道
    async editChannel(channelId) {
        try {
            const response = await fetch(`/api/channels/${channelId}`);
            if (!response.ok) {
                throw new Error('获取通道信息失败');
            }
            
            const channel = await response.json();
            this.showChannelModal(channel);
        } catch (error) {
            console.error('编辑通道出错:', error);
            alert('编辑通道时出错');
        }
    }
    
    // 删除通道 - 委托给channelManager处理
    async deleteChannel(channelId) {
        console.log('app.js中的deleteChannel被调用，委托给channelManager');
        
        // 直接调用channelManager的方法，避免重复逻辑
        if (this.channelManager) {
            return await this.channelManager.deleteChannel(channelId);
        } else {
            console.error('找不到channelManager实例');
            alert('系统错误：找不到通道管理器');
        }
    }
    
    // 显示通道模态框（编辑或新建）
    showChannelModal(channel = null) {
        console.log('app.js中的showChannelModal被调用，当前状态:', {
            currentScene: this.currentScene,
            sceneManagerCurrentScene: this.sceneManager?.currentScene,
            selectedSceneId: this.sceneManager?.selectedSceneId
        });
        
        // 直接调用channelManager的方法，避免重复逻辑
        if (this.channelManager) {
            this.channelManager.showChannelModal(channel);
            return;
        }
        
        // 如果channelManager不存在，才使用这里的逻辑
        if (!this.currentScene && !this.sceneManager?.currentScene && !this.sceneManager?.selectedSceneId) {
            alert('请先选择一个场景');
            return;
        }
        
        const modal = document.getElementById('channelModal');
        const form = document.getElementById('channelForm');
        const modalTitle = document.getElementById('channelModalTitle');
        
        if (!modal || !form || !modalTitle) {
            console.error('无法找到通道模态框元素');
            return;
        }
        
        if (channel) {
            // 编辑模式
            modalTitle.innerHTML = '<i class="fas fa-edit me-2"></i>编辑通道';
            
            // 使用 getElementById 设置值，避免 elements 访问问题
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'id';
            idInput.value = channel.id;
            form.appendChild(idInput);
            
            document.getElementById('channelName').value = channel.name || '';
            document.getElementById('channelNumber').value = channel.channelNumber || '';
            document.getElementById('channelIp').value = channel.ipAddress || '127.0.0.1';
            document.getElementById('channelPort').value = channel.port || '8080';
            document.getElementById('channelColor').value = channel.color || '#FF0000';
        } else {
            // 新建模式
            modalTitle.innerHTML = '<i class="fas fa-plus-circle me-2"></i>新建通道';
            form.reset();
            
            // 设置默认值
            document.getElementById('channelNumber').value = this.channels.length + 1;
            document.getElementById('channelIp').value = '127.0.0.1';
            document.getElementById('channelPort').value = '8080';
            document.getElementById('channelColor').value = '#FF0000';
            
            // 移除可能存在的隐藏ID字段
            const existingIdInput = form.querySelector('input[name="id"]');
            if (existingIdInput) {
                form.removeChild(existingIdInput);
            }
        }
        
        modal.style.display = 'flex';
    }

    // 保存通道 - 委托给channelManager处理
    async saveChannel() {
        console.log('app.js中的saveChannel被调用，委托给channelManager');
        
        // 直接调用channelManager的方法，避免重复逻辑
        if (this.channelManager) {
            return await this.channelManager.saveChannel();
        } else {
            console.error('找不到channelManager实例');
            alert('系统错误：找不到通道管理器');
        }
    }

    // 编辑通道
    async editChannel(channelId) {
        try {
            const response = await fetch(`/api/channels/${channelId}`);
            if (!response.ok) {
                throw new Error('获取通道信息失败');
            }
            
            const channel = await response.json();
            this.showChannelModal(channel);
        } catch (error) {
            console.error('编辑通道出错:', error);
            alert('编辑通道时出错');
        }
    }

    // 选择工具
    selectTool(tool) {
        console.log('选择工具:', tool);
        
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const toolBtn = document.querySelector(`[data-tool="${tool}"]`);
        if (toolBtn) {
            toolBtn.classList.add('active');
        } else {
            console.warn(`未找到工具按钮: [data-tool="${tool}"]`);
        }
        
        // 尝试获取canvasManager实例
        if (this.canvasManager) {
            console.log('使用this.canvasManager设置工具:', tool);
            this.canvasManager.setCurrentTool(tool);
            return;
        }
        
        if (window.canvasManager) {
            console.log('使用window.canvasManager设置工具:', tool);
            window.canvasManager.setCurrentTool(tool);
            // 更新引用
            this.canvasManager = window.canvasManager;
            return;
        }
        
        console.error('canvasManager未初始化，无法设置当前工具');
        
        // 尝试延迟设置工具
        setTimeout(() => {
            if (window.canvasManager) {
                console.log('延迟后找到canvasManager，设置工具:', tool);
                window.canvasManager.setCurrentTool(tool);
                this.canvasManager = window.canvasManager;
            } else {
                console.error('延迟后仍未找到canvasManager');
                
                // 尝试创建新的canvasManager实例
                const canvas = document.getElementById('trajectoryCanvas');
                if (canvas && window.CanvasManager) {
                    console.log('尝试创建新的CanvasManager实例');
                    try {
                        const manager = new window.CanvasManager('trajectoryCanvas');
                        window.canvasManager = manager;
                        this.canvasManager = manager;
                        manager.setCurrentTool(tool);
                        console.log('成功创建并设置canvasManager');
                    } catch (err) {
                        console.error('创建canvasManager失败:', err);
                        alert('画布工具初始化失败，请刷新页面后重试');
                    }
                } else {
                    alert('画布工具初始化失败，请刷新页面后重试');
                }
            }
        }, 1000);
    }
    
// 删除通道 - 委托给channelManager处理
async deleteChannel(channelId) {
    console.log('app.js中的deleteChannel被调用，委托给channelManager');
    
    // 直接调用channelManager的方法，避免重复逻辑
    if (this.channelManager) {
        return await this.channelManager.deleteChannel(channelId);
    } else {
        console.error('找不到channelManager实例');
        alert('系统错误：找不到通道管理器');
    }
}
    
// 显示通道模态框（编辑或新建）
showChannelModal(channel = null) {
    console.log('app.js中的showChannelModal被调用，当前状态:', {
        currentScene: this.currentScene,
        sceneManagerCurrentScene: this.sceneManager?.currentScene,
        selectedSceneId: this.sceneManager?.selectedSceneId
    });
    
    // 直接调用channelManager的方法，避免重复逻辑
    if (this.channelManager) {
        this.channelManager.showChannelModal(channel);
        return;
    }
    
    // 如果channelManager不存在，才使用这里的逻辑
    if (!this.currentScene && !this.sceneManager?.currentScene && !this.sceneManager?.selectedSceneId) {
        alert('请先选择一个场景');
        return;
    }
    
    const modal = document.getElementById('channelModal');
    const form = document.getElementById('channelForm');
    const modalTitle = document.getElementById('channelModalTitle');
    
    if (!modal || !form || !modalTitle) {
        console.error('无法找到通道模态框元素');
        return;
    }
    
    if (channel) {
        // 编辑模式
        modalTitle.innerHTML = '<i class="fas fa-edit me-2"></i>编辑通道';
        
        // 使用 getElementById 设置值，避免 elements 访问问题
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = channel.id;
        form.appendChild(idInput);
        
        document.getElementById('channelName').value = channel.name || '';
        document.getElementById('channelNumber').value = channel.channelNumber || '';
        document.getElementById('channelIp').value = channel.ipAddress || '127.0.0.1';
        document.getElementById('channelPort').value = channel.port || '8080';
        document.getElementById('channelColor').value = channel.color || '#FF0000';
    } else {
        // 新建模式
        modalTitle.innerHTML = '<i class="fas fa-plus-circle me-2"></i>新建通道';
        form.reset();
        
        // 设置默认值
        document.getElementById('channelNumber').value = this.channels.length + 1;
        document.getElementById('channelIp').value = '127.0.0.1';
        document.getElementById('channelPort').value = '8080';
        document.getElementById('channelColor').value = '#FF0000';
        
        // 移除可能存在的隐藏ID字段
        const existingIdInput = form.querySelector('input[name="id"]');
        if (existingIdInput) {
            form.removeChild(existingIdInput);
        }
    }
    
    modal.style.display = 'flex';
}

// 保存通道 - 委托给channelManager处理
async saveChannel() {
    console.log('app.js中的saveChannel被调用，委托给channelManager');
    
    // 直接调用channelManager的方法，避免重复逻辑
    if (this.channelManager) {
        return await this.channelManager.saveChannel();
    } else {
        console.error('找不到channelManager实例');
        alert('系统错误：找不到通道管理器');
    }
}

// 编辑通道
async editChannel(channelId) {
    try {
        const response = await fetch(`/api/channels/${channelId}`);
        if (!response.ok) {
            throw new Error('获取通道信息失败');
        }
        
        const channel = await response.json();
        this.showChannelModal(channel);
    } catch (error) {
        console.error('编辑通道出错:', error);
        alert('编辑通道时出错');
    }
}

// 选择工具
selectTool(tool) {
    console.log('选择工具:', tool);
    
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const toolBtn = document.querySelector(`[data-tool="${tool}"]`);
    if (toolBtn) {
        toolBtn.classList.add('active');
    } else {
        console.warn(`未找到工具按钮: [data-tool="${tool}"]`);
    }
    
    // 尝试获取canvasManager实例
    const getCanvasManager = () => {
        // 首先尝试使用this.canvasManager
        if (this.canvasManager) {
            console.log('使用this.canvasManager设置工具:', tool);
            return this.canvasManager;
        }
        
        // 其次尝试使用window.canvasManager
        if (window.canvasManager) {
            console.log('使用window.canvasManager设置工具:', tool);
            // 更新this.canvasManager引用
            this.canvasManager = window.canvasManager;
            return window.canvasManager;
        }
        
        // 尝试获取画布对象并创建CanvasManager实例
        const canvas = document.getElementById('trajectoryCanvas');
        if (canvas && window.CanvasManager) {
            console.log('创建新的CanvasManager实例');
            const manager = new window.CanvasManager('trajectoryCanvas');
            window.canvasManager = manager;
            this.canvasManager = manager;
            return manager;
        }
        
        return null;
    };
    
    // 尝试获取并使用canvasManager
    const manager = getCanvasManager();
    if (manager) {
        manager.setCurrentTool(tool);
        return;
    }
    
    console.error('canvasManager未初始化，无法设置当前工具');
    
    // 尝试延迟设置工具，给canvas.js更多时间初始化
    setTimeout(() => {
        const delayedManager = getCanvasManager();
        if (delayedManager) {
            console.log('延迟后找到canvasManager，设置工具:', tool);
            delayedManager.setCurrentTool(tool);
        } else {
            console.error('延迟后仍未找到canvasManager');
            alert('画布工具初始化失败，请刷新页面后重试');
        }
    }, 1000);
}

// 播放轨迹
async playTrajectory() {
    if (!this.currentChannel || this.trajectories.length === 0) {
        alert('请先选择通道并创建轨迹');
        return;
    }

    // 更新UI状态 - 显示播放中
    const playBtn = document.getElementById('playBtn');
    const stopBtn = document.getElementById('stopBtn');
    if (playBtn) playBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>播放中...';
    if (stopBtn) stopBtn.disabled = false;
    
    // 播放最新的轨迹
    const latestTrajectory = this.trajectories[0];
    
    try {
        // 调用TrajectoryPlaybackController的API播放整个通道
        const response = await fetch('/api/TrajectoryPlayback/play-channel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                channelId: this.currentChannel.id
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('播放轨迹成功:', result.message);
            // 更新UI状态 - 显示播放中
            if (playBtn) {
                playBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放中';
                playBtn.disabled = true;
            }
            if (stopBtn) stopBtn.disabled = false;
        } else {
            console.error('播放轨迹失败:', result.message);
            alert('播放轨迹失败: ' + result.message);
            // 恢复按钮状态
            if (playBtn) {
                playBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放';
                playBtn.disabled = false;
            }
        }
    } catch (error) {
        console.error('播放轨迹失败:', error);
        alert('播放轨迹失败');
        // 恢复按钮状态
        if (playBtn) {
            playBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放';
            playBtn.disabled = false;
        }
    }
}

// 停止轨迹
async stopTrajectory() {
    if (!this.currentChannel || this.trajectories.length === 0) {
        return;
    }

    // 更新UI状态 - 显示停止中
    const playBtn = document.getElementById('playBtn');
    const stopBtn = document.getElementById('stopBtn');
    if (stopBtn) stopBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>停止中...';
    
    try {
        // 调用TrajectoryPlaybackController的API停止所有轨迹
        const response = await fetch('/api/TrajectoryPlayback/stop-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('停止轨迹成功:', result.message);
            // 更新UI状态 - 恢复播放按钮
            if (playBtn) {
                playBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放';
                playBtn.disabled = false;
            }
            if (stopBtn) {
                stopBtn.innerHTML = '<i class="fas fa-stop me-1"></i>停止';
                stopBtn.disabled = true;
            }
        } else {
            console.error('停止轨迹失败:', result.message);
            alert('停止轨迹失败: ' + result.message);
        }
    } catch (error) {
        console.error('停止轨迹失败:', error);
        alert('停止轨迹失败');
        // 恢复按钮状态
        if (stopBtn) {
            stopBtn.innerHTML = '<i class="fas fa-stop me-1"></i>停止';
        }
    }
}

// 清除轨迹
async clearTrajectories() {
    if (!this.currentChannel) {
        alert('请先选择一个通道');
        return;
    }

    if (!confirm('确定要清除当前通道的所有轨迹吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/trajectories/channel/${this.currentChannel.id}/clear`, {
            method: 'DELETE'
        });

        if (response.ok) {
            this.trajectories = [];
            if (window.canvasManager) {
                window.canvasManager.clearCanvas();
            }
        } else {
            return;
        }

        // 播放最新的轨迹
        const latestTrajectory = this.trajectories[0];
        
        try {
            if (this.connection && this.connection.state === signalR.HubConnectionState.Connected) {
                await this.connection.invoke("PlayTrajectory", latestTrajectory.id);
            } else {
                const response = await fetch(`/api/trajectories/${latestTrajectory.id}/play`, {
                    method: 'POST'
                });
                
                if (!response.ok) {
                    alert('播放轨迹失败');
                }
            }
        } catch (error) {
            console.error('播放轨迹失败:', error);
            alert('播放轨迹失败');
        }
    }catch (error) {
            console.error('播放轨迹失败:', error);
            alert('播放轨迹失败');
        }
}
    // 停止轨迹
    async stopTrajectory() {
        if (!this.currentChannel || this.trajectories.length === 0) {
            return;
        }

        const latestTrajectory = this.trajectories[0];
        
        try {
            if (this.connection && this.connection.state === signalR.HubConnectionState.Connected) {
                await this.connection.invoke("StopTrajectory", latestTrajectory.id);
            } else {
                const response = await fetch(`/api/trajectories/${latestTrajectory.id}/stop`, {
                    method: 'POST'
                });
                
                if (!response.ok) {
                    alert('停止轨迹失败');
                }
            }
        } catch (error) {
            console.error('停止轨迹失败:', error);
            alert('停止轨迹失败');
        }
    }

    // 清除轨迹
    async clearTrajectories() {
        if (!this.currentChannel) {
            alert('请先选择一个通道');
            return;
        }

        if (!confirm('确定要清除当前通道的所有轨迹吗？')) {
            return;
        }

        try {
            const response = await fetch(`/api/trajectories/channel/${this.currentChannel.id}/clear`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.trajectories = [];
                if (window.canvasManager) {
                    window.canvasManager.clearCanvas();
                }
            } else {
                alert('清除轨迹失败');
            }
        } catch (error) {
            console.error('清除轨迹失败:', error);
            alert('清除轨迹失败');
        }
    }

    // 更新播放状态
    updatePlaybackStatus(trajectoryId, status) {
        const playBtn = document.getElementById('playBtn');
        const stopBtn = document.getElementById('stopBtn');

        if (status === 1) { // Playing
            playBtn.disabled = true;
            stopBtn.disabled = false;
            playBtn.textContent = '播放中...';
        } else { // Stopped
            playBtn.disabled = false;
            stopBtn.disabled = true;
            playBtn.textContent = '播放';
        }
    }

    // 设置画布背景
    setCanvasBackground(imagePath) {
        if (window.canvasManager) {
            window.canvasManager.setBackground(`/uploads/${imagePath}`);
        }
    }
}

// 初始化应用程序
document.addEventListener('DOMContentLoaded', () => {
    console.log('app.js: DOMContentLoaded事件触发');
    
    // 初始化应用
    window.app = new TrajectoryApp();
    
    // 检查canvasManager是否已初始化
    if (window.canvasManager) {
        console.log('app.js: canvasManager已存在，关联到app对象');
        window.app.canvasManager = window.canvasManager;
    } else {
        console.log('app.js: canvasManager尚未初始化，等待初始化事件');
        
        // 监听canvasManager初始化完成事件
        document.addEventListener('canvasManagerReady', (event) => {
            console.log('app.js: 收到canvasManagerReady事件');
            window.app.canvasManager = event.detail;
            console.log('app.js: canvasManager已关联到app对象');
        });
    }
});
