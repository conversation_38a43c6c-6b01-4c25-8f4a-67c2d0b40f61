using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Infrastructure.Data.XCodeModels;

namespace TrajectoryAuto.Infrastructure.Services;

/// <summary>
/// 通道服务实现 - 使用NewLife.XCode优化音频灯光联动性能
/// </summary>
public class XCodeChannelService : IChannelService
{
    public async Task<List<Channel>> GetChannelsBySceneIdAsync(Guid sceneId)
    {
        return await Task.Run(() =>
        {
            var entities = ChannelEntity.FindBySceneId(sceneId.ToString());
            return entities.Select(e => e.ToBusinessEntity()).ToList();
        });
    }

    public async Task<Channel?> GetChannelByIdAsync(Guid id)
    {
        return await Task.Run(() =>
        {
            var entity = ChannelEntity.FindById(id.ToString());
            if (entity == null) return null;

            var channel = entity.ToBusinessEntity();
            
            // 延迟加载轨迹信息
            var trajectories = TrajectoryEntity.FindByChannelId(id.ToString());
            channel.Trajectories = trajectories.Select(t => t.ToBusinessEntity()).ToList();
            
            return channel;
        });
    }

    public async Task<Channel> CreateChannelAsync(Channel channel)
    {
        return await Task.Run(() =>
        {
            // 设置通道地址
            channel.Address = $"/source/{channel.ChannelNumber}/";
            
            var entity = new ChannelEntity();
            entity.FromBusinessEntity(channel);
            entity.Insert();
            
            return entity.ToBusinessEntity();
        });
    }

    public async Task<Channel> UpdateChannelAsync(Channel channel)
    {
        return await Task.Run(() =>
        {
            var entity = ChannelEntity.FindById(channel.Id.ToString());
            if (entity == null)
                throw new ArgumentException("通道不存在");

            // 设置通道地址
            channel.Address = $"/source/{channel.ChannelNumber}/";
            
            entity.FromBusinessEntity(channel);
            entity.Update();
            
            return entity.ToBusinessEntity();
        });
    }

    public async Task<bool> DeleteChannelAsync(Guid id)
    {
        return await Task.Run(() =>
        {
            var entity = ChannelEntity.FindById(id.ToString());
            if (entity == null) return false;

            // XCode自动处理级联删除轨迹数据
            return entity.Delete() > 0;
        });
    }

    public async Task<bool> SetActiveChannelAsync(Guid channelId, Guid sceneId)
    {
        return await Task.Run(() =>
        {
            // 使用XCode优化的批量更新方法
            var count = ChannelEntity.SetActiveChannel(sceneId.ToString(), channelId.ToString());
            return count >= 0;
        });
    }
}