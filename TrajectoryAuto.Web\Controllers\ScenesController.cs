using Microsoft.AspNetCore.Mvc;
using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Interfaces;

namespace TrajectoryAuto.Web.Controllers;

/// <summary>
/// 场景控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ScenesController : ControllerBase
{
    private readonly ISceneService _sceneService;

    public ScenesController(ISceneService sceneService)
    {
        _sceneService = sceneService;
    }

    /// <summary>
    /// 获取所有场景
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<List<Scene>>> GetAllScenes()
    {
        var scenes = await _sceneService.GetAllScenesAsync();
        return Ok(scenes);
    }

    /// <summary>
    /// 根据ID获取场景
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<Scene>> GetScene(Guid id)
    {
        var scene = await _sceneService.GetSceneByIdAsync(id);
        if (scene == null)
            return NotFound("场景不存在");

        return Ok(scene);
    }

    /// <summary>
    /// 创建场景
    /// </summary>
    /// <param name="scene"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<Scene>> CreateScene([FromBody] Scene scene)
    {
        try
        {
            var createdScene = await _sceneService.CreateSceneAsync(scene);
            return CreatedAtAction(nameof(GetScene), new { id = createdScene.Id }, createdScene);
        }
        catch (Exception ex)
        {
            return BadRequest($"创建场景失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新场景
    /// </summary>
    /// <param name="id"></param>
    /// <param name="scene"></param>
    /// <returns></returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<Scene>> UpdateScene(Guid id, [FromBody] Scene scene)
    {
        if (id != scene.Id)
            return BadRequest("场景ID不匹配");

        try
        {
            var updatedScene = await _sceneService.UpdateSceneAsync(scene);
            return Ok(updatedScene);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return BadRequest($"更新场景失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除场景
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteScene(Guid id)
    {
        var result = await _sceneService.DeleteSceneAsync(id);
        if (!result)
            return NotFound("场景不存在");

        return NoContent();
    }

    /// <summary>
    /// 上传背景图片
    /// </summary>
    /// <param name="id"></param>
    /// <param name="file"></param>
    /// <returns></returns>
    [HttpPost("{id}/background")]
    public async Task<ActionResult<string>> UploadBackgroundImage(Guid id, IFormFile file)
    {
        if (file == null || file.Length == 0)
            return BadRequest("请选择图片文件");

        // 验证文件类型
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
        var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(fileExtension))
            return BadRequest("不支持的图片格式");

        // 验证文件大小（最大10MB）
        if (file.Length > 10 * 1024 * 1024)
            return BadRequest("图片文件过大，最大支持10MB");

        try
        {
            using var stream = file.OpenReadStream();
            var fileName = await _sceneService.UploadBackgroundImageAsync(id, stream, file.FileName);
            return Ok(new { fileName });
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return BadRequest($"上传图片失败: {ex.Message}");
        }
    }
}