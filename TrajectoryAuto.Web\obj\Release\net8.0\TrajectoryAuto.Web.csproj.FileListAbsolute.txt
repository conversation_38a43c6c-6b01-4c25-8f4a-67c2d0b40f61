D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\appsettings.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Web.staticwebassets.runtime.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Web.staticwebassets.endpoints.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Web.exe
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Web.deps.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Web.runtimeconfig.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Web.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Web.pdb
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\EntityFramework.SqlServer.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\EntityFramework.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Humanizer.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.CodeAnalysis.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.CodeAnalysis.CSharp.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.EntityFrameworkCore.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Design.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.Extensions.DependencyModel.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.OpenApi.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Microsoft.Win32.SystemEvents.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Mono.TextTemplating.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\NewLife.Core.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\XCode.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Data.SQLite.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Swashbuckle.AspNetCore.Swagger.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.CodeDom.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Composition.AttributedModel.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Composition.Convention.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Composition.Hosting.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Composition.Runtime.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Composition.TypedParts.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Configuration.ConfigurationManager.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Data.SqlClient.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Data.SQLite.EF6.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Drawing.Common.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Security.Cryptography.ProtectedData.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Security.Permissions.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\System.Windows.Extensions.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\de\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\es\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\it\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win\lib\netcoreapp3.0\Microsoft.Win32.SystemEvents.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win-arm64\native\sni.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win-x64\native\sni.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win-x86\native\sni.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\linux-x64\native\SQLite.Interop.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\osx-x64\native\SQLite.Interop.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win-x64\native\SQLite.Interop.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win-x86\native\SQLite.Interop.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\unix\lib\netcoreapp3.0\System.Drawing.Common.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win\lib\netcoreapp3.0\System.Drawing.Common.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\runtimes\win\lib\netcoreapp3.0\System.Windows.Extensions.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Core.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Infrastructure.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Core.pdb
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Release\net8.0\TrajectoryAuto.Infrastructure.pdb
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.csproj.AssemblyReference.cache
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.GeneratedMSBuildEditorConfig.editorconfig
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.AssemblyInfoInputs.cache
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.AssemblyInfo.cs
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.csproj.CoreCompileInputs.cache
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.MvcApplicationPartsAssemblyInfo.cs
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.MvcApplicationPartsAssemblyInfo.cache
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.RazorAssemblyInfo.cache
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.RazorAssemblyInfo.cs
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\scopedcss\bundle\TrajectoryAuto.Web.styles.css
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets.build.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets.development.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets.build.endpoints.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets\msbuild.TrajectoryAuto.Web.Microsoft.AspNetCore.StaticWebAssets.props
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets\msbuild.TrajectoryAuto.Web.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets\msbuild.build.TrajectoryAuto.Web.props
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets\msbuild.buildMultiTargeting.TrajectoryAuto.Web.props
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets\msbuild.buildTransitive.TrajectoryAuto.Web.props
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets.pack.json
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\staticwebassets.upToDateCheck.txt
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\Trajecto.D1CEDDFE.Up2Date
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\refint\TrajectoryAuto.Web.dll
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.pdb
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\TrajectoryAuto.Web.genruntimeconfig.cache
D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\obj\Release\net8.0\ref\TrajectoryAuto.Web.dll
