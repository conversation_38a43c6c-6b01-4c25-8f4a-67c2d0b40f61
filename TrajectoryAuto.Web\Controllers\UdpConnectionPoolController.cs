using Microsoft.AspNetCore.Mvc;
using TrajectoryAuto.Core.Interfaces;

namespace TrajectoryAuto.Web.Controllers
{
    /// <summary>
    /// UDP连接池管理控制器
    /// 提供连接池状态监控、管理和优化建议的API
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class UdpConnectionPoolController : ControllerBase
    {
        private readonly ILogger<UdpConnectionPoolController> _logger;
        private readonly IUdpConnectionPoolManager _connectionPoolManager;
        private readonly IEnhancedCommunicationService _enhancedCommunicationService;

        public UdpConnectionPoolController(
            ILogger<UdpConnectionPoolController> logger,
            IUdpConnectionPoolManager connectionPoolManager,
            IEnhancedCommunicationService enhancedCommunicationService)
        {
            _logger = logger;
            _connectionPoolManager = connectionPoolManager;
            _enhancedCommunicationService = enhancedCommunicationService;
        }

        /// <summary>
        /// 获取UDP连接池统计信息
        /// </summary>
        /// <returns>连接池统计</returns>
        [HttpGet("stats")]
        public async Task<IActionResult> GetConnectionPoolStats()
        {
            try
            {
                var stats = await _connectionPoolManager.GetConnectionPoolStatsAsync();
                
                return Ok(new
                {
                    success = true,
                    data = stats,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取UDP连接池统计信息时发生错误");
                return StatusCode(500, new { success = false, message = "获取连接池统计信息失败" });
            }
        }

        /// <summary>
        /// 获取连接池详细状态
        /// </summary>
        /// <returns>详细状态信息</returns>
        [HttpGet("status")]
        public async Task<IActionResult> GetConnectionPoolStatus()
        {
            try
            {
                var stats = await _connectionPoolManager.GetConnectionPoolStatsAsync();
                
                // 计算连接池健康度
                var healthScore = CalculateHealthScore(stats);
                var healthStatus = GetHealthStatus(healthScore);
                
                // 生成状态报告
                var statusReport = new
                {
                    healthScore = healthScore,
                    healthStatus = healthStatus,
                    connectionStats = stats,
                    recommendations = GenerateRecommendations(stats),
                    alerts = GenerateAlerts(stats)
                };

                return Ok(new
                {
                    success = true,
                    data = statusReport,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取UDP连接池状态时发生错误");
                return StatusCode(500, new { success = false, message = "获取连接池状态失败" });
            }
        }

        /// <summary>
        /// 手动清理空闲连接
        /// </summary>
        /// <param name="idleTimeoutMinutes">空闲超时时间（分钟），默认10分钟</param>
        /// <returns>清理结果</returns>
        [HttpPost("cleanup")]
        public async Task<IActionResult> CleanupIdleConnections([FromQuery] int idleTimeoutMinutes = 10)
        {
            try
            {
                if (idleTimeoutMinutes < 1 || idleTimeoutMinutes > 60)
                {
                    return BadRequest(new { success = false, message = "空闲超时时间必须在1-60分钟之间" });
                }

                var cleanedCount = await _connectionPoolManager.CleanupIdleConnectionsAsync(idleTimeoutMinutes);
                
                _logger.LogInformation("手动清理UDP连接完成，清理了 {Count} 个连接", cleanedCount);

                return Ok(new
                {
                    success = true,
                    message = $"清理完成，清理了 {cleanedCount} 个空闲连接",
                    cleanedCount = cleanedCount,
                    idleTimeoutMinutes = idleTimeoutMinutes
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动清理UDP连接时发生错误");
                return StatusCode(500, new { success = false, message = "清理连接失败" });
            }
        }

        /// <summary>
        /// 释放指定场景的所有UDP连接（已弃用，场景级别连接不再支持）
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <returns>释放结果</returns>
        [HttpPost("release-scene/{sceneId}")]
        public IActionResult ReleaseSceneConnections(int sceneId)
        {
            try
            {
                // 场景级别连接已移除，返回成功但不执行任何操作
                _logger.LogWarning("场景级别UDP连接已不再支持，场景 {SceneId} 的连接释放请求被忽略", sceneId);
                
                return Ok(new
                {
                    success = true,
                    message = $"场景级别连接已不再支持，场景 {sceneId} 的连接释放请求已忽略",
                    sceneId = sceneId,
                    deprecated = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理场景 {SceneId} 的UDP连接释放请求时发生错误", sceneId);
                return StatusCode(500, new
                {
                    success = false,
                    message = $"处理场景 {sceneId} 的UDP连接释放请求失败: {ex.Message}",
                    sceneId = sceneId
                });
            }
        }

        /// <summary>
        /// 释放指定通道的所有连接
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <returns>释放结果</returns>
        [HttpPost("release-channel/{channelId}")]
        public async Task<IActionResult> ReleaseChannelConnections(int channelId)
        {
            try
            {
                await _enhancedCommunicationService.StopChannelConnectionAsync(channelId);
                
                return Ok(new
                {
                    success = true,
                    message = $"通道 {channelId} 的所有UDP连接已释放",
                    channelId = channelId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放通道 {ChannelId} 的UDP连接时发生错误", channelId);
                return StatusCode(500, new { success = false, message = "释放通道连接失败" });
            }
        }

        /// <summary>
        /// 释放指定轨迹的连接
        /// </summary>
        /// <param name="trajectoryId">轨迹ID</param>
        /// <returns>释放结果</returns>
        [HttpPost("release-trajectory/{trajectoryId}")]
        public async Task<IActionResult> ReleaseTrajectoryConnection(int trajectoryId)
        {
            try
            {
                await _enhancedCommunicationService.StopTrajectoryConnectionAsync(trajectoryId);
                
                return Ok(new
                {
                    success = true,
                    message = $"轨迹 {trajectoryId} 的UDP连接已释放",
                    trajectoryId = trajectoryId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放轨迹 {TrajectoryId} 的UDP连接时发生错误", trajectoryId);
                return StatusCode(500, new { success = false, message = "释放轨迹连接失败" });
            }
        }

        /// <summary>
        /// 获取连接使用建议
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <param name="channelCount">通道数量</param>
        /// <param name="trajectoryCount">轨迹数量</param>
        /// <returns>连接使用建议</returns>
        [HttpGet("recommendation")]
        public async Task<IActionResult> GetConnectionUsageRecommendation(
            [FromQuery] int sceneId,
            [FromQuery] int channelCount,
            [FromQuery] int trajectoryCount)
        {
            try
            {
                if (channelCount < 1 || trajectoryCount < 1)
                {
                    return BadRequest(new { success = false, message = "通道数量和轨迹数量必须大于0" });
                }

                var recommendation = await _enhancedCommunicationService.GetConnectionUsageRecommendationAsync(
                    sceneId, channelCount, trajectoryCount);

                return Ok(new
                {
                    success = true,
                    data = recommendation,
                    inputParameters = new { sceneId, channelCount, trajectoryCount }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取连接使用建议时发生错误");
                return StatusCode(500, new { success = false, message = "获取连接使用建议失败" });
            }
        }

        /// <summary>
        /// 获取连接池性能报告
        /// </summary>
        /// <returns>性能报告</returns>
        [HttpGet("performance-report")]
        public async Task<IActionResult> GetPerformanceReport()
        {
            try
            {
                var stats = await _connectionPoolManager.GetConnectionPoolStatsAsync();
                
                var report = new
                {
                    summary = new
                    {
                        totalConnections = stats.TotalConnectionCount,
                        activeConnections = stats.ActiveConnectionCount,
                        idleConnections = stats.IdleConnectionCount,
                        poolEfficiency = $"{stats.PoolEfficiency:P2}",
                        averageConnectionAge = $"{stats.AverageConnectionAgeMinutes:F1} 分钟"
                    },
                    connectionBreakdown = new
                    {
                        // 场景级别连接已移除，不再统计
                        channelConnections = stats.ChannelConnectionCount,
                        trajectoryConnections = stats.TrajectoryConnectionCount,
                        note = "场景级别连接已不再支持"
                    },
                    networkStats = new
                    {
                        totalSendCount = stats.TotalSendCount,
                        totalBytesSent = FormatBytes(stats.TotalBytesSent),
                        averageThroughput = CalculateAverageThroughput(stats)
                    },
                    maintenance = new
                    {
                        lastCleanupTime = stats.LastCleanupTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "从未清理",
                        cleanedConnectionCount = stats.CleanedConnectionCount,
                        nextRecommendedCleanup = DateTime.UtcNow.AddMinutes(10).ToString("yyyy-MM-dd HH:mm:ss")
                    }
                };

                return Ok(new
                {
                    success = true,
                    data = report,
                    generatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成连接池性能报告时发生错误");
                return StatusCode(500, new { success = false, message = "生成性能报告失败" });
            }
        }

        #region 私有方法

        private double CalculateHealthScore(UdpConnectionPoolStats stats)
        {
            // 连接池效率评分 (40%)
            var efficiencyScore = Math.Min(stats.PoolEfficiency * 20, 40);
            
            // 活跃连接比例评分 (30%)
            var activeRatio = stats.TotalConnectionCount > 0 ? 
                (double)stats.ActiveConnectionCount / stats.TotalConnectionCount : 1.0;
            var activeScore = activeRatio * 30;
            
            // 连接年龄评分 (20%)
            var ageScore = stats.AverageConnectionAgeMinutes < 30 ? 20 : 
                Math.Max(0, 20 - (stats.AverageConnectionAgeMinutes - 30) / 10);
            
            // 总连接数评分 (10%)
            var countScore = stats.TotalConnectionCount < 100 ? 10 : 
                Math.Max(0, 10 - (stats.TotalConnectionCount - 100) / 50);

            return Math.Max(0, efficiencyScore + activeScore + ageScore + countScore);
        }

        private string GetHealthStatus(double healthScore)
        {
            return healthScore switch
            {
                >= 90 => "优秀",
                >= 80 => "良好",
                >= 70 => "一般",
                >= 60 => "较差",
                _ => "需要关注"
            };
        }

        private List<string> GenerateRecommendations(UdpConnectionPoolStats stats)
        {
            var recommendations = new List<string>();

            if (stats.PoolEfficiency < 2.0)
            {
                recommendations.Add("连接复用率较低，考虑使用更高层级的连接类型（如通道或场景级别）");
            }

            if (stats.IdleConnectionCount > stats.ActiveConnectionCount * 2)
            {
                recommendations.Add("空闲连接过多，建议执行清理操作");
            }

            if (stats.AverageConnectionAgeMinutes > 60)
            {
                recommendations.Add("连接平均年龄过高，建议增加清理频率");
            }

            if (stats.TotalConnectionCount > 200)
            {
                recommendations.Add("连接数量较多，考虑优化连接策略或增加清理频率");
            }

            if (recommendations.Count == 0)
            {
                recommendations.Add("连接池状态良好，无需特殊优化");
            }

            return recommendations;
        }

        private List<string> GenerateAlerts(UdpConnectionPoolStats stats)
        {
            var alerts = new List<string>();

            if (stats.TotalConnectionCount > 500)
            {
                alerts.Add("⚠️ 连接数量过多，可能影响系统性能");
            }

            if (stats.PoolEfficiency < 1.0)
            {
                alerts.Add("⚠️ 连接池效率极低，建议检查连接使用策略");
            }

            if (stats.IdleConnectionCount > 100)
            {
                alerts.Add("⚠️ 空闲连接过多，建议立即清理");
            }

            return alerts;
        }

        private string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private string CalculateAverageThroughput(UdpConnectionPoolStats stats)
        {
            // 简单估算，实际应该基于时间窗口计算
            var avgBytesPerSecond = stats.TotalBytesSent / Math.Max(1, stats.AverageConnectionAgeMinutes * 60);
            return FormatBytes((long)avgBytesPerSecond) + "/s";
        }

        #endregion
    }
}
