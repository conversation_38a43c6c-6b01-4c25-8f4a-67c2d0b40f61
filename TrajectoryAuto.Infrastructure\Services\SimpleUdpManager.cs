using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Infrastructure.Services
{
    /// <summary>
    /// 简单UDP管理器
    /// 不使用连接池，每次发送数据时创建新的UDP客户端
    /// </summary>
    public class SimpleUdpManager : IDisposable
    {
        private readonly ILogger<SimpleUdpManager> _logger;
        private readonly ConcurrentDictionary<string, UdpClient> _activeClients;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public SimpleUdpManager(ILogger<SimpleUdpManager> logger)
        {
            _logger = logger;
            _activeClients = new ConcurrentDictionary<string, UdpClient>();
            _logger.LogInformation("简单UDP管理器已初始化");
        }

        /// <summary>
        /// 发送坐标数据
        /// </summary>
        /// <param name="serverIp">服务器IP</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="coordinateData">坐标数据</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> SendCoordinateDataAsync(string serverIp, int serverPort, CoordinateData coordinateData)
        {
            if (string.IsNullOrEmpty(serverIp) || serverPort <= 0 || coordinateData == null)
            {
                return false;
            }

            try
            {
                // 每次发送都创建新的UDP客户端
                using (var udpClient = new UdpClient())
                {
                    var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                    string formattedData = coordinateData.GetFormattedCoordinateString();
                    var data = Encoding.UTF8.GetBytes(formattedData);
                    
                    int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
                    return bytesSent == data.Length;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"发送UDP数据失败: {serverIp}:{serverPort}");
                return false;
            }
        }

        /// <summary>
        /// 为场景创建UDP客户端
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <param name="serverIp">服务器IP</param>
        /// <param name="serverPort">服务器端口</param>
        /// <returns>是否创建成功</returns>
        public bool CreateSceneClient(Guid sceneId, string serverIp, int serverPort)
        {
            string clientKey = $"scene_{sceneId}_{serverIp}_{serverPort}";
            
            try
            {
                // 如果已存在，先关闭并移除
                if (_activeClients.TryRemove(clientKey, out var existingClient))
                {
                    try
                    {
                        existingClient.Close();
                        existingClient.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"关闭现有UDP客户端时出错: {clientKey}");
                    }
                }
                
                // 创建新的UDP客户端
                var udpClient = new UdpClient();
                _activeClients[clientKey] = udpClient;
                _logger.LogInformation($"为场景创建UDP客户端: {clientKey}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"为场景创建UDP客户端失败: {clientKey}");
                return false;
            }
        }

    /// <summary>
    /// 为通道创建UDP客户端
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">场景ID</param>
    /// <param name="serverIp">服务器IP</param>
    /// <param name="serverPort">服务器端口</param>
    /// <returns>是否创建成功</returns>
    public bool CreateChannelClient(Guid channelId, Guid sceneId, string serverIp, int serverPort)
    {
        string clientKey = $"channel_{channelId}_{sceneId}_{serverIp}_{serverPort}";
        
        try
        {
            // 如果已存在，先关闭并移除
            if (_activeClients.TryRemove(clientKey, out var existingClient))
            {
                try
                {
                    existingClient.Close();
                    existingClient.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"关闭现有UDP客户端时出错: {clientKey}");
                }
            }
            
            // 创建新的UDP客户端
            var udpClient = new UdpClient();
            _activeClients[clientKey] = udpClient;
            _logger.LogInformation($"为通道创建UDP客户端: {clientKey}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"为通道创建UDP客户端失败: {clientKey}");
            return false;
        }
    }
    
    /// <summary>
    /// 为轨迹创建UDP客户端
    /// </summary>
    /// <param name="trajectoryId">轨迹ID</param>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">场景ID</param>
    /// <param name="serverIp">服务器IP</param>
    /// <param name="serverPort">服务器端口</param>
    /// <returns>是否创建成功</returns>
    public bool CreateTrajectoryClient(Guid trajectoryId, Guid channelId, Guid sceneId, string serverIp, int serverPort)
    {
        string clientKey = $"trajectory_{trajectoryId}_{channelId}_{sceneId}_{serverIp}_{serverPort}";
        
        try
        {
            // 如果已存在，先关闭并移除
            if (_activeClients.TryRemove(clientKey, out var existingClient))
            {
                try
                {
                    existingClient.Close();
                    existingClient.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"关闭现有UDP客户端时出错: {clientKey}");
                }
            }
            
            // 创建新的UDP客户端
            var udpClient = new UdpClient();
            _activeClients[clientKey] = udpClient;
            _logger.LogInformation($"为轨迹创建UDP客户端: {clientKey}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"为轨迹创建UDP客户端失败: {clientKey}");
            return false;
        }
    }

        /// <summary>
        /// 使用场景客户端发送坐标数据
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <param name="serverIp">服务器IP</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="coordinateData">坐标数据</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> SendWithSceneClientAsync(Guid sceneId, string serverIp, int serverPort, CoordinateData coordinateData)
        {
            string clientKey = $"scene_{sceneId}_{serverIp}_{serverPort}";
            
            // 确保客户端存在
            if (!_activeClients.TryGetValue(clientKey, out var udpClient))
            {
                CreateSceneClient(sceneId, serverIp, serverPort);
                if (!_activeClients.TryGetValue(clientKey, out udpClient))
                {
                    return false;
                }
            }
            
            try
            {
                var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                string formattedData = coordinateData.GetFormattedCoordinateString();
                var data = Encoding.UTF8.GetBytes(formattedData);
                
                int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
                return bytesSent == data.Length;
            }
            catch (ObjectDisposedException)
            {
                _logger.LogWarning($"UDP客户端已被释放，尝试重新创建: {clientKey}");
                
                // 重新创建客户端并重试
                CreateSceneClient(sceneId, serverIp, serverPort);
                if (_activeClients.TryGetValue(clientKey, out udpClient))
                {
                    try
                    {
                        var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                        string formattedData = coordinateData.GetFormattedCoordinateString();
                        var data = Encoding.UTF8.GetBytes(formattedData);
                        
                        int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
                        return bytesSent == data.Length;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"使用重新创建的UDP客户端发送数据失败: {clientKey}");
                        return false;
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"使用场景UDP客户端发送数据失败: {clientKey}");
                return false;
            }
        }

    /// <summary>
    /// 使用通道客户端发送坐标数据
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">场景ID</param>
    /// <param name="serverIp">服务器IP</param>
    /// <param name="serverPort">服务器端口</param>
    /// <param name="coordinateData">坐标数据</param>
    /// <returns>是否发送成功</returns>
    public async Task<bool> SendWithChannelClientAsync(Guid channelId, Guid sceneId, string serverIp, int serverPort, CoordinateData coordinateData)
    {
        string clientKey = $"channel_{channelId}_{sceneId}_{serverIp}_{serverPort}";
        
        // 确保客户端存在
        if (!_activeClients.TryGetValue(clientKey, out var udpClient))
        {
            CreateChannelClient(channelId, sceneId, serverIp, serverPort);
            if (!_activeClients.TryGetValue(clientKey, out udpClient))
            {
                return false;
            }
        }
        
        try
        {
            var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
            string formattedData = coordinateData.GetFormattedCoordinateString();
            var data = Encoding.UTF8.GetBytes(formattedData);
            
            int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
            return bytesSent == data.Length;
        }
        catch (ObjectDisposedException)
        {
            _logger.LogWarning($"UDP客户端已被释放，尝试重新创建: {clientKey}");
            
            // 重新创建客户端并重试
            CreateChannelClient(channelId, sceneId, serverIp, serverPort);
            if (_activeClients.TryGetValue(clientKey, out udpClient))
            {
                try
                {
                    var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                    string formattedData = coordinateData.GetFormattedCoordinateString();
                    var data = Encoding.UTF8.GetBytes(formattedData);
                    
                    int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
                    return bytesSent == data.Length;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"使用重新创建的UDP客户端发送数据失败: {clientKey}");
                    return false;
                }
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"使用通道UDP客户端发送数据失败: {clientKey}");
            return false;
        }
    }
    
    /// <summary>
    /// 使用轨迹客户端发送坐标数据
    /// </summary>
    /// <param name="trajectoryId">轨迹ID</param>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">场景ID</param>
    /// <param name="serverIp">服务器IP</param>
    /// <param name="serverPort">服务器端口</param>
    /// <param name="coordinateData">坐标数据</param>
    /// <returns>是否发送成功</returns>
    public async Task<bool> SendWithTrajectoryClientAsync(Guid trajectoryId, Guid channelId, Guid sceneId, string serverIp, int serverPort, CoordinateData coordinateData)
    {
        string clientKey = $"trajectory_{trajectoryId}_{channelId}_{sceneId}_{serverIp}_{serverPort}";
        
        // 确保客户端存在
        if (!_activeClients.TryGetValue(clientKey, out var udpClient))
        {
            CreateTrajectoryClient(trajectoryId, channelId, sceneId, serverIp, serverPort);
            if (!_activeClients.TryGetValue(clientKey, out udpClient))
            {
                return false;
            }
        }
        
        try
        {
            var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
            string formattedData = coordinateData.GetFormattedCoordinateString();
            var data = Encoding.UTF8.GetBytes(formattedData);
            
            int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
            return bytesSent == data.Length;
        }
        catch (ObjectDisposedException)
        {
            _logger.LogWarning($"UDP客户端已被释放，尝试重新创建: {clientKey}");
            
            // 重新创建客户端并重试
            CreateTrajectoryClient(trajectoryId, channelId, sceneId, serverIp, serverPort);
            if (_activeClients.TryGetValue(clientKey, out udpClient))
            {
                try
                {
                    var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                    string formattedData = coordinateData.GetFormattedCoordinateString();
                    var data = Encoding.UTF8.GetBytes(formattedData);
                    
                    int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
                    return bytesSent == data.Length;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"使用重新创建的UDP客户端发送数据失败: {clientKey}");
                    return false;
                }
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"使用轨迹UDP客户端发送数据失败: {clientKey}");
            return false;
        }
    }

        /// <summary>
        /// 停止场景客户端
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        public void StopSceneClient(Guid sceneId)
        {
            foreach (var key in _activeClients.Keys)
            {
                if (key.StartsWith($"scene_{sceneId}_"))
                {
                    if (_activeClients.TryRemove(key, out var client))
                    {
                        try
                        {
                            client.Close();
                            client.Dispose();
                            _logger.LogInformation($"已停止场景UDP客户端: {key}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"停止场景UDP客户端时出错: {key}");
                        }
                    }
                }
            }
        }

    /// <summary>
    /// 停止通道客户端
    /// </summary>
    /// <param name="channelId">通道ID</param>
    public void StopChannelClient(Guid channelId)
    {
        foreach (var key in _activeClients.Keys)
        {
            if (key.StartsWith($"channel_{channelId}_"))
            {
                if (_activeClients.TryRemove(key, out var client))
                {
                    try
                    {
                        client.Close();
                        client.Dispose();
                        _logger.LogInformation($"已停止通道UDP客户端: {key}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"停止通道UDP客户端时出错: {key}");
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// 停止轨迹UDP客户端
    /// </summary>
    /// <param name="trajectoryId">轨迹ID</param>
    public void StopTrajectoryUdp(Guid trajectoryId)
    {
        foreach (var key in _activeClients.Keys)
        {
            if (key.Contains($"_{trajectoryId}_"))
            {
                if (_activeClients.TryRemove(key, out var client))
                {
                    try
                    {
                        client.Close();
                        client.Dispose();
                        _logger.LogInformation($"已停止轨迹UDP客户端: {key}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"停止轨迹UDP客户端时出错: {key}");
                    }
                }
            }
        }
    }

        /// <summary>
        /// 停止所有客户端
        /// </summary>
        public void StopAllClients()
        {
            foreach (var key in _activeClients.Keys)
            {
                if (_activeClients.TryRemove(key, out var client))
                {
                    try
                    {
                        client.Close();
                        client.Dispose();
                        _logger.LogInformation($"已停止UDP客户端: {key}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"停止UDP客户端时出错: {key}");
                    }
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                StopAllClients();
                _activeClients.Clear();
            }

            _disposed = true;
        }
    }
}