using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Core.Models;
using TrajectoryAuto.Infrastructure.Data.XCodeModels;
using TrajectoryAuto.Infrastructure.Services;

namespace TrajectoryAuto.Web.Controllers;

/// <summary>
/// 轨迹播放控制器
/// 负责处理轨迹播放、停止和UDP数据发送的核心逻辑
/// 
/// 架构特点：
/// - 使用单例播放状态服务解决控制器无状态问题
/// - 多层防护机制确保停止命令立即生效
/// - 线程安全的并发操作支持
/// - 支持单个、通道、场景级别的轨迹播放
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TrajectoryPlaybackController : ControllerBase
{
    #region 私有字段
    
    private readonly ITrajectoryService _trajectoryService;
    private readonly ICommunicationService _communicationService;
    private readonly IEnhancedCommunicationService _enhancedCommunicationService;
    private readonly ILogger<TrajectoryPlaybackController> _logger;
    private readonly IPlaybackStateService _playbackStateService;
    private readonly IUdpConnectionPoolManager _connectionPoolManager;
    private readonly SimpleUdpManager _simpleUdpManager;
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<Guid, CancellationTokenSource> _playbackTokens = new();
    
    #endregion

    #region 构造函数
    
    public TrajectoryPlaybackController(
        ITrajectoryService trajectoryService,
        ICommunicationService communicationService,
        IEnhancedCommunicationService enhancedCommunicationService,
        ILogger<TrajectoryPlaybackController> logger,
        IPlaybackStateService playbackStateService,
        IUdpConnectionPoolManager connectionPoolManager,
        SimpleUdpManager simpleUdpManager,
        IServiceProvider serviceProvider)
    {
        _trajectoryService = trajectoryService ?? throw new ArgumentNullException(nameof(trajectoryService));
        _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
        _enhancedCommunicationService = enhancedCommunicationService ?? throw new ArgumentNullException(nameof(enhancedCommunicationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _playbackStateService = playbackStateService ?? throw new ArgumentNullException(nameof(playbackStateService));
        _connectionPoolManager = connectionPoolManager ?? throw new ArgumentNullException(nameof(connectionPoolManager));
        _simpleUdpManager = simpleUdpManager ?? throw new ArgumentNullException(nameof(simpleUdpManager));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }
    
    #endregion

    #region 公共API方法
    
    /// <summary>
    /// 开始播放单个轨迹
    /// </summary>
    /// <param name="request">播放请求</param>
    /// <returns>播放结果</returns>
    [HttpPost("play")]
    public async Task<IActionResult> PlayTrajectory([FromBody] PlayTrajectoryRequest request)
    {
        if (request == null || request.TrajectoryId == Guid.Empty)
        {
            return BadRequest("无效的请求参数");
        }

        try
        {
            _logger.LogInformation($"开始播放轨迹: {request.TrajectoryId}");
            
            // 获取轨迹数据
            var trajectory = await _trajectoryService.GetTrajectoryByIdAsync(request.TrajectoryId);
            if (trajectory == null)
            {
                return NotFound($"找不到ID为 {request.TrajectoryId} 的轨迹");
            }

            // 停止之前的播放（如果有）
            await StopTrajectoryPlayback(request.TrajectoryId);
            
            // 重置全局停止标志（开始新的播放时）
            _playbackStateService.ResetGlobalStop();

            // 创建取消令牌
            var cts = new CancellationTokenSource();
            _playbackTokens[request.TrajectoryId] = cts;

            // 在后台任务中播放轨迹
            _ = Task.Run(async () =>
            {
                try
                {
                    await PlayTrajectoryInternalAsync(trajectory, cts.Token);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation($"轨迹 {request.TrajectoryId} 播放被取消");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"轨迹 {request.TrajectoryId} 播放出错: {ex.Message}");
                }
                finally
                {
                    _playbackTokens.Remove(request.TrajectoryId);
                }
            });

            return Ok(new { success = true, message = "轨迹播放已开始" });
        }
        catch (Exception ex)
        {
            _logger.LogError($"开始播放轨迹失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"开始播放轨迹失败: {ex.Message}" });
        }
    } 

    /// <summary>
    /// 播放通道下的所有轨迹（支持多场景并发播放）
    /// </summary>
    /// <param name="request">通道播放请求</param>
    /// <returns>播放结果</returns>
    [HttpPost("play-channel")]
    public async Task<IActionResult> PlayChannelTrajectories([FromBody] PlayChannelRequest request)
    {
        if (request == null || request.ChannelId == Guid.Empty)
        {
            return BadRequest("无效的请求参数");
        }

        try
        {
            _logger.LogInformation($"请求播放通道 {request.ChannelId} 的所有轨迹");
            
            // 检查通道是否已经在播放
            if (_playbackStateService.IsChannelPlaybackActive(request.ChannelId))
            {
                _logger.LogInformation($"通道 {request.ChannelId} 已经在播放中，先停止当前播放");
                // 先停止当前通道的播放
                await StopChannelPlayback(request.ChannelId);
                // 等待一小段时间确保停止操作生效
                await Task.Delay(100);
            }
            
            // 获取通道信息
            var channel = await GetChannelById(request.ChannelId);
            if (channel == null)
            {
                return NotFound($"找不到通道 {request.ChannelId}");
            }
            
            // 获取通道下的所有轨迹
            var trajectories = await _trajectoryService.GetTrajectoriesByChannelIdAsync(request.ChannelId);
            if (trajectories == null || trajectories.Count == 0)
            {
                return NotFound($"通道 {request.ChannelId} 下没有轨迹");
            }

            // 重置全局停止标志，确保可以重新播放
            _playbackStateService.ResetGlobalStop();
            _logger.LogInformation($"已重置全局停止标志，准备播放通道 {request.ChannelId}");
            
            // 开始通道播放状态管理
            _playbackStateService.StartChannelPlayback(request.ChannelId, channel.SceneId);
            _logger.LogInformation($"已设置通道 {request.ChannelId} 为播放状态");

            // 停止通道下所有轨迹的播放（如果有的话）
            foreach (var trajectory in trajectories)
            {
                await StopTrajectoryPlayback(trajectory.Id);
            }

            // 使用服务作用域获取连接池使用建议
            ConnectionUsageRecommendation recommendation;
            using (var scope = _serviceProvider.CreateScope())
            {
                var scopedEnhancedCommunicationService = scope.ServiceProvider.GetRequiredService<IEnhancedCommunicationService>();
                recommendation = await scopedEnhancedCommunicationService.GetConnectionUsageRecommendationAsync(
                    (int)channel.SceneId.GetHashCode(), 1, trajectories.Count);
            }
            _logger.LogInformation($"连接池使用建议: {recommendation.RecommendedConnectionType}, 理由: {recommendation.Reason}");

            // 为每个轨迹创建播放任务
            var tasks = new List<Task>();
            foreach (var trajectory in trajectories)
            {
                var cts = new CancellationTokenSource();
                _playbackTokens[trajectory.Id] = cts;

                var trajectoryTask = Task.Run(async () =>
                {
                    try
                    {
                        // 使用服务作用域避免 ObjectDisposedException
                        using var scope = _serviceProvider.CreateScope();
                        var scopedEnhancedCommunicationService = scope.ServiceProvider.GetRequiredService<IEnhancedCommunicationService>();
                        
                        // 根据建议使用不同级别的连接（不再支持场景级别）
                        switch (recommendation.RecommendedConnectionType)
                        {
                            case UdpConnectionType.Channel:
                                await PlayTrajectoryWithChannelConnectionAsync(trajectory, request.ChannelId, channel.SceneId, cts.Token, scopedEnhancedCommunicationService);
                                break;
                            case UdpConnectionType.Trajectory:
                            default:
                                // 回退到原有方法
                                await PlayTrajectoryInternalAsync(trajectory, cts.Token);
                                break;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.LogInformation($"轨迹 {trajectory.Id} 播放被取消");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"轨迹 {trajectory.Id} 播放出错: {ex.Message}");
                    }
                    finally
                    {
                        _playbackTokens.Remove(trajectory.Id);
                    }
                }, cts.Token);
                
                tasks.Add(trajectoryTask);
            }

            // 启动所有播放任务
            _ = Task.WhenAll(tasks);

            return Ok(new { 
                success = true, 
                message = $"通道 {request.ChannelId} 的 {trajectories.Count} 个轨迹开始播放", 
                trajectoryCount = trajectories.Count,
                sceneId = channel.SceneId,
                isAlreadyPlaying = false
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"播放通道轨迹失败: {ex.Message}");
            // 如果出错，清除通道播放状态
            _playbackStateService.StopChannelPlayback(request.ChannelId);
            return StatusCode(500, new { success = false, message = $"播放通道轨迹失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 播放场景下的所有轨迹（支持多场景并发播放）
    /// </summary>
    /// <param name="request">场景播放请求</param>
    /// <returns>播放结果</returns>
    [HttpPost("play-scene")]
    public async Task<IActionResult> PlaySceneTrajectories([FromBody] PlaySceneRequest request)
    {
        if (request == null || request.SceneId == Guid.Empty)
        {
            return BadRequest("无效的请求参数");
        }

        try
        {
            _logger.LogInformation($"请求播放场景 {request.SceneId} 的所有轨迹");
            
            // 检查场景是否已经在播放
            if (_playbackStateService.IsScenePlaybackActive(request.SceneId))
            {
                _logger.LogInformation($"场景 {request.SceneId} 已经在播放中，先停止当前播放");
                // 先停止当前场景的播放
                await StopScenePlayback(request.SceneId);
                // 等待一小段时间确保停止操作生效
                await Task.Delay(100);
            }
            
            // 获取场景下的所有通道
            var channels = await GetChannelsBySceneId(request.SceneId);
            if (channels == null || channels.Count == 0)
            {
                return NotFound($"场景 {request.SceneId} 下没有通道");
            }

            // 重置全局停止标志，确保可以重新播放
            _playbackStateService.ResetGlobalStop();
            _logger.LogInformation($"已重置全局停止标志，准备播放场景 {request.SceneId}");
            
            // 开始场景播放状态管理
            _playbackStateService.StartScenePlayback(request.SceneId);
            _logger.LogInformation($"已设置场景 {request.SceneId} 为播放状态");

            int trajectoryCount = 0;

            // 为每个通道播放轨迹
            foreach (var channel in channels)
            {
                // 获取通道下的所有轨迹
                var trajectories = await _trajectoryService.GetTrajectoriesByChannelIdAsync(channel.Id);
                if (trajectories == null || trajectories.Count == 0)
                {
                    continue;
                }

                trajectoryCount += trajectories.Count;
                
                // 开始通道播放状态管理
                _playbackStateService.StartChannelPlayback(channel.Id, request.SceneId);
                _logger.LogInformation($"已设置通道 {channel.Id} 为播放状态");

                // 停止通道下所有轨迹的播放（如果有的话）
                foreach (var trajectory in trajectories)
                {
                    await StopTrajectoryPlayback(trajectory.Id);
                }

                // 为每个轨迹创建播放任务（使用场景级别连接）
                foreach (var trajectory in trajectories)
                {
                    var cts = new CancellationTokenSource();
                    _playbackTokens[trajectory.Id] = cts;

                    // 在后台任务中播放轨迹（使用服务作用域）
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            // 使用服务作用域避免 ObjectDisposedException
                            using var scope = _serviceProvider.CreateScope();
                            var scopedEnhancedCommunicationService = scope.ServiceProvider.GetRequiredService<IEnhancedCommunicationService>();
                            
                            // 场景级别连接已不再支持，使用通道级别连接
                            await PlayTrajectoryWithChannelConnectionAsync(trajectory, trajectory.ChannelId, request.SceneId, cts.Token, scopedEnhancedCommunicationService);
                        }
                        catch (OperationCanceledException)
                        {
                            _logger.LogInformation($"轨迹 {trajectory.Id} 播放被取消");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"轨迹 {trajectory.Id} 播放出错: {ex.Message}");
                        }
                        finally
                        {
                            _playbackTokens.Remove(trajectory.Id);
                        }
                    });
                }
            }

            return Ok(new { 
                success = true, 
                message = $"场景 {request.SceneId} 的 {trajectoryCount} 个轨迹开始播放", 
                trajectoryCount = trajectoryCount,
                channelCount = channels.Count,
                isAlreadyPlaying = false
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"播放场景轨迹失败: {ex.Message}");
            // 如果出错，清除场景播放状态
            _playbackStateService.StopScenePlayback(request.SceneId);
            return StatusCode(500, new { success = false, message = $"播放场景轨迹失败: {ex.Message}" });
        }
    }
    
    /// <summary>
    /// 停止场景播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>停止结果</returns>
    [HttpPost("stop-scene/{sceneId}")]
    public async Task<IActionResult> StopScenePlayback(Guid sceneId)
    {
        if (sceneId == Guid.Empty)
        {
            return BadRequest("无效的场景ID");
        }

        try
        {
            _logger.LogInformation($"停止场景 {sceneId} 的播放");
            
            // 检查场景是否在播放
            if (!_playbackStateService.IsScenePlaybackActive(sceneId))
            {
                _logger.LogInformation($"场景 {sceneId} 当前没有在播放");
                return Ok(new { success = true, message = $"场景 {sceneId} 当前没有在播放", wasPlaying = false });
            }
            
            // 获取场景下正在播放的通道
            var activeChannels = _playbackStateService.GetActiveChannelsInScene(sceneId);
            _logger.LogInformation($"场景 {sceneId} 下有 {activeChannels.Count} 个通道正在播放");
            
            // 停止场景播放状态（这会自动停止所有通道）
            _playbackStateService.StopScenePlayback(sceneId);
            
            // 停止场景UDP客户端
            _simpleUdpManager.StopSceneClient(sceneId);
            _logger.LogInformation($"已停止场景UDP客户端: {sceneId}");
            
            int stoppedTrajectoryCount = 0;
            
            // 停止场景下所有轨迹的播放任务
            var trajectoryIds = _playbackTokens.Keys.ToList();
            foreach (var trajectoryId in trajectoryIds)
            {
                // 获取轨迹信息以检查是否属于该场景
                var trajectory = await _trajectoryService.GetTrajectoryByIdAsync(trajectoryId);
                if (trajectory != null)
                {
                    var channel = await GetChannelById(trajectory.ChannelId);
                    if (channel != null && channel.SceneId == sceneId)
                    {
                        await StopTrajectoryPlayback(trajectoryId);
                        stoppedTrajectoryCount++;
                    }
                }
            }
            
            // 等待一小段时间确保停止操作生效
            await Task.Delay(100);
            
            _logger.LogInformation($"已停止场景 {sceneId} 下的 {stoppedTrajectoryCount} 个轨迹播放");
            
            return Ok(new { 
                success = true, 
                message = $"已停止场景 {sceneId} 的播放", 
                stoppedTrajectoryCount = stoppedTrajectoryCount,
                stoppedChannelCount = activeChannels.Count,
                wasPlaying = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"停止场景播放失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"停止场景播放失败: {ex.Message}" });
        }
    }
    
    /// <summary>
    /// 停止通道播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <returns>停止结果</returns>
    [HttpPost("stop-channel/{channelId}")]
    public async Task<IActionResult> StopChannelPlayback(Guid channelId)
    {
        if (channelId == Guid.Empty)
        {
            return BadRequest("无效的通道ID");
        }

        try
        {
            _logger.LogInformation($"停止通道 {channelId} 的播放");
            
            // 检查通道是否在播放
            if (!_playbackStateService.IsChannelPlaybackActive(channelId))
            {
                _logger.LogInformation($"通道 {channelId} 当前没有在播放");
                return Ok(new { success = true, message = $"通道 {channelId} 当前没有在播放", wasPlaying = false });
            }
            
            // 停止通道播放状态
            _playbackStateService.StopChannelPlayback(channelId);
            
            // 停止通道UDP客户端
            _simpleUdpManager.StopChannelClient(channelId);
            _logger.LogInformation($"已停止通道UDP客户端: {channelId}");
            
            int stoppedTrajectoryCount = 0;
            
            // 停止通道下所有轨迹的播放任务
            var trajectoryIds = _playbackTokens.Keys.ToList();
            foreach (var trajectoryId in trajectoryIds)
            {
                // 获取轨迹信息以检查是否属于该通道
                var trajectory = await _trajectoryService.GetTrajectoryByIdAsync(trajectoryId);
                if (trajectory != null && trajectory.ChannelId == channelId)
                {
                    await StopTrajectoryPlayback(trajectoryId);
                    stoppedTrajectoryCount++;
                }
            }
            
            // 等待一小段时间确保停止操作生效
            await Task.Delay(50);
            
            _logger.LogInformation($"已停止通道 {channelId} 下的 {stoppedTrajectoryCount} 个轨迹播放");
            
            return Ok(new { 
                success = true, 
                message = $"已停止通道 {channelId} 的播放", 
                stoppedTrajectoryCount = stoppedTrajectoryCount,
                wasPlaying = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"停止通道播放失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"停止通道播放失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 停止所有播放
    /// </summary>
    /// <returns>停止结果</returns>
    [HttpPost("stop-all")]
    public async Task<IActionResult> StopAllPlayback()
    {
        try
        {
            _logger.LogInformation("停止所有轨迹播放");
            
            // 获取所有正在播放的轨迹ID
            var trajectoryIds = _playbackTokens.Keys.ToList();
            _logger.LogInformation($"当前有 {trajectoryIds.Count} 个轨迹正在播放");
            
            // 设置全局停止标志 - 这是唯一应该设置全局停止标志的地方
            _playbackStateService.SetGlobalStop();
            _logger.LogInformation("已设置全局停止标志");
            
            // 立即停止所有UDP客户端
            _simpleUdpManager.StopAllClients();
            _logger.LogInformation("已停止所有UDP客户端");
            
            // 获取所有活动场景
            var activeScenes = _playbackStateService.GetActiveScenes();
            foreach (var sceneId in activeScenes)
            {
                // 停止场景播放状态
                _playbackStateService.StopScenePlayback(sceneId);
                _logger.LogInformation($"已停止场景播放状态: {sceneId}");
                
                // 获取场景下的所有活动通道
                var activeChannels = _playbackStateService.GetActiveChannelsInScene(sceneId);
                foreach (var channelId in activeChannels)
                {
                    // 停止通道播放状态
                    _playbackStateService.StopChannelPlayback(channelId);
                    _logger.LogInformation($"已停止通道播放状态: {channelId}");
                    
                    // 停止通道UDP客户端
                    _simpleUdpManager.StopChannelClient(channelId);
                    _logger.LogInformation($"已停止通道UDP客户端: {channelId}");
                }
            }
            
            // 停止所有播放任务
            foreach (var trajectoryId in trajectoryIds)
            {
                // 同时调用控制器和轨迹服务的停止方法
                await StopTrajectoryPlayback(trajectoryId);
                await _trajectoryService.StopTrajectoryAsync(trajectoryId);
            }
            
            // 等待一小段时间确保所有任务都已停止
            await Task.Delay(200);
            
            // 停止轨迹服务中的所有播放任务
            try
            {
                await _trajectoryService.StopAllPlaybackAsync();
                _logger.LogInformation("已停止轨迹服务中的所有播放任务");
            }
            catch (Exception ex)
            {
                _logger.LogError($"停止轨迹服务播放任务失败: {ex.Message}");
            }
            
            // 再次检查是否还有活动的轨迹
            var remainingTrajectories = _playbackTokens.Keys.ToList();
            if (remainingTrajectories.Count > 0)
            {
                _logger.LogWarning($"仍有 {remainingTrajectories.Count} 个轨迹未停止，强制清除");
                foreach (var trajectoryId in remainingTrajectories)
                {
                    if (_playbackTokens.TryGetValue(trajectoryId, out var cts))
                    {
                        cts.Cancel();
                        _playbackTokens.Remove(trajectoryId);
                    }
                }
            }
            
            return Ok(new { success = true, message = $"已停止 {trajectoryIds.Count} 个轨迹的播放" });
        }
        catch (Exception ex)
        {
            _logger.LogError($"停止所有轨迹播放失败: {ex.Message}");
            // 即使出错也要尝试停止UDP客户端
            try
            {
                _simpleUdpManager.StopAllClients();
                _logger.LogInformation("异常处理中已停止所有UDP客户端");
                
                // 强制清除所有播放令牌
                foreach (var entry in _playbackTokens)
                {
                    try
                    {
                        entry.Value.Cancel();
                    }
                    catch { }
                }
                _playbackTokens.Clear();
                _logger.LogInformation("异常处理中已强制清除所有播放令牌");
            }
            catch (Exception stopEx)
            {
                _logger.LogError($"异常处理中停止UDP客户端也失败: {stopEx.Message}");
            }
            return StatusCode(500, new { success = false, message = $"停止所有轨迹播放失败: {ex.Message}" });
        }
    }
    
    /// <summary>
    /// 获取播放状态信息
    /// </summary>
    /// <returns>播放状态信息</returns>
    [HttpGet("status")]
    public IActionResult GetPlaybackStatus()
    {
        try
        {
            var activeScenes = _playbackStateService.GetActiveScenes();
            var sceneDetails = new List<object>();
            
            foreach (var sceneId in activeScenes)
            {
                var activeChannels = _playbackStateService.GetActiveChannelsInScene(sceneId);
                var channelDetails = new List<object>();
                
                foreach (var channelId in activeChannels)
                {
                    var trajectoryCount = _playbackTokens.Keys.Count(trajectoryId => 
                    {
                        // 这里可能需要查询数据库来获取轨迹的通道ID，但为了简化，我们返回基本信息
                        return true;
                    });
                    
                    channelDetails.Add(new
                    {
                        channelId = channelId,
                        isActive = _playbackStateService.IsChannelPlaybackActive(channelId),
                        trajectoryCount = trajectoryCount
                    });
                }
                
                sceneDetails.Add(new
                {
                    sceneId = sceneId,
                    isActive = _playbackStateService.IsScenePlaybackActive(sceneId),
                    channelCount = activeChannels.Count,
                    channels = channelDetails
                });
            }
            
            return Ok(new
            {
                success = true,
                isGlobalStopRequested = _playbackStateService.IsGlobalStopRequested(),
                activeSceneCount = activeScenes.Count,
                totalActiveTrajectories = _playbackTokens.Count,
                scenes = sceneDetails
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取播放状态失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"获取播放状态失败: {ex.Message}" });
        }
    }
    
    /// <summary>
    /// 获取场景播放状态
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>场景播放状态</returns>
    [HttpGet("status/scene/{sceneId}")]
    public IActionResult GetScenePlaybackStatus(Guid sceneId)
    {
        if (sceneId == Guid.Empty)
        {
            return BadRequest("无效的场景ID");
        }
        
        try
        {
            var isActive = _playbackStateService.IsScenePlaybackActive(sceneId);
            var activeChannels = _playbackStateService.GetActiveChannelsInScene(sceneId);
            
            return Ok(new
            {
                success = true,
                sceneId = sceneId,
                isActive = isActive,
                isStopRequested = _playbackStateService.IsSceneStopRequested(sceneId),
                activeChannelCount = activeChannels.Count,
                activeChannels = activeChannels
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取场景播放状态失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"获取场景播放状态失败: {ex.Message}" });
        }
    }
    
    /// <summary>
    /// 获取通道播放状态
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <returns>通道播放状态</returns>
    [HttpGet("status/channel/{channelId}")]
    public IActionResult GetChannelPlaybackStatus(Guid channelId)
    {
        if (channelId == Guid.Empty)
        {
            return BadRequest("无效的通道ID");
        }
        
        try
        {
            var isActive = _playbackStateService.IsChannelPlaybackActive(channelId);
            
            return Ok(new
            {
                success = true,
                channelId = channelId,
                isActive = isActive,
                isStopRequested = _playbackStateService.IsChannelStopRequested(channelId)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取通道播放状态失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"获取通道播放状态失败: {ex.Message}" });
        }
    }
    
    #endregion

    #region 私有方法
    
    /// <summary>
    /// 内部方法：停止轨迹播放
    /// </summary>
    private async Task<bool> StopTrajectoryPlayback(Guid trajectoryId)
    {
        bool stopped = false;
        
        // 停止控制器级别的播放
        if (_playbackTokens.TryGetValue(trajectoryId, out var cts))
        {
            _logger.LogInformation($"停止控制器轨迹播放: {trajectoryId}");
            cts.Cancel();
            _playbackTokens.Remove(trajectoryId);
            stopped = true;
        }
        
        // 停止轨迹UDP客户端
        _simpleUdpManager.StopTrajectoryUdp(trajectoryId);
        _logger.LogInformation($"已停止轨迹UDP客户端: {trajectoryId}");
        
        // 同时停止轨迹服务级别的播放
        try
        {
            var serviceResult = await _trajectoryService.StopTrajectoryAsync(trajectoryId);
            if (serviceResult)
            {
                _logger.LogInformation($"已停止轨迹服务播放: {trajectoryId}");
                stopped = true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"停止轨迹服务播放失败: {trajectoryId}, 错误: {ex.Message}");
        }
        
        if (stopped)
        {
            // 给一点时间让取消操作生效
            await Task.Delay(50);
        }
        
        return stopped;
    }

    /// <summary>
    /// 内部方法：播放轨迹并发送UDP数据
    /// 负责按时间序列发送轨迹点数据，并在多个层面检查停止条件
    /// </summary>
    /// <param name="trajectory">要播放的轨迹对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task PlayTrajectoryInternalAsync(TrajectoryAuto.Core.Entities.Trajectory trajectory, CancellationToken cancellationToken)
    {
        // 获取场景和通道信息
        var channel = await GetChannelById(trajectory.ChannelId);
        if (channel == null) return;

        var scene = await GetSceneById(channel.SceneId);
        if (scene == null) return;

        try
        {
            // 检查轨迹是否应该停止（综合检查全局、场景、通道停止状态）
            if (_playbackStateService.ShouldTrajectoryStop(trajectory.Id, trajectory.ChannelId, channel.SceneId))
            {
                _logger.LogInformation($"轨迹 {trajectory.Id} 应该停止，取消播放");
                return;
            }
            
            // 创建通道UDP客户端
            _simpleUdpManager.CreateChannelClient(trajectory.ChannelId, channel.SceneId, scene.ServerIp, scene.ServerPort);
            _logger.LogInformation($"已创建通道UDP客户端，轨迹: {trajectory.Id}, 场景: {scene.Id}, 通道: {trajectory.ChannelId}");

            do
            {
                var startTime = DateTime.Now;

                // 按时间戳顺序播放轨迹点
                var sortedPoints = trajectory.Points.OrderBy(p => p.Timestamp).ToList();

                foreach (var point in sortedPoints)
                {
                    // 检查是否已取消
                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogInformation($"轨迹播放已取消: {trajectory.Id}");
                        return;
                    }

                    // 检查轨迹是否应该停止（综合检查全局、场景、通道停止状态）
                    if (_playbackStateService.ShouldTrajectoryStop(trajectory.Id, trajectory.ChannelId, channel.SceneId))
                    {
                        _logger.LogInformation($"轨迹 {trajectory.Id} 应该停止，停止UDP发送");
                        return;
                    }

                    // 转换为物理坐标
                    var physicalX = (point.X - scene.OriginX) * scene.PixelToMeterRatio;
                    var physicalY = (scene.OriginY - point.Y) * scene.PixelToMeterRatio;
                    physicalX = Math.Round(physicalX, 3);
                    physicalY = Math.Round(physicalY, 3);

                    // 计算Z坐标值
                    double physicalZ = 0;
                    if (channel.UseZAxis)
                    {
                        // 使用正弦函数实现Z值在ZMinValue和ZMaxValue之间循环变化
                        double amplitude = (channel.ZAxisMax - channel.ZAxisMin) / 2;
                        double offset = channel.ZAxisMin + amplitude;
                        double frequency = 0.5; // 可以调整频率
                        physicalZ = Math.Round(offset + amplitude * Math.Sin(frequency * DateTime.Now.TimeOfDay.TotalSeconds), 3);
                    }

                    // 创建坐标数据
                    CoordinateData coordinateData = new CoordinateData
                    {
                        ChannelNumber = channel.ChannelNumber,
                        X = physicalX,
                        Y = physicalY,
                        Z = physicalZ,
                        Timestamp = DateTime.Now,
                        ChannelAddress = channel.Address
                    };

                    // 多层防护机制：确保停止命令能立即生效
                    
                    // 第一层：检查取消令牌（.NET标准机制）
                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogInformation($"轨迹播放已取消: {trajectory.Id}");
                        return;
                    }

                    // 第二层：检查轨迹是否应该停止（综合检查全局、场景、通道停止状态）
                    if (_playbackStateService.ShouldTrajectoryStop(trajectory.Id, trajectory.ChannelId, channel.SceneId))
                    {
                        _logger.LogInformation($"轨迹 {trajectory.Id} 应该停止，取消UDP发送");
                        return;
                    }

                    // 使用SimpleUdpManager发送坐标数据
                    bool sendResult = await _simpleUdpManager.SendWithChannelClientAsync(
                        trajectory.ChannelId, channel.SceneId, scene.ServerIp, scene.ServerPort, coordinateData);
                    
                    if (!sendResult)
                    {
                        _logger.LogWarning($"发送UDP数据失败，轨迹: {trajectory.Id}, 通道: {trajectory.ChannelId}");
                        
                        // 尝试使用直接发送方式重试一次
                        sendResult = await _simpleUdpManager.SendCoordinateDataAsync(scene.ServerIp, scene.ServerPort, coordinateData);
                        if (sendResult)
                        {
                            _logger.LogInformation($"使用直接发送方式成功发送UDP数据，轨迹: {trajectory.Id}");
                        }
                    }

                    // 精确时间控制
                    var elapsedTime = (DateTime.Now - startTime).TotalSeconds;
                    var waitTime = point.Timestamp - elapsedTime;
                    if (waitTime > 0)
                    {
                        try
                        {
                            await Task.Delay(TimeSpan.FromSeconds(waitTime), cancellationToken);
                        }
                        catch (TaskCanceledException)
                        {
                            _logger.LogInformation($"轨迹播放延迟被取消: {trajectory.Id}");
                            return;
                        }
                    }
                }

                // 如果不是循环播放，退出循环
                if (!trajectory.IsLoop)
                    break;

            } while (!cancellationToken.IsCancellationRequested);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation($"轨迹播放操作被取消: {trajectory.Id}");
        }
        catch (Exception ex)
        {
            _logger.LogError($"轨迹播放出错: {ex.Message}");
        }
        finally
        {
            // 移除当前轨迹的播放token（如果还存在）
            _playbackTokens.Remove(trajectory.Id);
            
            // 检查是否还有其他轨迹在播放
            if (_playbackTokens.Count == 0)
            {
                try
                {
                    // 停止通道UDP客户端
                    _simpleUdpManager.StopChannelClient(trajectory.ChannelId);
                    _logger.LogInformation($"已停止通道UDP客户端，轨迹: {trajectory.Id}, 通道: {trajectory.ChannelId}");
                    
                    // 重要：当所有轨迹播放结束时，重置场景和通道的播放状态
                    _playbackStateService.StopScenePlayback(channel.SceneId);
                    _playbackStateService.StopChannelPlayback(trajectory.ChannelId);
                    _logger.LogInformation($"已重置场景 {channel.SceneId} 和通道 {trajectory.ChannelId} 的播放状态");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"停止通道UDP客户端失败: {ex.Message}");
                }
            }
            else
            {
                _logger.LogInformation($"轨迹 {trajectory.Id} 播放结束，还有 {_playbackTokens.Count} 个轨迹在播放");
            }
        }
    }

    /// <summary>
    /// 获取通道信息
    /// </summary>
    private async Task<TrajectoryAuto.Core.Entities.Channel?> GetChannelById(Guid channelId)
    {
        // 这里应该调用通道服务获取通道信息
        // 由于我们没有完整的服务代码，这里使用模拟数据
        // 实际项目中应该注入IChannelService并调用其方法
        
        try
        {
            // 模拟从数据库获取通道信息
            var channelEntity = await Task.Run(() => ChannelEntity.FindById(channelId.ToString()));
            if (channelEntity == null) 
            {
                _logger.LogWarning($"找不到通道: {channelId}");
                return null;
            }
            
            return new TrajectoryAuto.Core.Entities.Channel
            {
                Id = Guid.Parse(channelEntity.Id),
                Name = channelEntity.Name ?? string.Empty,
                SceneId = Guid.Parse(channelEntity.SceneId),
                ChannelNumber = channelEntity.ChannelNumber,
                Address = channelEntity.Address ?? string.Empty
                // IsVisible 属性在 Channel 类中不存在，已移除
            };
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取通道信息失败: {channelId}, 错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 获取场景信息
    /// </summary>
    private async Task<TrajectoryAuto.Core.Entities.Scene?> GetSceneById(Guid sceneId)
    {
        // 这里应该调用场景服务获取场景信息
        // 由于我们没有完整的服务代码，这里使用模拟数据
        // 实际项目中应该注入ISceneService并调用其方法
        
        try
        {
            // 模拟从数据库获取场景信息
            var sceneEntity = await Task.Run(() => TrajectoryAuto.Infrastructure.Data.XCodeModels.SceneEntity.FindById(sceneId.ToString()));
            if (sceneEntity == null) 
            {
                _logger.LogWarning($"找不到场景: {sceneId}");
                return null;
            }
            
            return new TrajectoryAuto.Core.Entities.Scene
            {
                Id = Guid.Parse(sceneEntity.Id),
                Name = sceneEntity.Name ?? string.Empty,
                Width = sceneEntity.Width,
                Height = sceneEntity.Height,
                OriginX = sceneEntity.OriginX,
                OriginY = sceneEntity.OriginY,
                PixelToMeterRatio = sceneEntity.PixelToMeterRatio,
                ServerIp = sceneEntity.ServerIp ?? string.Empty,
                ServerPort = sceneEntity.ServerPort
            };
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取场景信息失败: {sceneId}, 错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 获取场景下的所有通道
    /// </summary>
    private async Task<List<TrajectoryAuto.Core.Entities.Channel>> GetChannelsBySceneId(Guid sceneId)
    {
        // 这里应该调用通道服务获取场景下的所有通道
        // 由于我们没有完整的服务代码，这里使用模拟数据
        // 实际项目中应该注入IChannelService并调用其方法
        
        // 模拟从数据库获取通道信息
        var channelEntities = await Task.Run(() => 
        {
            // 这里应该是调用数据库查询，由于FindAllBySceneId方法可能不存在，改为模拟实现
            var allChannels = TrajectoryAuto.Infrastructure.Data.XCodeModels.ChannelEntity.FindAll();
            return allChannels.Where(c => c.SceneId == sceneId.ToString()).ToList();
        });
        if (channelEntities == null || channelEntities.Count == 0) return new List<TrajectoryAuto.Core.Entities.Channel>();
        
        var channels = new List<TrajectoryAuto.Core.Entities.Channel>();
        foreach (var entity in channelEntities)
        {
            channels.Add(new TrajectoryAuto.Core.Entities.Channel
            {
                Id = Guid.Parse(entity.Id),
                Name = entity.Name,
                SceneId = Guid.Parse(entity.SceneId),
                ChannelNumber = entity.ChannelNumber,
                Address = entity.Address
            });
        }
        
        return channels;
    }

    /// <summary>
    /// 使用场景级别UDP连接播放轨迹（已弃用，重定向到通道级别连接）
    /// </summary>
    /// <param name="trajectory">轨迹对象</param>
    /// <param name="sceneId">场景ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="enhancedCommunicationService">增强通信服务实例</param>
    private async Task PlayTrajectoryWithSceneConnectionAsync(TrajectoryAuto.Core.Entities.Trajectory trajectory, Guid sceneId, CancellationToken cancellationToken, IEnhancedCommunicationService? enhancedCommunicationService = null)
    {
        // 场景级别连接已不再支持，重定向到通道级别连接
        _logger.LogWarning("场景级别UDP连接已不再支持，轨迹 {TrajectoryId} 将使用通道级别连接播放", trajectory.Id);
        
        // 重定向到通道级别连接播放，传入服务作用域实例
        await PlayTrajectoryWithChannelConnectionAsync(trajectory, trajectory.ChannelId, sceneId, cancellationToken, enhancedCommunicationService);
    }

    /// <summary>
    /// 使用通道级别UDP连接播放轨迹
    /// </summary>
    /// <param name="trajectory">轨迹对象</param>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">场景ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="enhancedCommunicationService">增强通信服务（可选，用于服务作用域）</param>
    private async Task PlayTrajectoryWithChannelConnectionAsync(TrajectoryAuto.Core.Entities.Trajectory trajectory, Guid channelId, Guid sceneId, CancellationToken cancellationToken, IEnhancedCommunicationService? enhancedCommunicationService = null)
    {
        // 获取场景和通道信息
        var channel = await GetChannelById(channelId);
        if (channel == null) return;

        var scene = await GetSceneById(sceneId);
        if (scene == null) return;

        try
        {
            // 检查轨迹是否应该停止
            if (_playbackStateService.ShouldTrajectoryStop(trajectory.Id, channelId, sceneId))
            {
                _logger.LogInformation($"轨迹 {trajectory.Id} 应该停止，取消播放");
                return;
            }
            
            // 创建通道UDP客户端
            bool clientCreated = _simpleUdpManager.CreateChannelClient(channelId, sceneId, scene.ServerIp, scene.ServerPort);
            _logger.LogInformation($"通道UDP客户端创建{(clientCreated ? "成功" : "失败")}，轨迹: {trajectory.Id}, 场景: {sceneId}, 通道: {channelId}, 目标: {scene.ServerIp}:{scene.ServerPort}");
            
            if (!clientCreated)
            {
                _logger.LogError($"无法创建通道UDP客户端，轨迹播放可能无法正常工作，轨迹: {trajectory.Id}, 通道: {channelId}");
                // 尝试重新创建
                clientCreated = _simpleUdpManager.CreateChannelClient(channelId, sceneId, scene.ServerIp, scene.ServerPort);
                _logger.LogInformation($"重试创建通道UDP客户端{(clientCreated ? "成功" : "失败")}");
            }
            
            _logger.LogInformation($"开始使用通道级别连接播放轨迹: {trajectory.Id}, 通道: {channelId}");

            do
            {
                var startTime = DateTime.Now;
                var sortedPoints = trajectory.Points.OrderBy(p => p.Timestamp).ToList();

                foreach (var point in sortedPoints)
                {
                    // 检查取消条件
                    if (cancellationToken.IsCancellationRequested ||
                        _playbackStateService.ShouldTrajectoryStop(trajectory.Id, channelId, sceneId))
                    {
                        _logger.LogInformation($"轨迹播放已停止: {trajectory.Id}");
                        return;
                    }

                    // 计算物理坐标
                    var physicalX = Math.Round((point.X - scene.OriginX) * scene.PixelToMeterRatio, 3);
                    var physicalY = Math.Round((scene.OriginY - point.Y) * scene.PixelToMeterRatio, 3);
                    
                    double physicalZ = 0;
                    if (channel.UseZAxis)
                    {
                        double amplitude = (channel.ZAxisMax - channel.ZAxisMin) / 2;
                        double offset = channel.ZAxisMin + amplitude;
                        physicalZ = Math.Round(offset + amplitude * Math.Sin(0.5 * DateTime.Now.TimeOfDay.TotalSeconds), 3);
                    }

                    var coordinateData = new CoordinateData
                    {
                        ChannelNumber = channel.ChannelNumber,
                        X = physicalX,
                        Y = physicalY,
                        Z = physicalZ,
                        Timestamp = DateTime.Now,
                        ChannelAddress = channel.Address
                    };

                    // 使用SimpleUdpManager发送坐标数据
                    _logger.LogDebug($"尝试发送UDP数据，轨迹: {trajectory.Id}, 通道: {channelId}, 坐标: X={coordinateData.X}, Y={coordinateData.Y}, Z={coordinateData.Z}");
                    
                    bool sendSuccess = await _simpleUdpManager.SendWithChannelClientAsync(
                        channelId, sceneId, scene.ServerIp, scene.ServerPort, coordinateData);
                    
                    if (!sendSuccess)
                    {
                        _logger.LogWarning($"通道级别连接发送数据失败: 轨迹={trajectory.Id}, 通道={channelId}, 场景={sceneId}, 目标={scene.ServerIp}:{scene.ServerPort}");
                        
                        // 尝试使用直接发送方式重试一次
                        _logger.LogInformation($"尝试使用直接发送方式重试发送UDP数据，轨迹: {trajectory.Id}");
                        sendSuccess = await _simpleUdpManager.SendCoordinateDataAsync(scene.ServerIp, scene.ServerPort, coordinateData);
                        
                        if (sendSuccess)
                        {
                            _logger.LogInformation($"使用直接发送方式成功发送UDP数据，轨迹: {trajectory.Id}");
                        }
                        else
                        {
                            _logger.LogError($"两种方式发送UDP数据均失败，轨迹: {trajectory.Id}, 通道: {channelId}, 目标: {scene.ServerIp}:{scene.ServerPort}");
                        }
                    }
                    else
                    {
                        _logger.LogDebug($"成功发送UDP数据，轨迹: {trajectory.Id}, 通道: {channelId}");
                    }

                    // 时间控制
                    var elapsedTime = DateTime.Now - startTime;
                    var waitTime = Math.Max(point.Timestamp - elapsedTime.TotalSeconds, 0.01);
                    await Task.Delay(TimeSpan.FromSeconds(waitTime), cancellationToken);
                }
                
                // 如果不是循环播放，退出循环
                if (!trajectory.IsLoop)
                    break;
                    
            } while (!cancellationToken.IsCancellationRequested &&
                     !_playbackStateService.ShouldTrajectoryStop(trajectory.Id, channelId, sceneId));
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation($"轨迹播放被取消: {trajectory.Id}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"通道级别连接播放轨迹失败: {trajectory.Id}");
        }
        finally
        {
            // 移除当前轨迹的播放 token（如果还存在）
            _playbackTokens.Remove(trajectory.Id);
            
            // 检查是否还有其他轨迹在播放
            if (_playbackTokens.Count == 0)
            {
                try
                {
                    // 停止通道UDP客户端
                    _simpleUdpManager.StopChannelClient(channelId);
                    _logger.LogInformation($"已停止通道UDP客户端，轨迹: {trajectory.Id}, 通道: {channelId}");
                    
                    // 重要：当所有轨迹播放结束时，重置场景和通道的播放状态
                    _playbackStateService.StopScenePlayback(sceneId);
                    _playbackStateService.StopChannelPlayback(channelId);
                    _logger.LogInformation($"已重置场景 {sceneId} 和通道 {channelId} 的播放状态");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"停止通道UDP客户端失败: {trajectory.Id}");
                }
            }
            else
            {
                _logger.LogInformation($"轨迹 {trajectory.Id} 播放结束，还有 {_playbackTokens.Count} 个轨迹在播放");
            }
        }
    }
    
    #endregion
}

#region 请求模型类

/// <summary>
/// 播放轨迹请求模型
/// </summary>
public class PlayTrajectoryRequest
{
    /// <summary>
    /// 轨迹ID
    /// </summary>
    public Guid TrajectoryId { get; set; }
}

/// <summary>
/// 播放通道轨迹请求模型
/// </summary>
public class PlayChannelRequest
{
    /// <summary>
    /// 通道ID
    /// </summary>
    public Guid ChannelId { get; set; }
}

/// <summary>
/// 播放场景轨迹请求模型
/// </summary>
public class PlaySceneRequest
{
    /// <summary>
    /// 场景ID
    /// </summary>
    public Guid SceneId { get; set; }
}

#endregion