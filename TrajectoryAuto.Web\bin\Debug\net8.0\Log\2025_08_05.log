﻿#Software: TrajectoryAuto.Web
#ProcessID: 22704 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:51:05.2180000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:04:55.157  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:04:55.165  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:04:55.166  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:04:55.166  1 N - TrajectoryAuto.Web 
16:04:55.166  1 N - 保存配置 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\Config\Core.config
16:04:55.167  1 N - 保存配置 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\Config\XCode.config
16:04:55.174  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:04:55.174  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:04:55.174  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:04:55.584  1 N - 创建数据库：TrajectoryAuto
16:04:55.585  1 N - 创建数据库：D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db
16:04:55.588  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:04:55.599  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:04:55.644  1 N - [TrajectoryAuto] select * from sqlite_master
16:04:55.654  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:04:55.655  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:04:55.713  1 N - 创建表：Scenes(场景表)
16:04:55.723  1 N - [TrajectoryAuto] Create Table Scenes(
	Id nvarchar(50) Primary Key COLLATE NOCASE,
	Name nvarchar(100) NOT NULL DEFAULT '' COLLATE NOCASE,
	Width int NOT NULL DEFAULT 0,
	Height int NOT NULL DEFAULT 0,
	ServerIp nvarchar(50) NULL COLLATE NOCASE,
	ServerPort int NOT NULL DEFAULT 0,
	BackgroundImagePath nvarchar(500) NULL COLLATE NOCASE,
	OriginX real NOT NULL DEFAULT 0,
	OriginY real NOT NULL DEFAULT 0,
	PixelToMeterRatio real NOT NULL DEFAULT 0,
	CreateTime datetime NULL,
	UpdateTime datetime NULL
)
16:04:55.740  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 -2
16:04:55.746  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:04:55.765  1 N - [TrajectoryAuto]待检查数据表：Channels
16:04:55.765  1 N - [TrajectoryAuto] select * from sqlite_master
16:04:55.775  1 N - 创建表：Channels(通道表)
16:04:55.777  1 N - [TrajectoryAuto] Create Table Channels(
	Id nvarchar(50) Primary Key COLLATE NOCASE,
	Name nvarchar(100) NOT NULL DEFAULT '' COLLATE NOCASE,
	ChannelNumber int NOT NULL DEFAULT 0,
	IpAddress nvarchar(50) NULL COLLATE NOCASE,
	Port int NOT NULL DEFAULT 0,
	Color nvarchar(20) NULL COLLATE NOCASE,
	IsActive bit NOT NULL DEFAULT 0,
	SceneId nvarchar(50) NOT NULL DEFAULT '' COLLATE NOCASE,
	CreateTime datetime NULL,
	UpdateTime datetime NULL
)
16:04:55.788  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 -2
16:04:55.789  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:04:55.799  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:04:55.800  1 N - [TrajectoryAuto] select * from sqlite_master
16:04:55.807  1 N - 创建表：Trajectories(轨迹表)
16:04:55.816  1 N - [TrajectoryAuto] Create Table Trajectories(
	Id nvarchar(50) Primary Key COLLATE NOCASE,
	Name nvarchar(100) NOT NULL DEFAULT '' COLLATE NOCASE,
	Type int NOT NULL DEFAULT 0,
	Duration real NOT NULL DEFAULT 0,
	IsLoop bit NOT NULL DEFAULT 0,
	ChannelId nvarchar(50) NOT NULL DEFAULT '' COLLATE NOCASE,
	CreateTime datetime NULL,
	UpdateTime datetime NULL
)
16:04:55.827  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 -2
16:04:55.828  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:04:55.837  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:04:55.838  1 N - [TrajectoryAuto] select * from sqlite_master
16:04:55.846  1 N - 创建表：TrajectoryPoints(轨迹点表)
16:04:55.848  1 N - [TrajectoryAuto] Create Table TrajectoryPoints(
	Id nvarchar(50) Primary Key COLLATE NOCASE,
	X real NOT NULL DEFAULT 0,
	Y real NOT NULL DEFAULT 0,
	Timestamp real NOT NULL DEFAULT 0,
	[Order] int NOT NULL DEFAULT 0,
	TrajectoryId nvarchar(50) NOT NULL DEFAULT '' COLLATE NOCASE
)
16:04:55.860  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 -2
16:04:55.861  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:04:55.869  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 22908 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:01:30.1250000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:15:20.070  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:15:20.076  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:15:20.076  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:15:20.076  1 N - TrajectoryAuto.Web 
16:15:20.076  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:15:20.076  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:15:20.077  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:15:20.748  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:15:20.759  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:15:20.796  1 N - [TrajectoryAuto] select * from sqlite_master
16:15:20.812  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:15:20.813  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:15:20.916  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 0
16:15:20.927  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:15:20.945  1 N - [TrajectoryAuto]待检查数据表：Channels
16:15:20.946  1 N - [TrajectoryAuto] select * from sqlite_master
16:15:20.959  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 0
16:15:20.961  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:15:20.974  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:15:20.975  1 N - [TrajectoryAuto] select * from sqlite_master
16:15:20.986  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 0
16:15:20.987  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:15:20.999  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:15:20.999  1 N - [TrajectoryAuto] select * from sqlite_master
16:15:21.011  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 0
16:15:21.012  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:15:21.021  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
16:15:21.389  1 N - System.IO.IOException: Failed to bind to address https://127.0.0.1:55483: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 81
16:15:21.392  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 1036 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll --urls https://localhost:5001;http://localhost:5000
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:01:59.6400000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:15:49.589  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:15:49.595  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:15:49.595  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:15:49.596  1 N - TrajectoryAuto.Web 
16:15:49.596  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:15:49.596  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:15:49.596  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:15:50.460  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:15:50.470  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:15:50.506  1 N - [TrajectoryAuto] select * from sqlite_master
16:15:50.524  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:15:50.525  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:15:50.644  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
16:15:50.658  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:15:50.688  1 N - [TrajectoryAuto]待检查数据表：Channels
16:15:50.690  1 N - [TrajectoryAuto] select * from sqlite_master
16:15:50.707  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
16:15:50.708  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:15:50.743  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:15:50.747  1 N - [TrajectoryAuto] select * from sqlite_master
16:15:50.758  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 10
16:15:50.759  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:15:50.775  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:15:50.781  1 N - [TrajectoryAuto] select * from sqlite_master
16:15:50.803  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 767
16:15:50.804  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:15:50.836  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 29368 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:27:58.4370000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:41:48.378  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:41:48.387  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:41:48.388  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:41:48.388  1 N - TrajectoryAuto.Web 
16:41:48.388  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:41:48.388  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:41:48.388  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:41:49.007  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:41:49.017  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:41:49.057  1 N - [TrajectoryAuto] select * from sqlite_master
16:41:49.069  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:41:49.069  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:41:49.168  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
16:41:49.175  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:41:49.195  1 N - [TrajectoryAuto]待检查数据表：Channels
16:41:49.196  1 N - [TrajectoryAuto] select * from sqlite_master
16:41:49.206  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
16:41:49.207  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:41:49.216  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:41:49.217  1 N - [TrajectoryAuto] select * from sqlite_master
16:41:49.225  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 10
16:41:49.226  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:41:49.236  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:41:49.237  1 N - [TrajectoryAuto] select * from sqlite_master
16:41:49.247  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 767
16:41:49.247  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:41:49.258  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 24344 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:48:52.3750000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:02:42.298  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:02:42.312  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:02:42.313  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:02:42.313  1 N - TrajectoryAuto.Web 
17:02:42.313  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:02:42.313  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:02:42.314  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:02:43.375  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:02:43.389  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:02:43.440  1 N - [TrajectoryAuto] select * from sqlite_master
17:02:43.458  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:02:43.459  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:02:43.585  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
17:02:43.593  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:02:43.613  1 N - [TrajectoryAuto]待检查数据表：Channels
17:02:43.613  1 N - [TrajectoryAuto] select * from sqlite_master
17:02:43.629  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
17:02:43.630  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:02:43.646  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:02:43.646  1 N - [TrajectoryAuto] select * from sqlite_master
17:02:43.658  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 10
17:02:43.659  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:02:43.672  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:02:43.673  1 N - [TrajectoryAuto] select * from sqlite_master
17:02:43.686  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 767
17:02:43.688  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:02:43.699  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
17:02:44.188  1 N - System.IO.IOException: Failed to bind to address https://127.0.0.1:55483: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 115
17:02:44.191  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 13712 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:02:14.0150000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:19:43.965  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
18:19:43.972  1 N - NewLife组件核心库 ©2002-2024 NewLife
18:19:43.973  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
18:19:43.973  1 N - TrajectoryAuto.Web 
18:19:43.973  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
18:19:43.974  1 N - NewLife数据中间件 ©2002-2024 NewLife
18:19:43.974  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
18:19:44.438  1 N - [TrajectoryAuto]待检查数据表：Scenes
18:19:44.451  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
18:19:44.494  1 N - [TrajectoryAuto] select * from sqlite_master
18:19:44.504  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
18:19:44.504  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
18:19:44.581  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
18:19:44.586  1 N - [TrajectoryAuto] Select Count(*) From Scenes
18:19:44.599  1 N - [TrajectoryAuto]待检查数据表：Channels
18:19:44.599  1 N - [TrajectoryAuto] select * from sqlite_master
18:19:44.606  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
18:19:44.607  1 N - [TrajectoryAuto] Select Count(*) From Channels
18:19:44.615  1 N - [TrajectoryAuto]待检查数据表：Trajectories
18:19:44.615  1 N - [TrajectoryAuto] select * from sqlite_master
18:19:44.623  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 10
18:19:44.624  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
18:19:44.632  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
18:19:44.632  1 N - [TrajectoryAuto] select * from sqlite_master
18:19:44.641  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 767
18:19:44.641  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
18:19:44.647  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 10820 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:23:07.7960000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:40:37.755  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
18:40:37.758  1 N - NewLife组件核心库 ©2002-2024 NewLife
18:40:37.759  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
18:40:37.759  1 N - TrajectoryAuto.Web 
18:40:37.759  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
18:40:37.759  1 N - NewLife数据中间件 ©2002-2024 NewLife
18:40:37.759  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
18:40:38.199  1 N - [TrajectoryAuto]待检查数据表：Scenes
18:40:38.215  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
18:40:38.277  1 N - [TrajectoryAuto] select * from sqlite_master
18:40:38.286  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
18:40:38.286  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
18:40:38.360  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
18:40:38.365  1 N - [TrajectoryAuto] Select Count(*) From Scenes
18:40:38.377  1 N - [TrajectoryAuto]待检查数据表：Channels
18:40:38.378  1 N - [TrajectoryAuto] select * from sqlite_master
18:40:38.386  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
18:40:38.387  1 N - [TrajectoryAuto] Select Count(*) From Channels
18:40:38.396  1 N - [TrajectoryAuto]待检查数据表：Trajectories
18:40:38.397  1 N - [TrajectoryAuto] select * from sqlite_master
18:40:38.407  1 N - [TrajectoryAuto] Alter Table Trajectories Add LineColor nvarchar(20) NULL COLLATE NOCASE
18:40:38.418  1 N - [TrajectoryAuto] Alter Table Trajectories Add LineWidth int NULL
18:40:38.427  1 N - [TrajectoryAuto] Alter Table Trajectories Add LineStyle int NULL
18:40:38.438  1 N - [TrajectoryAuto] Alter Table Trajectories Add LineOpacity real NULL
18:40:38.449  1 N - [TrajectoryAuto] Alter Table Trajectories Add ShowPoints bit NULL
18:40:38.460  1 N - [TrajectoryAuto] Alter Table Trajectories Add PointSize int NULL
18:40:38.470  1 N - [TrajectoryAuto] Alter Table Trajectories Add PointColor nvarchar(20) NULL COLLATE NOCASE
18:40:38.479  1 N - [TrajectoryAuto] Alter Table Trajectories Add ShowEndpoints bit NULL
18:40:38.489  1 N - [TrajectoryAuto] Alter Table Trajectories Add StartPointColor nvarchar(20) NULL COLLATE NOCASE
18:40:38.499  1 N - [TrajectoryAuto] Alter Table Trajectories Add EndPointColor nvarchar(20) NULL COLLATE NOCASE
18:40:38.510  1 N - [TrajectoryAuto] Alter Table Trajectories Add EnableShadow bit NULL
18:40:38.519  1 N - [TrajectoryAuto] Alter Table Trajectories Add ShadowColor nvarchar(30) NULL COLLATE NOCASE
18:40:38.529  1 N - [TrajectoryAuto] Alter Table Trajectories Add ShadowBlur int NULL
18:40:38.539  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 10
18:40:38.540  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
18:40:38.548  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
18:40:38.549  1 N - [TrajectoryAuto] select * from sqlite_master
18:40:38.556  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 767
18:40:38.557  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
18:40:38.563  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 14284 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:26:41.6090000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:44:11.562  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
18:44:11.565  1 N - NewLife组件核心库 ©2002-2024 NewLife
18:44:11.566  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
18:44:11.566  1 N - TrajectoryAuto.Web 
18:44:11.566  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
18:44:11.566  1 N - NewLife数据中间件 ©2002-2024 NewLife
18:44:11.566  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
18:44:11.882  1 N - [TrajectoryAuto]待检查数据表：Scenes
18:44:11.887  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
18:44:11.904  1 N - [TrajectoryAuto] select * from sqlite_master
18:44:11.910  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
18:44:11.911  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
18:44:11.962  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
18:44:11.966  1 N - [TrajectoryAuto] Select Count(*) From Scenes
18:44:11.976  1 N - [TrajectoryAuto]待检查数据表：Channels
18:44:11.977  1 N - [TrajectoryAuto] select * from sqlite_master
18:44:11.983  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
18:44:11.984  1 N - [TrajectoryAuto] Select Count(*) From Channels
18:44:11.991  1 N - [TrajectoryAuto]待检查数据表：Trajectories
18:44:11.992  1 N - [TrajectoryAuto] select * from sqlite_master
18:44:12.000  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 10
18:44:12.001  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
18:44:12.007  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
18:44:12.008  1 N - [TrajectoryAuto] select * from sqlite_master
18:44:12.015  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 767
18:44:12.015  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
18:44:12.023  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 17940 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:29:24.5460000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:46:54.506  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
18:46:54.509  1 N - NewLife组件核心库 ©2002-2024 NewLife
18:46:54.510  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
18:46:54.510  1 N - TrajectoryAuto.Web 
18:46:54.510  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
18:46:54.510  1 N - NewLife数据中间件 ©2002-2024 NewLife
18:46:54.510  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
18:46:54.870  1 N - [TrajectoryAuto]待检查数据表：Scenes
18:46:54.876  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
18:46:54.893  1 N - [TrajectoryAuto] select * from sqlite_master
18:46:54.901  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
18:46:54.901  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
18:46:54.960  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
18:46:54.965  1 N - [TrajectoryAuto] Select Count(*) From Scenes
18:46:54.977  1 N - [TrajectoryAuto]待检查数据表：Channels
18:46:54.978  1 N - [TrajectoryAuto] select * from sqlite_master
18:46:54.985  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
18:46:54.986  1 N - [TrajectoryAuto] Select Count(*) From Channels
18:46:54.994  1 N - [TrajectoryAuto]待检查数据表：Trajectories
18:46:54.995  1 N - [TrajectoryAuto] select * from sqlite_master
18:46:55.003  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 10
18:46:55.003  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
18:46:55.011  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
18:46:55.012  1 N - [TrajectoryAuto] select * from sqlite_master
18:46:55.019  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 767
18:46:55.019  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
18:46:55.025  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21120 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:49:14.6400000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
19:06:47.576  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
19:06:47.581  1 N - NewLife组件核心库 ©2002-2024 NewLife
19:06:47.581  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
19:06:47.581  1 N - TrajectoryAuto.Web 
19:06:47.581  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
19:06:47.581  1 N - NewLife数据中间件 ©2002-2024 NewLife
19:06:47.581  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
19:06:48.029  1 N - [TrajectoryAuto]待检查数据表：Scenes
19:06:48.036  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
19:06:48.058  1 N - [TrajectoryAuto] select * from sqlite_master
19:06:48.067  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
19:06:48.068  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
19:06:48.132  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
19:06:48.138  1 N - [TrajectoryAuto] Select Count(*) From Scenes
19:06:48.152  1 N - [TrajectoryAuto]待检查数据表：Channels
19:06:48.152  1 N - [TrajectoryAuto] select * from sqlite_master
19:06:48.162  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
19:06:48.162  1 N - [TrajectoryAuto] Select Count(*) From Channels
19:06:48.171  1 N - [TrajectoryAuto]待检查数据表：Trajectories
19:06:48.172  1 N - [TrajectoryAuto] select * from sqlite_master
19:06:48.182  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 10
19:06:48.183  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
19:06:48.192  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
19:06:48.192  1 N - [TrajectoryAuto] select * from sqlite_master
19:06:48.200  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 767
19:06:48.201  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
19:06:48.209  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23060 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:56:16.9060000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
19:13:49.846  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
19:13:49.850  1 N - NewLife组件核心库 ©2002-2024 NewLife
19:13:49.850  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
19:13:49.851  1 N - TrajectoryAuto.Web 
19:13:49.851  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
19:13:49.851  1 N - NewLife数据中间件 ©2002-2024 NewLife
19:13:49.851  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
19:13:50.250  1 N - [TrajectoryAuto]待检查数据表：Scenes
19:13:50.257  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
19:13:50.275  1 N - [TrajectoryAuto] select * from sqlite_master
19:13:50.283  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
19:13:50.284  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
19:13:50.347  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
19:13:50.353  1 N - [TrajectoryAuto] Select Count(*) From Scenes
19:13:50.366  1 N - [TrajectoryAuto]待检查数据表：Channels
19:13:50.366  1 N - [TrajectoryAuto] select * from sqlite_master
19:13:50.373  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
19:13:50.374  1 N - [TrajectoryAuto] Select Count(*) From Channels
19:13:50.384  1 N - [TrajectoryAuto]待检查数据表：Trajectories
19:13:50.384  1 N - [TrajectoryAuto] select * from sqlite_master
19:13:50.392  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 11
19:13:50.393  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
19:13:50.401  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
19:13:50.402  1 N - [TrajectoryAuto] select * from sqlite_master
19:13:50.408  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 1,126
19:13:50.409  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
19:13:50.417  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 20976 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:01:11.8430000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
19:18:44.786  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
19:18:44.790  1 N - NewLife组件核心库 ©2002-2024 NewLife
19:18:44.790  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
19:18:44.790  1 N - TrajectoryAuto.Web 
19:18:44.790  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
19:18:44.790  1 N - NewLife数据中间件 ©2002-2024 NewLife
19:18:44.791  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
19:18:45.122  1 N - [TrajectoryAuto]待检查数据表：Scenes
19:18:45.128  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
19:18:45.148  1 N - [TrajectoryAuto] select * from sqlite_master
19:18:45.156  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
19:18:45.156  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
19:18:45.210  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
19:18:45.215  1 N - [TrajectoryAuto] Select Count(*) From Scenes
19:18:45.228  1 N - [TrajectoryAuto]待检查数据表：Channels
19:18:45.229  1 N - [TrajectoryAuto] select * from sqlite_master
19:18:45.237  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
19:18:45.237  1 N - [TrajectoryAuto] Select Count(*) From Channels
19:18:45.247  1 N - [TrajectoryAuto]待检查数据表：Trajectories
19:18:45.248  1 N - [TrajectoryAuto] select * from sqlite_master
19:18:45.257  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 11
19:18:45.258  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
19:18:45.268  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
19:18:45.268  1 N - [TrajectoryAuto] select * from sqlite_master
19:18:45.277  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 1,126
19:18:45.278  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
19:18:45.285  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 20160 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:03:16.9530000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
19:20:49.890  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
19:20:49.893  1 N - NewLife组件核心库 ©2002-2024 NewLife
19:20:49.894  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
19:20:49.894  1 N - TrajectoryAuto.Web 
19:20:49.894  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
19:20:49.894  1 N - NewLife数据中间件 ©2002-2024 NewLife
19:20:49.894  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
19:20:50.229  1 N - [TrajectoryAuto]待检查数据表：Scenes
19:20:50.235  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
19:20:50.254  1 N - [TrajectoryAuto] select * from sqlite_master
19:20:50.261  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
19:20:50.262  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
19:20:50.315  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
19:20:50.320  1 N - [TrajectoryAuto] Select Count(*) From Scenes
19:20:50.332  1 N - [TrajectoryAuto]待检查数据表：Channels
19:20:50.332  1 N - [TrajectoryAuto] select * from sqlite_master
19:20:50.341  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
19:20:50.341  1 N - [TrajectoryAuto] Select Count(*) From Channels
19:20:50.352  1 N - [TrajectoryAuto]待检查数据表：Trajectories
19:20:50.353  1 N - [TrajectoryAuto] select * from sqlite_master
19:20:50.360  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 11
19:20:50.361  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
19:20:50.369  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
19:20:50.370  1 N - [TrajectoryAuto] select * from sqlite_master
19:20:50.378  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 1,126
19:20:50.378  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
19:20:50.387  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23200 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:35:01.1090000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
19:52:34.040  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
19:52:34.044  1 N - NewLife组件核心库 ©2002-2024 NewLife
19:52:34.044  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
19:52:34.044  1 N - TrajectoryAuto.Web 
19:52:34.044  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
19:52:34.044  1 N - NewLife数据中间件 ©2002-2024 NewLife
19:52:34.044  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
19:52:34.454  1 N - [TrajectoryAuto]待检查数据表：Scenes
19:52:34.462  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
19:52:34.481  1 N - [TrajectoryAuto] select * from sqlite_master
19:52:34.490  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
19:52:34.491  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
19:52:34.543  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
19:52:34.547  1 N - [TrajectoryAuto] Select Count(*) From Scenes
19:52:34.559  1 N - [TrajectoryAuto]待检查数据表：Channels
19:52:34.560  1 N - [TrajectoryAuto] select * from sqlite_master
19:52:34.568  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
19:52:34.569  1 N - [TrajectoryAuto] Select Count(*) From Channels
19:52:34.578  1 N - [TrajectoryAuto]待检查数据表：Trajectories
19:52:34.579  1 N - [TrajectoryAuto] select * from sqlite_master
19:52:34.588  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 11
19:52:34.589  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
19:52:34.598  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
19:52:34.598  1 N - [TrajectoryAuto] select * from sqlite_master
19:52:34.607  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 1,126
19:52:34.608  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
19:52:34.615  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 22376 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:46:39.3900000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:04:12.333  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:04:12.336  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:04:12.337  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:04:12.337  1 N - TrajectoryAuto.Web 
20:04:12.337  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:04:12.337  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:04:12.337  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:04:12.758  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:04:12.764  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:04:12.783  1 N - [TrajectoryAuto] select * from sqlite_master
20:04:12.792  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:04:12.793  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:04:12.851  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
20:04:12.855  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:04:12.867  1 N - [TrajectoryAuto]待检查数据表：Channels
20:04:12.868  1 N - [TrajectoryAuto] select * from sqlite_master
20:04:12.876  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
20:04:12.877  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:04:12.886  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:04:12.887  1 N - [TrajectoryAuto] select * from sqlite_master
20:04:12.895  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
20:04:12.896  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:04:12.906  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:04:12.907  1 N - [TrajectoryAuto] select * from sqlite_master
20:04:12.920  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
20:04:12.920  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:04:12.930  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21604 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:47:39.7180000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:05:12.662  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:05:12.665  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:05:12.665  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:05:12.665  1 N - TrajectoryAuto.Web 
20:05:12.665  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:05:12.665  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:05:12.666  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:05:13.079  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:05:13.085  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:05:13.105  1 N - [TrajectoryAuto] select * from sqlite_master
20:05:13.113  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:05:13.114  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:05:13.169  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
20:05:13.174  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:05:13.187  1 N - [TrajectoryAuto]待检查数据表：Channels
20:05:13.188  1 N - [TrajectoryAuto] select * from sqlite_master
20:05:13.197  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
20:05:13.198  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:05:13.208  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:05:13.209  1 N - [TrajectoryAuto] select * from sqlite_master
20:05:13.218  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
20:05:13.219  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:05:13.229  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:05:13.230  1 N - [TrajectoryAuto] select * from sqlite_master
20:05:13.239  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
20:05:13.239  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:05:13.247  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 12688 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:07:48.3750000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:25:21.296  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:25:21.308  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:25:21.309  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:25:21.309  1 N - TrajectoryAuto.Web 
20:25:21.309  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:25:21.309  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:25:21.310  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:25:22.253  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:25:22.264  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:25:22.300  1 N - [TrajectoryAuto] select * from sqlite_master
20:25:22.317  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:25:22.318  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:25:22.415  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
20:25:22.423  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:25:22.444  1 N - [TrajectoryAuto]待检查数据表：Channels
20:25:22.444  1 N - [TrajectoryAuto] select * from sqlite_master
20:25:22.457  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
20:25:22.457  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:25:22.471  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:25:22.472  1 N - [TrajectoryAuto] select * from sqlite_master
20:25:22.484  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
20:25:22.484  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:25:22.497  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:25:22.497  1 N - [TrajectoryAuto] select * from sqlite_master
20:25:22.508  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
20:25:22.509  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:25:22.521  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 2620 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:12:40.2500000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
20:30:13.173  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
20:30:13.177  1 N - NewLife组件核心库 ©2002-2024 NewLife
20:30:13.178  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
20:30:13.178  1 N - TrajectoryAuto.Web 
20:30:13.178  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
20:30:13.178  1 N - NewLife数据中间件 ©2002-2024 NewLife
20:30:13.179  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
20:30:13.819  1 N - [TrajectoryAuto]待检查数据表：Scenes
20:30:13.828  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
20:30:13.859  1 N - [TrajectoryAuto] select * from sqlite_master
20:30:13.872  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
20:30:13.873  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
20:30:13.954  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
20:30:13.960  1 N - [TrajectoryAuto] Select Count(*) From Scenes
20:30:13.977  1 N - [TrajectoryAuto]待检查数据表：Channels
20:30:13.978  1 N - [TrajectoryAuto] select * from sqlite_master
20:30:13.989  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
20:30:13.989  1 N - [TrajectoryAuto] Select Count(*) From Channels
20:30:14.001  1 N - [TrajectoryAuto]待检查数据表：Trajectories
20:30:14.002  1 N - [TrajectoryAuto] select * from sqlite_master
20:30:14.014  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
20:30:14.014  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
20:30:14.026  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
20:30:14.027  1 N - [TrajectoryAuto] select * from sqlite_master
20:30:14.038  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
20:30:14.038  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
20:30:14.048  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 21664 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:54:42.1870000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:12:15.121  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:12:15.126  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:12:15.127  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:12:15.127  1 N - TrajectoryAuto.Web 
21:12:15.127  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:12:15.127  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:12:15.127  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:12:15.574  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:12:15.580  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:12:15.600  1 N - [TrajectoryAuto] select * from sqlite_master
21:12:15.609  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:12:15.610  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:12:15.670  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
21:12:15.675  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:12:15.688  1 N - [TrajectoryAuto]待检查数据表：Channels
21:12:15.689  1 N - [TrajectoryAuto] select * from sqlite_master
21:12:15.699  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
21:12:15.700  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:12:15.710  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:12:15.711  1 N - [TrajectoryAuto] select * from sqlite_master
21:12:15.720  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
21:12:15.721  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:12:15.729  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:12:15.730  1 N - [TrajectoryAuto] select * from sqlite_master
21:12:15.738  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
21:12:15.738  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:12:15.748  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 24340 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:05:56.3120000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:23:29.238  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:23:29.243  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:23:29.244  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:23:29.244  1 N - TrajectoryAuto.Web 
21:23:29.244  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:23:29.244  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:23:29.244  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:23:29.731  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:23:29.740  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:23:29.763  1 N - [TrajectoryAuto] select * from sqlite_master
21:23:29.775  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:23:29.776  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:23:29.837  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
21:23:29.842  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:23:29.857  1 N - [TrajectoryAuto]待检查数据表：Channels
21:23:29.857  1 N - [TrajectoryAuto] select * from sqlite_master
21:23:29.866  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
21:23:29.867  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:23:29.878  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:23:29.879  1 N - [TrajectoryAuto] select * from sqlite_master
21:23:29.888  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
21:23:29.888  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:23:29.898  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:23:29.899  1 N - [TrajectoryAuto] select * from sqlite_master
21:23:29.912  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
21:23:29.912  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:23:29.923  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 17984 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:08:44.4840000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:26:17.413  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:26:17.417  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:26:17.418  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:26:17.418  1 N - TrajectoryAuto.Web 
21:26:17.418  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:26:17.418  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:26:17.418  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:26:17.841  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:26:17.848  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:26:17.869  1 N - [TrajectoryAuto] select * from sqlite_master
21:26:17.879  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:26:17.880  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:26:17.940  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
21:26:17.945  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:26:17.959  1 N - [TrajectoryAuto]待检查数据表：Channels
21:26:17.960  1 N - [TrajectoryAuto] select * from sqlite_master
21:26:17.968  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
21:26:17.969  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:26:17.980  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:26:17.981  1 N - [TrajectoryAuto] select * from sqlite_master
21:26:17.992  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
21:26:17.993  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:26:18.004  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:26:18.004  1 N - [TrajectoryAuto] select * from sqlite_master
21:26:18.013  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
21:26:18.014  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:26:18.023  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23604 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:13:26.4840000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:30:59.417  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:30:59.421  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:30:59.422  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:30:59.422  1 N - TrajectoryAuto.Web 
21:30:59.422  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:30:59.422  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:30:59.422  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:30:59.840  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:30:59.847  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:30:59.870  1 N - [TrajectoryAuto] select * from sqlite_master
21:30:59.880  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:30:59.880  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:30:59.934  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
21:30:59.943  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:30:59.960  1 N - [TrajectoryAuto]待检查数据表：Channels
21:30:59.961  1 N - [TrajectoryAuto] select * from sqlite_master
21:30:59.971  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
21:30:59.972  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:30:59.983  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:30:59.984  1 N - [TrajectoryAuto] select * from sqlite_master
21:30:59.993  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
21:30:59.994  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:31:00.004  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:31:00.004  1 N - [TrajectoryAuto] select * from sqlite_master
21:31:00.014  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
21:31:00.015  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:31:00.027  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23896 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:24:29.8430000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:42:02.782  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:42:02.786  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:42:02.786  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:42:02.786  1 N - TrajectoryAuto.Web 
21:42:02.786  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:42:02.786  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:42:02.786  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:42:03.241  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:42:03.247  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:42:03.269  1 N - [TrajectoryAuto] select * from sqlite_master
21:42:03.279  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:42:03.279  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:42:03.335  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
21:42:03.340  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:42:03.352  1 N - [TrajectoryAuto]待检查数据表：Channels
21:42:03.353  1 N - [TrajectoryAuto] select * from sqlite_master
21:42:03.362  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
21:42:03.363  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:42:03.373  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:42:03.374  1 N - [TrajectoryAuto] select * from sqlite_master
21:42:03.384  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
21:42:03.384  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:42:03.394  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:42:03.395  1 N - [TrajectoryAuto] select * from sqlite_master
21:42:03.405  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
21:42:03.405  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:42:03.414  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 12036 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:28:13.9680000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:45:46.907  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:45:46.911  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:45:46.911  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:45:46.911  1 N - TrajectoryAuto.Web 
21:45:46.911  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:45:46.911  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:45:46.912  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:45:47.354  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:45:47.361  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:45:47.382  1 N - [TrajectoryAuto] select * from sqlite_master
21:45:47.391  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:45:47.392  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:45:47.446  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
21:45:47.451  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:45:47.464  1 N - [TrajectoryAuto]待检查数据表：Channels
21:45:47.465  1 N - [TrajectoryAuto] select * from sqlite_master
21:45:47.474  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
21:45:47.475  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:45:47.485  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:45:47.485  1 N - [TrajectoryAuto] select * from sqlite_master
21:45:47.495  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
21:45:47.496  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:45:47.506  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:45:47.507  1 N - [TrajectoryAuto] select * from sqlite_master
21:45:47.515  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
21:45:47.516  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:45:47.524  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23964 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:36:53.7500000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:54:26.689  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:54:26.693  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:54:26.694  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:54:26.694  1 N - TrajectoryAuto.Web 
21:54:26.694  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:54:26.694  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:54:26.694  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:54:27.116  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:54:27.122  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:54:27.143  1 N - [TrajectoryAuto] select * from sqlite_master
21:54:27.153  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:54:27.154  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:54:27.212  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
21:54:27.217  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:54:27.230  1 N - [TrajectoryAuto]待检查数据表：Channels
21:54:27.231  1 N - [TrajectoryAuto] select * from sqlite_master
21:54:27.240  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
21:54:27.241  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:54:27.251  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:54:27.251  1 N - [TrajectoryAuto] select * from sqlite_master
21:54:27.261  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
21:54:27.262  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:54:27.272  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:54:27.272  1 N - [TrajectoryAuto] select * from sqlite_master
21:54:27.281  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
21:54:27.281  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:54:27.290  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9572 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:40:33.3590000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
21:58:06.301  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
21:58:06.305  1 N - NewLife组件核心库 ©2002-2024 NewLife
21:58:06.306  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
21:58:06.306  1 N - TrajectoryAuto.Web 
21:58:06.306  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
21:58:06.306  1 N - NewLife数据中间件 ©2002-2024 NewLife
21:58:06.306  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
21:58:06.752  1 N - [TrajectoryAuto]待检查数据表：Scenes
21:58:06.759  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
21:58:06.781  1 N - [TrajectoryAuto] select * from sqlite_master
21:58:06.791  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
21:58:06.792  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
21:58:06.846  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
21:58:06.850  1 N - [TrajectoryAuto] Select Count(*) From Scenes
21:58:06.863  1 N - [TrajectoryAuto]待检查数据表：Channels
21:58:06.864  1 N - [TrajectoryAuto] select * from sqlite_master
21:58:06.873  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
21:58:06.873  1 N - [TrajectoryAuto] Select Count(*) From Channels
21:58:06.884  1 N - [TrajectoryAuto]待检查数据表：Trajectories
21:58:06.885  1 N - [TrajectoryAuto] select * from sqlite_master
21:58:06.894  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
21:58:06.895  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
21:58:06.904  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
21:58:06.904  1 N - [TrajectoryAuto] select * from sqlite_master
21:58:06.914  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
21:58:06.914  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
21:58:06.922  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 10384 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:44:18.9840000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:01:51.917  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:01:51.920  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:01:51.921  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:01:51.921  1 N - TrajectoryAuto.Web 
22:01:51.921  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:01:51.921  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:01:51.921  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:01:52.342  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:01:52.350  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:01:52.369  1 N - [TrajectoryAuto] select * from sqlite_master
22:01:52.378  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:01:52.380  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:01:52.434  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
22:01:52.439  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:01:52.452  1 N - [TrajectoryAuto]待检查数据表：Channels
22:01:52.453  1 N - [TrajectoryAuto] select * from sqlite_master
22:01:52.462  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
22:01:52.463  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:01:52.473  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:01:52.474  1 N - [TrajectoryAuto] select * from sqlite_master
22:01:52.483  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
22:01:52.484  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:01:52.494  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:01:52.494  1 N - [TrajectoryAuto] select * from sqlite_master
22:01:52.502  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
22:01:52.503  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:01:52.511  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 12092 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:54:34.5460000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:12:07.477  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:12:07.480  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:12:07.480  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:12:07.481  1 N - TrajectoryAuto.Web 
22:12:07.481  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:12:07.481  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:12:07.481  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:12:07.895  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:12:07.902  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:12:07.923  1 N - [TrajectoryAuto] select * from sqlite_master
22:12:07.935  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:12:07.935  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:12:07.995  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
22:12:07.999  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:12:08.013  1 N - [TrajectoryAuto]待检查数据表：Channels
22:12:08.014  1 N - [TrajectoryAuto] select * from sqlite_master
22:12:08.024  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
22:12:08.025  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:12:08.034  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:12:08.035  1 N - [TrajectoryAuto] select * from sqlite_master
22:12:08.045  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
22:12:08.046  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:12:08.056  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:12:08.056  1 N - [TrajectoryAuto] select * from sqlite_master
22:12:08.065  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
22:12:08.066  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:12:08.095  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9484 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 03:56:50.5000000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:14:23.429  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:14:23.433  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:14:23.433  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:14:23.434  1 N - TrajectoryAuto.Web 
22:14:23.434  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:14:23.434  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:14:23.434  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:14:23.872  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:14:23.879  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:14:23.901  1 N - [TrajectoryAuto] select * from sqlite_master
22:14:23.910  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:14:23.911  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:14:23.974  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
22:14:23.979  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:14:23.993  1 N - [TrajectoryAuto]待检查数据表：Channels
22:14:23.994  1 N - [TrajectoryAuto] select * from sqlite_master
22:14:24.002  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
22:14:24.003  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:14:24.015  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:14:24.016  1 N - [TrajectoryAuto] select * from sqlite_master
22:14:24.026  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
22:14:24.027  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:14:24.037  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:14:24.038  1 N - [TrajectoryAuto] select * from sqlite_master
22:14:24.047  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
22:14:24.048  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:14:24.058  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 8520 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:01:12.1090000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:18:45.045  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:18:45.049  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:18:45.050  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:18:45.050  1 N - TrajectoryAuto.Web 
22:18:45.050  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:18:45.050  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:18:45.050  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:18:45.476  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:18:45.483  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:18:45.504  1 N - [TrajectoryAuto] select * from sqlite_master
22:18:45.513  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:18:45.513  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:18:45.564  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
22:18:45.568  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:18:45.581  1 N - [TrajectoryAuto]待检查数据表：Channels
22:18:45.582  1 N - [TrajectoryAuto] select * from sqlite_master
22:18:45.590  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
22:18:45.591  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:18:45.601  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:18:45.601  1 N - [TrajectoryAuto] select * from sqlite_master
22:18:45.611  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
22:18:45.611  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:18:45.621  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:18:45.621  1 N - [TrajectoryAuto] select * from sqlite_master
22:18:45.631  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
22:18:45.632  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:18:45.640  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 3904 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:08:44.7960000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:26:17.735  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:26:17.740  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:26:17.740  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:26:17.741  1 N - TrajectoryAuto.Web 
22:26:17.741  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:26:17.741  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:26:17.741  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:26:18.179  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:26:18.186  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:26:18.206  1 N - [TrajectoryAuto] select * from sqlite_master
22:26:18.215  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:26:18.216  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:26:18.268  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
22:26:18.273  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:26:18.285  1 N - [TrajectoryAuto]待检查数据表：Channels
22:26:18.286  1 N - [TrajectoryAuto] select * from sqlite_master
22:26:18.295  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
22:26:18.296  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:26:18.307  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:26:18.308  1 N - [TrajectoryAuto] select * from sqlite_master
22:26:18.316  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
22:26:18.317  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:26:18.327  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:26:18.328  1 N - [TrajectoryAuto] select * from sqlite_master
22:26:18.338  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
22:26:18.339  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:26:18.347  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23176 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:24:54.7810000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
22:42:27.720  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
22:42:27.723  1 N - NewLife组件核心库 ©2002-2024 NewLife
22:42:27.724  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
22:42:27.724  1 N - TrajectoryAuto.Web 
22:42:27.724  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
22:42:27.724  1 N - NewLife数据中间件 ©2002-2024 NewLife
22:42:27.724  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
22:42:28.151  1 N - [TrajectoryAuto]待检查数据表：Scenes
22:42:28.158  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
22:42:28.180  1 N - [TrajectoryAuto] select * from sqlite_master
22:42:28.189  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
22:42:28.190  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
22:42:28.242  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
22:42:28.246  1 N - [TrajectoryAuto] Select Count(*) From Scenes
22:42:28.262  1 N - [TrajectoryAuto]待检查数据表：Channels
22:42:28.262  1 N - [TrajectoryAuto] select * from sqlite_master
22:42:28.272  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
22:42:28.273  1 N - [TrajectoryAuto] Select Count(*) From Channels
22:42:28.283  1 N - [TrajectoryAuto]待检查数据表：Trajectories
22:42:28.284  1 N - [TrajectoryAuto] select * from sqlite_master
22:42:28.294  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
22:42:28.294  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
22:42:28.304  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
22:42:28.305  1 N - [TrajectoryAuto] select * from sqlite_master
22:42:28.313  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
22:42:28.314  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
22:42:28.325  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 16424 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:08:15.8750000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:25:48.803  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:25:48.807  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:25:48.807  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:25:48.807  1 N - TrajectoryAuto.Web 
23:25:48.807  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:25:48.808  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:25:48.808  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:25:49.254  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:25:49.261  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:25:49.284  1 N - [TrajectoryAuto] select * from sqlite_master
23:25:49.294  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:25:49.295  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:25:49.360  1 N - [TrajectoryAuto] Alter Table Scenes Add ScaleX real NULL
23:25:49.372  1 N - [TrajectoryAuto] Alter Table Scenes Add ScaleY real NULL
23:25:49.383  1 N - [TrajectoryAuto] Alter Table Scenes Add InvertY bit NULL
23:25:49.405  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
23:25:49.411  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:25:49.422  1 N - [TrajectoryAuto]待检查数据表：Channels
23:25:49.423  1 N - [TrajectoryAuto] select * from sqlite_master
23:25:49.432  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
23:25:49.433  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:25:49.443  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:25:49.444  1 N - [TrajectoryAuto] select * from sqlite_master
23:25:49.453  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
23:25:49.453  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:25:49.464  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:25:49.465  1 N - [TrajectoryAuto] select * from sqlite_master
23:25:49.473  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
23:25:49.474  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:25:49.482  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 13080 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:20:12.0620000
#Date: 2025-08-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:37:44.993  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:37:44.997  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:37:44.997  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:37:44.997  1 N - TrajectoryAuto.Web 
23:37:44.997  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:37:44.997  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:37:44.998  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:37:45.418  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:37:45.424  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:37:45.445  1 N - [TrajectoryAuto] select * from sqlite_master
23:37:45.455  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:37:45.456  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:37:45.507  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 1
23:37:45.511  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:37:45.524  1 N - [TrajectoryAuto]待检查数据表：Channels
23:37:45.525  1 N - [TrajectoryAuto] select * from sqlite_master
23:37:45.534  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
23:37:45.535  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:37:45.545  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:37:45.546  1 N - [TrajectoryAuto] select * from sqlite_master
23:37:45.556  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
23:37:45.557  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:37:45.566  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:37:45.567  1 N - [TrajectoryAuto] select * from sqlite_master
23:37:45.575  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
23:37:45.576  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:37:45.585  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
