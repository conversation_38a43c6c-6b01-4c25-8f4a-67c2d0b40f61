{"format": 1, "restore": {"D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj": {}}, "projects": {"D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj", "projectName": "TrajectoryAuto.Core", "projectPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj", "projectName": "TrajectoryAuto.Infrastructure", "projectPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj": {"projectPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "NewLife.Core": {"target": "Package", "version": "[10.10.2024.301, )"}, "NewLife.XCode": {"target": "Package", "version": "[11.10.2024.301, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj", "projectName": "TrajectoryAuto.Web", "projectPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj": {"projectPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj"}, "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj": {"projectPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}