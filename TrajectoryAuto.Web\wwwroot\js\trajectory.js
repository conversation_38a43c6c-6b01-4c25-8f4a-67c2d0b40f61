// 轨迹管理器
class TrajectoryManager {
    constructor() {
        this.playbackTimers = new Map();
        this.playbackStatus = new Map();
        this.undoStack = [];
        this.redoStack = [];
        this.selectedTrajectory = null;
        this.init();
    }

    init() {
        this.setupKeyboardShortcuts();
        //this.setupCanvasZoom();
    }

    // 设置键盘快捷键
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Z 撤销
            if (e.ctrlKey && e.key === 'z') {
                e.preventDefault();
                this.undo();
            }
            
            // Ctrl+Y 重做
            if (e.ctrlKey && e.key === 'y') {
                e.preventDefault();
                this.redo();
            }
            
            // 空格键播放/暂停
            if (e.code === 'Space' && e.target.tagName !== 'INPUT') {
                e.preventDefault();
                this.togglePlayback();
            }
            
            // Delete键删除选中轨迹
            if (e.key === 'Delete') {
                this.deleteSelectedTrajectory();
            }
            
            // 数字键1-6选择工具
            if (e.key >= '1' && e.key <= '6') {
                const tools = ['select', 'circle', 'rectangle', 'polygon', 'freedraw', 'realtime'];
                const toolIndex = parseInt(e.key) - 1;
                if (toolIndex < tools.length) {
                    window.app.selectTool(tools[toolIndex]);
                }
            }
            
            // Ctrl+S 保存
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveCurrentState();
            }
            
            // Ctrl+O 打开
            if (e.ctrlKey && e.key === 'o') {
                e.preventDefault();
                this.loadState();
            }
        });
    }

    // 设置画布缩放
    setupCanvasZoom() {
        const canvas = document.getElementById('trajectoryCanvas');
        
        // 鼠标滚轮缩放
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            const rect = canvas.getBoundingClientRect();
            const currentWidth = rect.width;
            const currentHeight = rect.height;
            
            const newWidth = currentWidth * delta;
            const newHeight = currentHeight * delta;
            
            // 限制缩放范围
            if (newWidth >= 200 && newWidth <= 2000 && newHeight >= 150 && newHeight <= 1500) {
                canvas.style.width = newWidth + 'px';
                canvas.style.height = newHeight + 'px';
            }
        });
        
        // 双击重置缩放
        canvas.addEventListener('dblclick', () => {
            window.canvasManager.resetZoom();
        });
    }

    // 切换播放状态
    async togglePlayback() {
        if (!window.app.currentChannel || !window.app.trajectories.length) {
            return;
        }

        const latestTrajectory = window.app.trajectories[0];
        const status = this.playbackStatus.get(latestTrajectory.id) || 0;

        if (status === 1) { // 正在播放
            await window.app.stopTrajectory();
        } else { // 停止状态
            await window.app.playTrajectory();
        }
    }

    // 删除选中的轨迹
    async deleteSelectedTrajectory() {
        if (!this.selectedTrajectory) {
            return;
        }

        if (confirm('确定要删除选中的轨迹吗？')) {
            try {
                const response = await fetch(`/api/trajectories/${this.selectedTrajectory.id}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    // 从本地列表中移除
                    const index = window.app.trajectories.findIndex(t => t.id === this.selectedTrajectory.id);
                    if (index !== -1) {
                        window.app.trajectories.splice(index, 1);
                    }
                    
                    this.selectedTrajectory = null;
                    window.canvasManager.redrawCanvas();
                } else {
                    alert('删除轨迹失败');
                }
            } catch (error) {
                console.error('删除轨迹失败:', error);
                alert('删除轨迹失败');
            }
        }
    }
    
    // 清除所有轨迹
    clearTrajectories() {
        console.log('清除所有轨迹');
        this.selectedTrajectory = null;
        
        // 停止所有播放定时器
        if (this.playbackTimers) {
            this.playbackTimers.forEach((timer) => {
                clearTimeout(timer);
            });
            this.playbackTimers.clear();
        }
        
        // 重置播放状态
        if (this.playbackStatus) {
            this.playbackStatus.clear();
        }
        
        // 清除撤销/重做堆栈
        this.undoStack = [];
        this.redoStack = [];
        
        // 如果存在全局轨迹数组，清空它
        if (window.app && window.app.trajectories) {
            window.app.trajectories = [];
        }
        
        // 重绘画布
        if (window.canvasManager) {
            window.canvasManager.redrawCanvas();
        }
    }

    // 撤销操作
    undo() {
        if (this.undoStack.length === 0) {
            return;
        }

        const currentState = this.getCurrentState();
        this.redoStack.push(currentState);

        const previousState = this.undoStack.pop();
        this.restoreState(previousState);
    }

    // 重做操作
    redo() {
        if (this.redoStack.length === 0) {
            return;
        }

        const currentState = this.getCurrentState();
        this.undoStack.push(currentState);

        const nextState = this.redoStack.pop();
        this.restoreState(nextState);
    }

    // 获取当前状态
    getCurrentState() {
        return {
            trajectories: JSON.parse(JSON.stringify(window.app.trajectories)),
            currentScene: window.app.currentScene,
            currentChannel: window.app.currentChannel,
            timestamp: Date.now()
        };
    }

    // 恢复状态
    restoreState(state) {
        window.app.trajectories = state.trajectories;
        window.canvasManager.trajectories = state.trajectories;
        window.canvasManager.redrawCanvas();
    }

    // 保存状态到撤销栈
    saveStateToUndoStack() {
        const currentState = this.getCurrentState();
        this.undoStack.push(currentState);
        
        // 限制撤销栈大小
        if (this.undoStack.length > 50) {
            this.undoStack.shift();
        }
        
        // 清空重做栈
        this.redoStack = [];
    }

    // 保存当前状态到本地存储
    saveCurrentState() {
        const state = {
            scenes: window.app.scenes,
            currentScene: window.app.currentScene,
            channels: window.app.channels,
            currentChannel: window.app.currentChannel,
            trajectories: window.app.trajectories,
            timestamp: Date.now()
        };

        try {
            localStorage.setItem('trajectoryAutoState', JSON.stringify(state));
            this.showNotification('状态已保存到本地', 'success');
        } catch (error) {
            console.error('保存状态失败:', error);
            this.showNotification('保存状态失败', 'error');
        }
    }

    // 从本地存储加载状态
    loadState() {
        try {
            const savedState = localStorage.getItem('trajectoryAutoState');
            if (!savedState) {
                this.showNotification('没有找到保存的状态', 'warning');
                return;
            }

            const state = JSON.parse(savedState);
            
            if (confirm('确定要加载保存的状态吗？当前状态将被覆盖。')) {
                // 恢复状态
                window.app.scenes = state.scenes || [];
                window.app.channels = state.channels || [];
                window.app.trajectories = state.trajectories || [];
                
                // 重新渲染界面
                window.app.renderScenes();
                if (state.currentScene) {
                    window.app.selectScene(state.currentScene);
                }
                if (state.currentChannel) {
                    window.app.selectChannel(state.currentChannel);
                }
                
                window.canvasManager.trajectories = state.trajectories || [];
                window.canvasManager.redrawCanvas();
                
                this.showNotification('状态加载成功', 'success');
            }
        } catch (error) {
            console.error('加载状态失败:', error);
            this.showNotification('加载状态失败', 'error');
        }
    }

    // 导出轨迹数据
    exportTrajectories() {
        if (!window.app.trajectories.length) {
            this.showNotification('没有轨迹数据可导出', 'warning');
            return;
        }

        const exportData = {
            scene: window.app.currentScene,
            channel: window.app.currentChannel,
            trajectories: window.app.trajectories,
            exportTime: new Date().toISOString(),
            version: '1.0'
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `trajectories_${Date.now()}.json`;
        link.click();
        
        this.showNotification('轨迹数据已导出', 'success');
    }

    // 导入轨迹数据
    importTrajectories() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            try {
                const text = await file.text();
                const importData = JSON.parse(text);
                
                if (!importData.trajectories || !Array.isArray(importData.trajectories)) {
                    throw new Error('无效的轨迹数据格式');
                }
                
                if (confirm('确定要导入轨迹数据吗？当前轨迹将被替换。')) {
                    // 导入轨迹到服务器
                    for (const trajectory of importData.trajectories) {
                        trajectory.channelId = window.app.currentChannel?.id;
                        await this.createTrajectoryOnServer(trajectory);
                    }
                    
                    // 重新加载轨迹
                    if (window.app.currentChannel) {
                        await window.app.loadTrajectories(window.app.currentChannel.id);
                    }
                    
                    this.showNotification('轨迹数据导入成功', 'success');
                }
            } catch (error) {
                console.error('导入轨迹失败:', error);
                this.showNotification('导入轨迹失败: ' + error.message, 'error');
            }
        };
        
        input.click();
    }

    // 在服务器上创建轨迹
    async createTrajectoryOnServer(trajectory) {
        try {
            const response = await fetch('/api/trajectories', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trajectory)
            });

            if (!response.ok) {
                throw new Error('服务器创建轨迹失败');
            }

            return await response.json();
        } catch (error) {
            console.error('创建轨迹失败:', error);
            throw error;
        }
    }

    // 显示通知消息
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        // 根据类型设置背景色
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#28a745';
                break;
            case 'error':
                notification.style.backgroundColor = '#dc3545';
                break;
            case 'warning':
                notification.style.backgroundColor = '#ffc107';
                notification.style.color = '#212529';
                break;
            default:
                notification.style.backgroundColor = '#17a2b8';
        }
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // 生成预设轨迹
    generatePresetTrajectory(type) {
        const canvas = document.getElementById('trajectoryCanvas');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        
        switch (type) {
            case 'figure8':
                this.generateFigure8Trajectory(centerX, centerY);
                break;
            case 'spiral':
                this.generateSpiralTrajectory(centerX, centerY);
                break;
            case 'zigzag':
                this.generateZigzagTrajectory(centerX, centerY);
                break;
            case 'star':
                this.generateStarTrajectory(centerX, centerY);
                break;
        }
    }

    // 生成8字形轨迹
    generateFigure8Trajectory(centerX, centerY) {
        const points = [];
        const duration = parseFloat(document.getElementById('trajectoryDuration').value) || 10;
        const radius = 60;
        const pointCount = 72;
        
        for (let i = 0; i < pointCount; i++) {
            const t = (i / pointCount) * 4 * Math.PI;
            const x = centerX + radius * Math.sin(t);
            const y = centerY + radius * Math.sin(t) * Math.cos(t);
            const timestamp = (i / pointCount) * duration;
            
            points.push({ x, y, timestamp });
        }
        
        window.canvasManager.createTrajectory('figure8', points, duration);
    }

    // 生成螺旋轨迹
    generateSpiralTrajectory(centerX, centerY) {
        const points = [];
        const duration = parseFloat(document.getElementById('trajectoryDuration').value) || 10;
        const maxRadius = 80;
        const pointCount = 72;
        
        for (let i = 0; i < pointCount; i++) {
            const t = (i / pointCount) * 6 * Math.PI;
            const radius = (i / pointCount) * maxRadius;
            const x = centerX + radius * Math.cos(t);
            const y = centerY + radius * Math.sin(t);
            const timestamp = (i / pointCount) * duration;
            
            points.push({ x, y, timestamp });
        }
        
        window.canvasManager.createTrajectory('spiral', points, duration);
    }

    // 生成锯齿轨迹
    generateZigzagTrajectory(centerX, centerY) {
        const points = [];
        const duration = parseFloat(document.getElementById('trajectoryDuration').value) || 10;
        const width = 120;
        const height = 60;
        const segments = 8;
        
        for (let i = 0; i <= segments; i++) {
            const x = centerX - width/2 + (i / segments) * width;
            const y = centerY + (i % 2 === 0 ? -height/2 : height/2);
            const timestamp = (i / segments) * duration;
            
            points.push({ x, y, timestamp });
        }
        
        window.canvasManager.createTrajectory('zigzag', points, duration);
    }

    // 生成星形轨迹
    generateStarTrajectory(centerX, centerY) {
        const points = [];
        const duration = parseFloat(document.getElementById('trajectoryDuration').value) || 10;
        const outerRadius = 70;
        const innerRadius = 30;
        const spikes = 5;
        const pointCount = spikes * 2;
        
        for (let i = 0; i <= pointCount; i++) {
            const angle = (i / pointCount) * 2 * Math.PI;
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const x = centerX + radius * Math.cos(angle - Math.PI/2);
            const y = centerY + radius * Math.sin(angle - Math.PI/2);
            const timestamp = (i / pointCount) * duration;
            
            points.push({ x, y, timestamp });
        }
        
        // 闭合星形
        points.push({ ...points[0], timestamp: duration });
        
        window.canvasManager.createTrajectory('star', points, duration);
    }

    // 更新播放状态
    updatePlaybackStatus(trajectoryId, status) {
        this.playbackStatus.set(trajectoryId, status);
    }
}

// 注意：TrajectoryManager实例在canvas.js中创建，这里不重复创建
// canvas.js中的CanvasManager会创建并管理TrajectoryManager实例