using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using NewLife.Data;
using XCode;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace TrajectoryAuto.Infrastructure.Data.XCodeModels;

/// <summary>轨迹点实体 - 音频灯光联动毫秒级响应优化</summary>
[BindTable("TrajectoryPoints", Description = "轨迹点表", ConnName = "TrajectoryAuto", DbType = DatabaseType.SQLite)]
public partial class TrajectoryPointEntity : Entity<TrajectoryPointEntity>
{
    #region 属性
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 50)]
    [BindColumn("Id", "编号", "")]
    public String Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }
    private String _Id = "";

    /// <summary>X坐标</summary>
    [DisplayName("X坐标")]
    [Description("X坐标")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("X", "X坐标", "")]
    public Double X { get => _X; set { if (OnPropertyChanging("X", value)) { _X = value; OnPropertyChanged("X"); } } }
    private Double _X;

    /// <summary>Y坐标</summary>
    [DisplayName("Y坐标")]
    [Description("Y坐标")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Y", "Y坐标", "")]
    public Double Y { get => _Y; set { if (OnPropertyChanging("Y", value)) { _Y = value; OnPropertyChanged("Y"); } } }
    private Double _Y;

    /// <summary>时间戳</summary>
    [DisplayName("时间戳")]
    [Description("时间戳")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Timestamp", "时间戳", "")]
    public Double Timestamp { get => _Timestamp; set { if (OnPropertyChanging("Timestamp", value)) { _Timestamp = value; OnPropertyChanged("Timestamp"); } } }
    private Double _Timestamp;

    /// <summary>顺序</summary>
    [DisplayName("顺序")]
    [Description("顺序")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Order", "顺序", "")]
    public Int32 Order { get => _Order; set { if (OnPropertyChanging("Order", value)) { _Order = value; OnPropertyChanged("Order"); } } }
    private Int32 _Order;

    /// <summary>轨迹编号</summary>
    [DisplayName("轨迹编号")]
    [Description("轨迹编号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("TrajectoryId", "轨迹编号", "")]
    public String TrajectoryId { get => _TrajectoryId; set { if (OnPropertyChanging("TrajectoryId", value)) { _TrajectoryId = value; OnPropertyChanged("TrajectoryId"); } } }
    private String _TrajectoryId = "";
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get
        {
            switch (name)
            {
                case "Id": return _Id;
                case "X": return _X;
                case "Y": return _Y;
                case "Timestamp": return _Timestamp;
                case "Order": return _Order;
                case "TrajectoryId": return _TrajectoryId;
                default: return base[name];
            }
        }
        set
        {
            switch (name)
            {
                case "Id": _Id = Convert.ToString(value) ?? ""; break;
                case "X": _X = Convert.ToDouble(value); break;
                case "Y": _Y = Convert.ToDouble(value); break;
                case "Timestamp": _Timestamp = Convert.ToDouble(value); break;
                case "Order": _Order = Convert.ToInt32(value); break;
                case "TrajectoryId": _TrajectoryId = Convert.ToString(value) ?? ""; break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 字段名
    /// <summary>取得轨迹点实体字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>X坐标</summary>
        public static readonly Field X = FindByName("X");

        /// <summary>Y坐标</summary>
        public static readonly Field Y = FindByName("Y");

        /// <summary>时间戳</summary>
        public static readonly Field Timestamp = FindByName("Timestamp");

        /// <summary>顺序</summary>
        public static readonly Field Order = FindByName("Order");

        /// <summary>轨迹编号</summary>
        public static readonly Field TrajectoryId = FindByName("TrajectoryId");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }
    #endregion

    #region 对象操作
    static TrajectoryPointEntity()
    {
        // 轨迹点数据量大，关闭不必要的模块提升性能
        Meta.Session.Dal.Db.ShowSQL = false;
    }

    public override void Valid(Boolean isNew)
    {
        if (!HasDirty) return;
        base.Valid(isNew);

        if (String.IsNullOrEmpty(TrajectoryId)) throw new ArgumentNullException(nameof(TrajectoryId), "轨迹ID不能为空！");
    }
    #endregion

    #region 扩展查询
    /// <summary>根据轨迹ID查找点位</summary>
    public static IList<TrajectoryPointEntity> FindByTrajectoryId(String trajectoryId)
    {
        if (String.IsNullOrEmpty(trajectoryId)) return new List<TrajectoryPointEntity>();
        return FindAll(_.TrajectoryId == trajectoryId).OrderBy(e => e.Order).ToList();
    }

    /// <summary>批量插入轨迹点</summary>
    public static Int32 BatchInsert(IList<TrajectoryPointEntity> points)
    {
        if (points == null || points.Count == 0) return 0;
        
        var count = 0;
        foreach (var point in points)
        {
            point.Insert();
            count++;
        }
        return count;
    }

    /// <summary>根据轨迹通道ID删除点位</summary>
    public static Int32 DeleteByTrajectoryChannelId(String channelId)
    {
        if (String.IsNullOrEmpty(channelId)) return 0;

        // 先查找所有相关轨迹
        var trajectories = TrajectoryEntity.FindByChannelId(channelId);
        var count = 0;
        
        foreach (var trajectory in trajectories)
        {
            count += Delete(_.TrajectoryId == trajectory.Id);
        }
        
        return count;
    }
    #endregion

    #region 业务操作
    public TrajectoryAuto.Core.Entities.TrajectoryPoint ToBusinessEntity()
    {
        return new TrajectoryAuto.Core.Entities.TrajectoryPoint
        {
            Id = Guid.Parse(Id),
            X = X,
            Y = Y,
            Timestamp = Timestamp,
            Order = Order,
            TrajectoryId = Guid.Parse(TrajectoryId)
        };
    }

    public void FromBusinessEntity(TrajectoryAuto.Core.Entities.TrajectoryPoint point)
    {
        Id = point.Id.ToString();
        X = point.X;
        Y = point.Y;
        Timestamp = point.Timestamp;
        Order = point.Order;
        TrajectoryId = point.TrajectoryId.ToString();
    }
    #endregion
}