class SceneManager {
    constructor(app) {
        console.log('初始化SceneManager', app);
        this.app = app;
        this.scenes = [];
        this.currentScene = null;
        this.selectedSceneId = null;
        
        // 在构造函数中绑定方法的this上下文
        this.showSceneModal = this.showSceneModal.bind(this);
        this.hideSceneModal = this.hideSceneModal.bind(this);
        this.saveScene = this.saveScene.bind(this);
    }

    // 加载场景列表
    async loadScenes() {
        try {
            const response = await fetch('/api/scenes');
            this.scenes = await response.json();
            this.renderScenes();
            return this.scenes;
        } catch (error) {
            console.error('加载场景失败:', error);
            throw error;
        }
    }

    // 渲染场景列表
    renderScenes() {
        const sceneList = document.getElementById('sceneList');
        if (!sceneList) return;

        sceneList.innerHTML = '';
        this.scenes.forEach(scene => {
            const sceneItem = document.createElement('div');
            sceneItem.className = 'scene-item';
            sceneItem.dataset.id = scene.id;
            
            sceneItem.innerHTML = `
                <h4>${scene.name}</h4>
                <p>尺寸: ${scene.width}x${scene.height}</p>
                <p>服务器: ${scene.serverIp}:${scene.serverPort}</p>
            `;

            sceneList.appendChild(sceneItem);
        });
    }

    // 选择场景
    async selectScene(scene) {
        if (!scene) return;

        console.log('开始选择场景:', scene.name, scene.id);
        this.currentScene = scene;
        this.selectedSceneId = scene.id;
        
        // 同步到全局应用对象
        window.app.currentScene = scene;
        
        // 清理画布和轨迹状态
        if (window.app && window.app.trajectoryManager) {
            console.log('清理当前轨迹数据');
            window.app.trajectoryManager.clearTrajectories();
        } else if (window.trajectoryManager) {
            window.trajectoryManager.clearTrajectories();
        } else {
            console.log('轨迹管理器不存在，无法清除轨迹');
        }
        
        // 更新UI状态
        document.querySelectorAll('.scene-item').forEach(item => {
            item.classList.toggle('selected', item.dataset.id === scene.id);
        });
        
        // 清理通道选中状态
        document.querySelectorAll('.channel-item').forEach(item => {
            item.classList.remove('selected');
            item.classList.remove('active');
        });

        // 更新画布尺寸
        const canvas = document.getElementById('trajectoryCanvas');
        if (canvas) {
            // 更新画布尺寸
            canvas.width = scene.width;
            canvas.height = scene.height;
            
            console.log('更新画布尺寸为:', scene.width, 'x', scene.height);
            
            // 先清空画布
            const ctx = canvas.getContext('2d');
            if (ctx) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
            }
            
            // 强制触发一次重绘，确保尺寸变更后立即重绘
            if (this.app.canvasManager) {
                console.log('场景切换后立即触发画布重绘');
                this.app.canvasManager.redrawCanvas();
            }
        }

        // 设置背景图片
        if (scene.backgroundImagePath) {
            if (this.app.canvasManager) {
                console.log('设置场景背景图片:', scene.backgroundImagePath);
                this.app.canvasManager.setBackground(scene.backgroundImagePath);
                // 直接触发重绘
                this.app.canvasManager.redrawCanvas();
            }
        } else if (this.app.canvasManager) {
            // 清除背景图片
            console.log('清除场景背景图片');
            this.app.canvasManager.clearBackground();
            // 直接触发重绘
            this.app.canvasManager.redrawCanvas();
        }
        
        // 确保画布立即重绘
        if (this.app.canvasManager) {
            console.log('场景切换后强制立即重绘画布');
            this.app.canvasManager.redrawCanvas();
        }
        
        // 加载场景的通道
        if (this.app.channelManager) {
            console.log('切换场景，加载通道并重绘轨迹');
            try {
                // 加载通道并等待完成
                const channels = await this.app.channelManager.loadChannels(scene.id);
                console.log(`场景 ${scene.id} 加载到通道数量:`, channels.length);
                
                // 再次确保画布重绘
                if (this.app.canvasManager) {
                    console.log('通道加载后再次强制重绘画布');
                    this.app.canvasManager.redrawCanvas();
                }
                
                // 确保当前通道已经设置
                if (this.app.currentChannel && this.app.currentChannel.id) {
                    console.log('当前通道已设置，开始加载轨迹:', this.app.currentChannel.id);
                    
                    // 直接加载轨迹
                    try {
                        const trajectories = await this.app.loadTrajectories(this.app.currentChannel.id);
                        console.log('轨迹加载完成，数量:', trajectories.length);
                        
                        // 确保轨迹数据同步到画布管理器
                        if (this.app.canvasManager) {
                            console.log('同步轨迹数据到画布管理器');
                            this.app.canvasManager.trajectories = [...trajectories];
                        }
                        
                        // 轨迹加载完成后立即重绘画布
                        if (this.app.canvasManager) {
                            const timestamp = new Date().getTime();
                            console.log(`[${timestamp}] 开始重绘画布显示轨迹`);
                            
                            // 确保画布可见
                            const canvas = document.getElementById('trajectoryCanvas');
                            if (canvas) {
                                canvas.style.visibility = 'visible';
                                canvas.style.display = 'block';
                            }
                            
                            // 立即重绘
                            this.app.canvasManager.redrawCanvas();
                            
                            // 使用多个延迟时间的重绘，确保在各种情况下都能正确显示
                            const delayTimes = [50, 100, 300, 500];
                            delayTimes.forEach((delay, index) => {
                                setTimeout(() => {
                                    console.log(`[${timestamp}] 第${index+1}次延迟重绘画布(${delay}ms)，确保轨迹显示`);
                                    this.app.canvasManager.redrawCanvas();
                                }, delay);
                            });
                        }
                    } catch (trajError) {
                        console.error('加载轨迹时出错:', trajError);
                    }
                } else if (channels && channels.length > 0) {
                    // 如果有通道但当前通道未设置，尝试选择第一个通道
                    console.log('当前通道未设置，尝试选择第一个通道');
                    try {
                        await this.app.channelManager.selectChannel(channels[0]);
                    } catch (selError) {
                        console.error('选择通道失败:', selError);
                    }
                } else {
                    console.warn('没有可用的通道，无法加载轨迹');
                    // 即使没有通道，也重绘画布显示背景图片
                    if (this.app.canvasManager) {
                        this.app.canvasManager.redrawCanvas();
                    }
                }
            } catch (error) {
                console.error('加载通道时出错:', error);
                // 即使出错，也尝试重绘画布
                if (this.app.canvasManager) {
                    this.app.canvasManager.redrawCanvas();
                }
            }
        }
    }

    // 编辑场景
    async editScene(sceneId) {
        try {
            const response = await fetch(`/api/scenes/${sceneId}`);
            if (!response.ok) {
                throw new Error('获取场景信息失败');
            }
            
            const scene = await response.json();
            this.showSceneModal(scene);
        } catch (error) {
            console.error('编辑场景出错:', error);
            alert('编辑场景时出错: ' + error.message);
        }
    }
    
    // 删除场景
    async deleteScene(sceneId) {
        try {
            if (!confirm('确定要删除这个场景吗？')) {
                return;
            }

            const response = await fetch(`/api/scenes/${sceneId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                // 清除选中状态
                this.selectedSceneId = null;
                document.getElementById('sceneActions').style.display = 'none';
                // 重新加载场景列表
                await this.loadScenes();
            } else {
                alert('删除场景失败');
            }
        } catch (error) {
            console.error('删除场景出错:', error);
            alert('删除场景时出错: ' + error.message);
        }
    }
    
    // 显示场景模态框
    showSceneModal(scene = null) {
        console.log('显示场景模态框被调用', scene);
        
        // 先删除现有的模态框，避免样式冲突
        let existingModal = document.getElementById('sceneModal');
        if (existingModal && existingModal.parentNode) {
            console.log('删除现有模态框');
            existingModal.parentNode.removeChild(existingModal);
        }
        
        // 创建新的模态框
        console.log('创建新的模态框');
        let modal = this.createSceneModal();
        
        const form = document.getElementById('sceneForm');
        const modalTitle = document.getElementById('sceneModalTitle');
        
        if (!form || !modalTitle) {
            console.error('模态框元素未找到', { form, modalTitle });
            return;
        }
        
        // 检查模态框当前样式
        console.log('模态框当前样式:', {
            display: modal.style.display,
            zIndex: modal.style.zIndex,
            position: modal.style.position,
            computedStyle: window.getComputedStyle(modal)
        });
        
        if (scene) {
            // 编辑模式
            modalTitle.innerHTML = '<i class="fas fa-edit me-2"></i>编辑场景';
            
            // 设置表单值
            document.getElementById('sceneName').value = scene.name || '';
            document.getElementById('sceneWidth').value = scene.width || 800;
            document.getElementById('sceneHeight').value = scene.height || 600;
            document.getElementById('originX').value = scene.originX || 0;
            document.getElementById('originY').value = scene.originY || 0;
            document.getElementById('scaleX').value = scene.scaleX || 1.0;
            document.getElementById('scaleY').value = scene.scaleY || 1.0;
            document.getElementById('invertY').checked = !!scene.invertY;
            document.getElementById('serverIp').value = scene.serverIp || '127.0.0.1';
            document.getElementById('serverPort').value = scene.serverPort || 8080;
            
            // 处理背景图片预览
            const backgroundImagePreview = document.getElementById('backgroundImagePreview');
            const backgroundImageName = document.getElementById('backgroundImageName');
            const viewBackgroundBtn = document.getElementById('viewBackgroundBtn');
            const deleteBackgroundBtn = document.getElementById('deleteBackgroundBtn');
            const keepBackgroundImage = document.getElementById('keepBackgroundImage');
            
            // 设置保留背景图片的默认值为true
            keepBackgroundImage.value = 'true';
            
            if (scene.backgroundImagePath) {
                // 如果有背景图片，显示预览区域
                backgroundImagePreview.style.display = 'block';
                backgroundImageName.textContent = `已上传: ${scene.backgroundImagePath}`;
                
                // 存储原始背景图片路径
                form.dataset.originalBackgroundPath = scene.backgroundImagePath;
                
                // 绑定查看图片按钮事件
                viewBackgroundBtn.onclick = () => {
                    // 打开一个新窗口或模态框显示图片
                    const imageUrl = `/uploads/${scene.backgroundImagePath}`;
                    window.open(imageUrl, '_blank', 'width=800,height=600');
                };
                
                // 绑定删除背景图片按钮事件
                deleteBackgroundBtn.onclick = () => {
                    if (confirm('确定要删除背景图片吗？')) {
                        backgroundImagePreview.style.display = 'none';
                        keepBackgroundImage.value = 'false';
                        
                        // 实时更新画布预览
                        if (this.app.canvasManager) {
                            this.app.canvasManager.clearBackground();
                            this.app.canvasManager.redrawCanvas();
                        }
                    }
                };
            } else {
                // 如果没有背景图片，隐藏预览区域
                backgroundImagePreview.style.display = 'none';
            }
            
            // 存储场景ID
            let idInput = form.querySelector('input[name="id"]');
            if (!idInput) {
                idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'id';
                form.appendChild(idInput);
            }
            idInput.value = scene.id;
        } else {
            // 新建模式
            modalTitle.innerHTML = '<i class="fas fa-plus-circle me-2"></i>新建场景';
            form.reset();
            
            // 设置默认值
            document.getElementById('sceneWidth').value = 800;
            document.getElementById('sceneHeight').value = 600;
            document.getElementById('originX').value = 0;
            document.getElementById('originY').value = 0;
            document.getElementById('scaleX').value = 1.0;
            document.getElementById('scaleY').value = 1.0;
            document.getElementById('invertY').checked = false;
            document.getElementById('serverIp').value = '127.0.0.1';
            document.getElementById('serverPort').value = 8080;
            
            // 移除隐藏的ID字段（如果存在）
            const idInput = form.querySelector('input[name="id"]');
            if (idInput && idInput.parentNode) {
                form.removeChild(idInput);
            }
        }
        
        // 强制设置样式确保模态框显示在最上层
        console.log('设置模态框样式');
        modal.style.display = 'block'; // 改为block而不是flex
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.style.zIndex = '9999';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        
        // 确保模态框内容可见
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            console.log('设置模态框内容样式');
            modalContent.style.opacity = '1';
            modalContent.style.visibility = 'visible';
            modalContent.style.zIndex = '10000';
            modalContent.style.position = 'relative';
            modalContent.style.margin = '10% auto';
            modalContent.style.width = '80%';
            modalContent.style.maxWidth = '600px';
            modalContent.style.backgroundColor = '#fff';
            modalContent.style.borderRadius = '5px';
            modalContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
            modalContent.style.overflow = 'hidden'; // 防止内容溢出圆角
            
            // 处理模态框标题栏圆角问题
            const modalHeader = modalContent.querySelector('.modal-header');
            if (modalHeader) {
                modalHeader.style.borderTopLeftRadius = '5px';
                modalHeader.style.borderTopRightRadius = '5px';
                modalHeader.style.backgroundColor = '#f8f9fa';
                modalHeader.style.padding = '10px 15px';
                modalHeader.style.borderBottom = '1px solid #dee2e6';
            }
        }
        
        // 确保模态框在body下
        const body = document.body;
        if (modal.parentNode !== body) {
            console.log('将模态框移动到body直接子元素位置');
            body.appendChild(modal);
        }
        
        // 添加点击事件测试
        modal.onclick = function(event) {
            console.log('模态框被点击', event.target);
            if (event.target === modal) {
                console.log('点击了模态框背景');
            }
        };
        
        console.log('模态框已显示', modal);
        
        // 强制重绘
        setTimeout(() => {
            console.log('强制重绘模态框');
            modal.style.display = 'none';
            setTimeout(() => {
                modal.style.display = 'block';
            }, 10);
        }, 50);
    }

    // 隐藏场景模态框
    hideSceneModal() {
        const modal = document.getElementById('sceneModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 生成新的GUID
    generateGuid() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    
    // 创建场景模态框
    createSceneModal() {
        console.log('创建新的场景模态框');
        
        // 检查是否已存在
        let existingModal = document.getElementById('sceneModal');
        if (existingModal) {
            console.log('模态框已存在，返回现有模态框');
            return existingModal;
        }
        
        // 创建模态框结构
        const modal = document.createElement('div');
        modal.id = 'sceneModal';
        modal.className = 'modal';
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .background-image-preview {
                margin-top: 10px;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
            }
            .preview-info {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            .background-actions {
                display: flex;
                gap: 10px;
            }
            #viewBackgroundBtn {
                background-color: #17a2b8;
                border-color: #17a2b8;
                color: white;
            }
            #viewBackgroundBtn:hover {
                background-color: #138496;
                border-color: #117a8b;
            }
            #deleteBackgroundBtn {
                background-color: #dc3545;
                border-color: #dc3545;
                color: white;
            }
            #deleteBackgroundBtn:hover {
                background-color: #c82333;
                border-color: #bd2130;
            }
        `;
        document.head.appendChild(style);
        
        // 设置模态框HTML内容
        modal.innerHTML = `
            <div class="modal-content scene-modal">
                <div class="modal-header">
                    <h3 id="sceneModalTitle"><i class="fas fa-layer-group me-2"></i>新建场景</h3>
                    <span class="close" id="sceneModalClose">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="sceneForm" class="scene-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="sceneName">场景名称</label>
                                <input type="text" id="sceneName" name="sceneName" class="form-control" required>
                            </div> 
                        </div>
                        <div class="form-row"> 
                            <div class="form-group">
                                <label for="sceneWidth">宽度(px)</label>
                                <input type="number" id="sceneWidth" name="sceneWidth" class="form-control" value="800" min="100" max="2000">
                            </div>
                            <div class="form-group">
                                <label for="sceneHeight">高度(px)</label>
                                <input type="number" id="sceneHeight" name="sceneHeight" class="form-control" value="600" min="100" max="1500">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="originX">原点X</label>
                                <input type="number" id="originX" name="originX" class="form-control" value="0" step="0.1">
                            </div>
                            <div class="form-group">
                                <label for="originY">原点Y</label>
                                <input type="number" id="originY" name="originY" class="form-control" value="0" step="0.1">
                            </div> 
                        </div>
                        <div class="form-row"> 
                            <div class="form-group">
                                <label for="scaleX">X轴缩放</label>
                                <input type="number" id="scaleX" name="scaleX" class="form-control" value="1.0" step="0.1" min="0.1">
                            </div>
                            <div class="form-group">
                                <label for="scaleY">Y轴缩放</label>
                                <input type="number" id="scaleY" name="scaleY" class="form-control" value="1.0" step="0.1" min="0.1">
                            </div>
                        </div>
                        <div class="form-row"> 
                            <div class="form-group">
                                <label for="invertY">Y轴反转</label>
                                <div class="form-check">
                                    <input type="checkbox" id="invertY" name="invertY" class="form-check-input">
                                    <label class="form-check-label" for="invertY">启用</label>
                                </div>
                            </div> 
                        </div>
                        <div class="form-row"> 
                            <div class="form-group">
                                <label for="backgroundImage">背景图片</label>
                                <div class="background-image-container">
                                    <input type="file" id="backgroundImage" name="backgroundImage" class="form-control" accept="image/*">
                                    <div id="backgroundImagePreview" class="background-image-preview" style="display: none;">
                                        <div class="preview-info">
                                            <span id="backgroundImageName"></span>
                                            <div class="background-actions">
                                                <button type="button" id="viewBackgroundBtn" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> 查看图片
                                                </button>
                                                <button type="button" id="deleteBackgroundBtn" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> 删除背景
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" id="keepBackgroundImage" name="keepBackgroundImage" value="true">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="serverIp">服务器IP</label>
                                <input type="text" id="serverIp" name="serverIp" class="form-control" value="127.0.0.1">
                            </div>
                            <div class="form-group">
                                <label for="serverPort">服务器端口</label>
                                <input type="number" id="serverPort" name="serverPort" class="form-control" value="8080" min="1" max="65535">
                            </div> 
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="sceneCancelBtn"><i class="fas fa-times me-1"></i>取消</button>
                    <button type="button" class="btn btn-primary" id="sceneSaveBtn"><i class="fas fa-save me-1"></i>保存</button>
                </div>
            </div>
        `;
        
        // 添加到body
        document.body.appendChild(modal);
        
        // 绑定事件
        const closeBtn = modal.querySelector('#sceneModalClose');
        const cancelBtn = modal.querySelector('#sceneCancelBtn');
        const saveBtn = modal.querySelector('#sceneSaveBtn');
        const backgroundImageInput = modal.querySelector('#backgroundImage');
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideSceneModal());
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideSceneModal());
        }
        
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveScene());
        }
        
        // 添加背景图片选择事件
        if (backgroundImageInput) {
            backgroundImageInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    // 显示预览区域
                    const backgroundImagePreview = document.getElementById('backgroundImagePreview');
                    const backgroundImageName = document.getElementById('backgroundImageName');
                    
                    backgroundImagePreview.style.display = 'block';
                    backgroundImageName.textContent = `已选择: ${file.name}`;
                    
                    // 实时预览图片
                    if (this.app.canvasManager) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                            // 创建临时图片对象
                            const img = new Image();
                            img.onload = () => {
                                // 设置为临时背景并重绘
                                this.app.canvasManager.backgroundImage = img;
                                this.app.canvasManager.redrawCanvas();
                            };
                            img.src = event.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                }
            });
        }
        
        return modal;
    }

    // 保存场景
    async saveScene() {
        const form = document.getElementById('sceneForm');
        if (!form) {
            console.error('找不到场景表单');
            return;
        }

        // 获取隐藏的ID字段
        const idInput = form.querySelector('input[name="id"]');
        const isEditMode = idInput && idInput.value && idInput.value !== '00000000-0000-0000-0000-000000000000';
        
        // 收集表单数据
        const sceneData = {
            name: document.getElementById('sceneName').value || '未命名场景',
            width: parseInt(document.getElementById('sceneWidth').value) || 800,
            height: parseInt(document.getElementById('sceneHeight').value) || 600,
            originX: parseFloat(document.getElementById('originX').value) || 0,
            originY: parseFloat(document.getElementById('originY').value) || 0,
            pixelToMeterRatio: 1.0, // 添加默认值
            scaleX: parseFloat(document.getElementById('scaleX').value) || 1.0,
            scaleY: parseFloat(document.getElementById('scaleY').value) || 1.0,
            invertY: document.getElementById('invertY').checked,
            serverIp: document.getElementById('serverIp').value || '127.0.0.1',
            serverPort: parseInt(document.getElementById('serverPort').value) || 8080
        };
        
        // 处理背景图片路径
        const keepBackgroundImage = document.getElementById('keepBackgroundImage').value === 'true';
        if (isEditMode && keepBackgroundImage && form.dataset.originalBackgroundPath) {
            // 如果是编辑模式且保留原背景图片，则使用原始背景图片路径
            sceneData.backgroundImagePath = form.dataset.originalBackgroundPath;
        }
        
        // 只有在编辑模式时才添加ID
        if (isEditMode) {
            sceneData.id = idInput.value;
        }

        // 验证必填字段
        if (!sceneData.name) {
            alert('请填写场景名称');
            return;
        }

        try {
            // 判断是否为新建场景：没有ID或ID为空或ID为全0的GUID
            const isNew = !sceneData.id || sceneData.id === '' || sceneData.id === '00000000-0000-0000-0000-000000000000';
            const url = isNew ? '/api/scenes' : `/api/scenes/${sceneData.id}`;
            const method = isNew ? 'POST' : 'PUT';
            
            // 获取CSRF令牌
            const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': token
                },
                body: JSON.stringify(sceneData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || '保存场景失败');
            }

            const result = await response.json();
            
            // 处理背景图片上传
            const backgroundImage = document.getElementById('backgroundImage')?.files[0];
            const keepBackgroundImage = document.getElementById('keepBackgroundImage').value === 'true';
            
            // 如果选择了新的背景图片，则上传
            if (backgroundImage) {
                try {
                    // 上传背景图片并获取返回结果
                    const uploadResult = await this.uploadBackgroundImage(result.id, backgroundImage);
                    console.log('背景图片上传成功:', uploadResult);
                    
                    // 如果上传成功，需要更新场景的backgroundImagePath
                    if (uploadResult && uploadResult.fileName) {
                        // 更新场景对象的backgroundImagePath属性
                        const updateResponse = await fetch(`/api/scenes/${result.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': token
                            },
                            body: JSON.stringify({
                                ...result,
                                backgroundImagePath: uploadResult.fileName
                            })
                        });
                        
                        if (!updateResponse.ok) {
                            console.warn('更新场景背景图片路径失败，但图片已上传');
                        } else {
                            console.log('场景背景图片路径已更新');
                        }
                    }
                } catch (uploadError) {
                    console.error('上传背景图片失败:', uploadError);
                    // 不阻止场景保存成功，只显示警告
                    alert('场景已保存，但上传背景图片时出错: ' + uploadError.message);
                }
            } 
            // 如果是编辑模式且选择了删除背景图片
            else if (isEditMode && !keepBackgroundImage) {
                console.log('删除场景背景图片');
                // 更新场景对象，清除backgroundImagePath
                const updateResponse = await fetch(`/api/scenes/${result.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    },
                    body: JSON.stringify({
                        ...result,
                        backgroundImagePath: null
                    })
                });
                
                if (!updateResponse.ok) {
                    console.warn('更新场景背景图片路径失败');
                } else {
                    console.log('场景背景图片已删除');
                }
            }

            // 重新加载场景列表
            await this.loadScenes();
            this.hideSceneModal();
            
            return result;
        } catch (error) {
            console.error('保存场景失败:', error);
            alert('保存场景失败: ' + (error.message || '未知错误'));
            throw error;
        }
    }

    // 上传背景图片
    async uploadBackgroundImage(sceneId, file) {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch(`/api/scenes/${sceneId}/background`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error('上传背景图片失败');
            }

            return await response.json();
        } catch (error) {
            console.error('上传背景图片失败:', error);
            throw error;
        }
    }
}
