using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TrajectoryAuto.Core.Entities;

namespace TrajectoryAuto.Core.Interfaces;

/// <summary>
/// 高性能轨迹播放管理器接口
/// 解决多场景并发播放时的性能问题
/// </summary>
public interface IPerformantPlaybackManager : IDisposable
{
    /// <summary>
    /// 启动场景播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <param name="trajectories">轨迹列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>播放任务</returns>
    Task<bool> StartScenePlaybackAsync(Guid sceneId, List<Trajectory> trajectories, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 停止场景播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>是否成功停止</returns>
    Task<bool> StopScenePlaybackAsync(Guid sceneId);
    
    /// <summary>
    /// 启动通道播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <param name="trajectories">轨迹列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>播放任务</returns>
    Task<bool> StartChannelPlaybackAsync(Guid channelId, List<Trajectory> trajectories, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 停止通道播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <returns>是否成功停止</returns>
    Task<bool> StopChannelPlaybackAsync(Guid channelId);
    
    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计</returns>
    PlaybackPerformanceStats GetPerformanceStats();
    
    /// <summary>
    /// 清理过期的播放状态
    /// </summary>
    /// <returns>清理的条目数量</returns>
    Task<int> CleanupExpiredStatesAsync();
    
    /// <summary>
    /// 获取活跃的播放任务数量
    /// </summary>
    /// <returns>任务数量统计</returns>
    PlaybackTaskStats GetTaskStats();
}

/// <summary>
/// 播放性能统计信息
/// </summary>
public class PlaybackPerformanceStats
{
    /// <summary>
    /// 活跃场景数量
    /// </summary>
    public int ActiveSceneCount { get; set; }
    
    /// <summary>
    /// 活跃通道数量
    /// </summary>
    public int ActiveChannelCount { get; set; }
    
    /// <summary>
    /// 活跃轨迹数量
    /// </summary>
    public int ActiveTrajectoryCount { get; set; }
    
    /// <summary>
    /// 总UDP发送次数
    /// </summary>
    public long TotalUdpSentCount { get; set; }
    
    /// <summary>
    /// 平均UDP发送延迟（毫秒）
    /// </summary>
    public double AverageUdpLatencyMs { get; set; }
    
    /// <summary>
    /// 内存使用情况（MB）
    /// </summary>
    public double MemoryUsageMB { get; set; }
    
    /// <summary>
    /// 活跃线程数量
    /// </summary>
    public int ActiveThreadCount { get; set; }
    
    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 播放任务统计信息
/// </summary>
public class PlaybackTaskStats
{
    /// <summary>
    /// 场景级任务数量
    /// </summary>
    public int SceneTaskCount { get; set; }
    
    /// <summary>
    /// 通道级任务数量
    /// </summary>
    public int ChannelTaskCount { get; set; }
    
    /// <summary>
    /// 轨迹级任务数量
    /// </summary>
    public int TrajectoryTaskCount { get; set; }
    
    /// <summary>
    /// 待清理的任务数量
    /// </summary>
    public int PendingCleanupCount { get; set; }
    
    /// <summary>
    /// 线程池队列长度
    /// </summary>
    public long ThreadPoolQueueLength { get; set; }
    
    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.Now;
}
