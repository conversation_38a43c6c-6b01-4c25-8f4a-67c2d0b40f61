using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using TrajectoryAuto.Core.Interfaces;

namespace TrajectoryAuto.Infrastructure.Services;

/// <summary>
/// 播放状态服务实现 - 管理多场景、多通道的播放状态（单例）
/// </summary>
public class PlaybackStateService : IPlaybackStateService
{
    #region 私有字段
    
    // 全局播放状态
    private volatile bool _globalStopRequested = false;
    private readonly object _globalLock = new object();
    
    // 场景播放状态 - 使用线程安全的字典
    private readonly ConcurrentDictionary<Guid, bool> _scenePlaybackStates = new();
    private readonly ConcurrentDictionary<Guid, bool> _sceneStopRequests = new();
    
    // 通道播放状态 - 使用线程安全的字典
    private readonly ConcurrentDictionary<Guid, bool> _channelPlaybackStates = new();
    private readonly ConcurrentDictionary<Guid, bool> _channelStopRequests = new();
    private readonly ConcurrentDictionary<Guid, Guid> _channelToSceneMapping = new(); // 通道到场景的映射
    
    #endregion
    
    #region 全局播放状态管理

    /// <summary>
    /// 设置全局停止标志
    /// </summary>
    public void SetGlobalStop()
    {
        lock (_globalLock)
        {
            _globalStopRequested = true;
        }
    }

    /// <summary>
    /// 重置全局停止标志，允许新的播放开始
    /// </summary>
    public void ResetGlobalStop()
    {
        lock (_globalLock)
        {
            _globalStopRequested = false;
        }
    }

    /// <summary>
    /// 检查是否已请求全局停止
    /// </summary>
    /// <returns>如果已请求停止返回true</returns>
    public bool IsGlobalStopRequested()
    {
        lock (_globalLock)
        {
            return _globalStopRequested;
        }
    }
    
    #endregion
    
    #region 场景级别播放状态管理
    
    /// <summary>
    /// 开始场景播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    public void StartScenePlayback(Guid sceneId)
    {
        _scenePlaybackStates[sceneId] = true;
        _sceneStopRequests[sceneId] = false;
    }
    
    /// <summary>
    /// 停止场景播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    public void StopScenePlayback(Guid sceneId)
    {
        _sceneStopRequests[sceneId] = true;
        _scenePlaybackStates[sceneId] = false;
        
        // 同时停止该场景下的所有通道
        StopAllChannelsInScene(sceneId);
    }
    
    /// <summary>
    /// 检查场景是否正在播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>如果场景正在播放返回true</returns>
    public bool IsScenePlaybackActive(Guid sceneId)
    {
        return _scenePlaybackStates.GetValueOrDefault(sceneId, false);
    }
    
    /// <summary>
    /// 检查场景是否已请求停止
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>如果场景已请求停止返回true</returns>
    public bool IsSceneStopRequested(Guid sceneId)
    {
        return _sceneStopRequests.GetValueOrDefault(sceneId, false);
    }
    
    /// <summary>
    /// 获取所有正在播放的场景ID
    /// </summary>
    /// <returns>正在播放的场景ID列表</returns>
    public IReadOnlyList<Guid> GetActiveScenes()
    {
        return _scenePlaybackStates
            .Where(kvp => kvp.Value)
            .Select(kvp => kvp.Key)
            .ToList();
    }
    
    #endregion
    
    #region 通道级别播放状态管理
    
    /// <summary>
    /// 开始通道播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">所属场景ID</param>
    public void StartChannelPlayback(Guid channelId, Guid sceneId)
    {
        _channelPlaybackStates[channelId] = true;
        _channelStopRequests[channelId] = false;
        _channelToSceneMapping[channelId] = sceneId;
        
        // 确保场景也处于播放状态
        if (!IsScenePlaybackActive(sceneId))
        {
            StartScenePlayback(sceneId);
        }
    }
    
    /// <summary>
    /// 停止通道播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    public void StopChannelPlayback(Guid channelId)
    {
        _channelStopRequests[channelId] = true;
        _channelPlaybackStates[channelId] = false;
    }
    
    /// <summary>
    /// 检查通道是否正在播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <returns>如果通道正在播放返回true</returns>
    public bool IsChannelPlaybackActive(Guid channelId)
    {
        return _channelPlaybackStates.GetValueOrDefault(channelId, false);
    }
    
    /// <summary>
    /// 检查通道是否已请求停止
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <returns>如果通道已请求停止返回true</returns>
    public bool IsChannelStopRequested(Guid channelId)
    {
        return _channelStopRequests.GetValueOrDefault(channelId, false);
    }
    
    /// <summary>
    /// 获取场景下所有正在播放的通道ID
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>正在播放的通道ID列表</returns>
    public IReadOnlyList<Guid> GetActiveChannelsInScene(Guid sceneId)
    {
        return _channelToSceneMapping
            .Where(kvp => kvp.Value == sceneId && IsChannelPlaybackActive(kvp.Key))
            .Select(kvp => kvp.Key)
            .ToList();
    }
    
    /// <summary>
    /// 停止场景下所有通道的播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    public void StopAllChannelsInScene(Guid sceneId)
    {
        var channelsInScene = _channelToSceneMapping
            .Where(kvp => kvp.Value == sceneId)
            .Select(kvp => kvp.Key)
            .ToList();
            
        foreach (var channelId in channelsInScene)
        {
            StopChannelPlayback(channelId);
        }
    }
    
    #endregion
    
    #region 轨迹级别播放状态管理
    
    /// <summary>
    /// 检查轨迹是否应该停止（综合检查全局、场景、通道停止状态）
    /// </summary>
    /// <param name="trajectoryId">轨迹ID</param>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">场景ID</param>
    /// <returns>如果轨迹应该停止返回true</returns>
    public bool ShouldTrajectoryStop(Guid trajectoryId, Guid channelId, Guid sceneId)
    {
        // 第一层：检查全局停止状态
        if (IsGlobalStopRequested())
        {
            return true;
        }
        
        // 第二层：检查场景停止状态
        if (IsSceneStopRequested(sceneId))
        {
            return true;
        }
        
        // 第三层：检查通道停止状态
        if (IsChannelStopRequested(channelId))
        {
            return true;
        }
        
        return false;
    }
    
    #endregion
}
