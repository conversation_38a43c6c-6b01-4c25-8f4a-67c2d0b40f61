using TrajectoryAuto.Core.Entities;

namespace TrajectoryAuto.Core.Interfaces;

/// <summary>
/// 场景服务接口
/// </summary>
public interface ISceneService
{
    /// <summary>
    /// 获取所有场景
    /// </summary>
    /// <returns></returns>
    Task<List<Scene>> GetAllScenesAsync();
    
    /// <summary>
    /// 根据ID获取场景
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<Scene?> GetSceneByIdAsync(Guid id);
    
    /// <summary>
    /// 创建场景
    /// </summary>
    /// <param name="scene"></param>
    /// <returns></returns>
    Task<Scene> CreateSceneAsync(Scene scene);
    
    /// <summary>
    /// 更新场景
    /// </summary>
    /// <param name="scene"></param>
    /// <returns></returns>
    Task<Scene> UpdateSceneAsync(Scene scene);
    
    /// <summary>
    /// 删除场景
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> DeleteSceneAsync(Guid id);
    
    /// <summary>
    /// 上传背景图片
    /// </summary>
    /// <param name="sceneId"></param>
    /// <param name="imageStream"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    Task<string> UploadBackgroundImageAsync(Guid sceneId, Stream imageStream, string fileName);
}