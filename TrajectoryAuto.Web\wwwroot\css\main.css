/* 精简的主样式文件 - 合并优化版 */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #00d2d3;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --dark-color: #2f3542;
    --light-color: #f1f2f6;
    --border-color: #e8ecf0;
    --text-color: #333;
    --shadow: 0 2px 8px rgba(0,0,0,0.1);
    --gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* 全局样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    height: 100%;
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    background-color: #f5f7fa;
    color: var(--text-color);
    overflow: hidden;
}

/* 应用布局 */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

.app-header {
    background: var(--gradient);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.app-main {
    display: flex;
    flex: 1;
    overflow: hidden;
    gap: 8px;
    padding: 8px;
}

/* 面板样式 */
.panel {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
}

.scene-panel {
    width: 240px;
}

.channel-panel {
    width: 260px;
}

.canvas-panel {
    flex: 1;
    background-color: #f8f9fa;
}

.panel-header {
    padding: 1rem 1.25rem;
    border-bottom: 2px solid var(--primary-color);
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
}

.panel-header h2 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

/* 画布区域 */
.canvas-toolbar {
    padding: 0.75rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
}

.canvas-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#trajectoryCanvas {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
    cursor: crosshair;
    user-select: none;
    background: white;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.btn-primary {
    background: var(--gradient);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: #212529;
}

.btn-small {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

/* 列表样式 */
.list {
    flex: 1;
    overflow-y: auto;
    padding: 0.75rem;
}

.list-item {
    padding: 1rem;
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.list-item:hover {
    background: #f8f9ff;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.list-item.active {
    background: var(--gradient);
    color: white;
    border-color: var(--primary-color);
}

.list-item h4 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.list-item p {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1rem 1.5rem;
    background: var(--gradient);
    color: white;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-main {
        flex-direction: column;
        gap: 4px;
    }
    
    .scene-panel, 
    .channel-panel {
        width: 100%;
        height: 150px;
    }
    
    .canvas-panel {
        flex: 1;
        min-height: 400px;
    }
    
    .canvas-toolbar {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .toolbar {
        flex-wrap: wrap;
    }
}

/* 实用工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.ml-auto { margin-left: auto; }
.mr-auto { margin-right: auto; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }