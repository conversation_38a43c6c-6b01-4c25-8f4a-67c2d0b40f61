using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Core.Interfaces;

/// <summary>
/// 轨迹服务接口
/// </summary>
public interface ITrajectoryService
{
    /// <summary>
    /// 获取通道的所有轨迹
    /// </summary>
    /// <param name="channelId"></param>
    /// <returns></returns>
    Task<List<Trajectory>> GetTrajectoriesByChannelIdAsync(Guid channelId);
    
    /// <summary>
    /// 根据ID获取轨迹
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<Trajectory?> GetTrajectoryByIdAsync(Guid id);
    
    /// <summary>
    /// 创建轨迹
    /// </summary>
    /// <param name="trajectory"></param>
    /// <returns></returns>
    Task<Trajectory> CreateTrajectoryAsync(Trajectory trajectory);
    
    /// <summary>
    /// 更新轨迹
    /// </summary>
    /// <param name="trajectory"></param>
    /// <returns></returns>
    Task<Trajectory> UpdateTrajectoryAsync(Trajectory trajectory);
    
    /// <summary>
    /// 删除轨迹
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> DeleteTrajectoryAsync(Guid id);
    
    /// <summary>
    /// 清除通道的所有轨迹
    /// </summary>
    /// <param name="channelId"></param>
    /// <returns></returns>
    Task<bool> ClearChannelTrajectoriesAsync(Guid channelId);
    
    /// <summary>
    /// 播放轨迹
    /// </summary>
    /// <param name="trajectoryId">轨迹ID</param>
    /// <param name="enableZAxis">是否启用Z轴</param>
    /// <param name="minZ">Z轴最小值</param>
    /// <param name="maxZ">Z轴最大值</param>
    /// <returns></returns>
    Task<bool> PlayTrajectoryAsync(Guid trajectoryId, bool enableZAxis = false, double minZ = 0, double maxZ = 100);
    
    /// <summary>
    /// 停止轨迹播放
    /// </summary>
    /// <param name="trajectoryId"></param>
    /// <returns></returns>
    Task<bool> StopTrajectoryAsync(Guid trajectoryId);
    
    /// <summary>
    /// 停止所有轨迹播放
    /// </summary>
    /// <returns></returns>
    Task<bool> StopAllPlaybackAsync();
    
    /// <summary>
    /// 获取轨迹播放状态
    /// </summary>
    /// <param name="trajectoryId"></param>
    /// <returns></returns>
    Task<PlaybackStatus> GetPlaybackStatusAsync(Guid trajectoryId);
}