using System.Net;
using System.Net.Sockets;
using System.Text;
using Microsoft.Extensions.Logging;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Infrastructure.Services;

/// <summary>
/// 通信服务实现
/// </summary>
public class CommunicationService : ICommunicationService, IDisposable, IAsyncDisposable
{
    private readonly ILogger<CommunicationService> _logger;
    private readonly IUdpConnectionPoolManager _connectionPoolManager;
    private readonly DirectUdpService _directUdpService;
    
    // 当前活动的连接信息
    private UdpConnectionInfo? _currentConnection;
    private int _currentSceneId;
    private int _currentChannelId;
    private string _currentServerIp = string.Empty;
    private int _currentServerPort;
    
    private bool _isRealTimeMode = false;
    private volatile bool _allowSending = true;
    private readonly object _sendingLock = new object();
    
    // 是否使用直接UDP服务（不使用连接池）
    private bool _useDirectUdp = true;
    
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="connectionPoolManager">UDP连接池管理器</param>
    /// <param name="directUdpService">直接UDP服务</param>
    public CommunicationService(
        ILogger<CommunicationService> logger,
        IUdpConnectionPoolManager connectionPoolManager,
        DirectUdpService directUdpService)
    {
        _logger = logger;
        _connectionPoolManager = connectionPoolManager;
        _directUdpService = directUdpService;
    }

    public async Task<bool> SendCoordinateDataAsync(string serverIp, int serverPort, CoordinateData coordinateData)
    {
        // 首先检查是否允许发送数据
        if (!_allowSending)
        {
            _logger.LogDebug("发送已停止，忽略发送请求");
            return false;
        }

        try
        {
            lock (_sendingLock)
            {
                // 在锁内再次检查，确保在获取锁的过程中状态没有改变
                if (!_allowSending)
                {
                    _logger.LogDebug("发送已停止（锁内检查），忽略发送请求");
                    return false;
                }
                
                // 只有在实时通信模式下才发送数据
                if (!_isRealTimeMode)
                {
                    _logger.LogDebug("非实时通信模式，忽略发送请求");
                    return false;
                }
            }
            
            // 使用格式化的坐标数据字符串：{channelAddress}{X},{Y},{Z}
            string formattedData = coordinateData.GetFormattedCoordinateString();
            
            // 发送前最后一次检查
            if (!_allowSending || !_isRealTimeMode)
            {
                _logger.LogDebug("发送已停止（发送前检查），忽略发送请求");
                return false;
            }
            
            // 根据配置选择使用直接UDP服务还是连接池
            if (_useDirectUdp)
            {
                _logger.LogInformation($"使用直接UDP服务发送数据: {formattedData} 到 {serverIp}:{serverPort}");
                return await _directUdpService.SendCoordinateDataAsync(serverIp, serverPort, coordinateData);
            }
            else
            {
                // 使用连接池发送数据
                var data = Encoding.UTF8.GetBytes(formattedData);
                
                // 使用当前连接发送数据
                if (_currentConnection != null)
                {
                    // 使用调试级别日志，避免与TrajectoryHub中的日志重复
                    if (System.Diagnostics.Debugger.IsAttached)
                    {
                        _logger.LogDebug($"发送UDP数据: {formattedData} 到 {serverIp}:{serverPort}");
                    }
                    
                    return await _connectionPoolManager.SendDataAsync(_currentConnection, data);
                }
                else
                {
                    _logger.LogWarning("当前没有活动的UDP连接，无法发送数据");
                    return false;
                }
            }
        }
        catch (Exception ex)
        {
            // 记录日志
            _logger.LogError(ex, $"发送坐标数据失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> SendBatchCoordinateDataAsync(string serverIp, int serverPort, List<CoordinateData> coordinateDataList)
    {
        // 首先检查是否允许发送数据
        if (!_allowSending)
        {
            _logger.LogDebug("发送已停止，忽略批量发送请求");
            return false;
        }
        
        try
        {
            // 创建临时连接用于批量发送
            var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
            
            // 使用通道连接（假设批量发送是针对某个通道的）
            var tempConnection = await _connectionPoolManager.GetChannelConnectionAsync(_currentChannelId, _currentSceneId, endpoint);
            
            // 准备数据列表
            var dataList = new List<byte[]>();
            foreach (var coordinateData in coordinateDataList)
            {
                // 使用格式化的坐标数据字符串：{channelAddress}{X},{Y},{Z}
                string formattedData = coordinateData.GetFormattedCoordinateString();
                var data = Encoding.UTF8.GetBytes(formattedData);
                dataList.Add(data);
                
                _logger.LogDebug($"准备批量发送UDP数据: {formattedData} 到 {serverIp}:{serverPort}");
            }
            
            // 批量发送数据
            int successCount = await _connectionPoolManager.SendBatchDataAsync(tempConnection, dataList);
            
            // 释放临时连接
            await _connectionPoolManager.ReleaseChannelConnectionAsync(_currentChannelId);
            
            return successCount == coordinateDataList.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"批量发送坐标数据失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> TestConnectionAsync(string serverIp, int serverPort)
    {
        try
        {
            // 创建临时端点
            var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
            
            // 创建临时连接用于测试
            var tempConnection = await _connectionPoolManager.GetChannelConnectionAsync(0, 0, endpoint);
            
            // 发送测试数据
            var testData = new CoordinateData
            {
                ChannelNumber = 0,
                X = 0,
                Y = 0,
                Z = 0,
                ChannelAddress = "TEST",
                Timestamp = DateTime.Now
            };
            
            // 使用格式化的坐标数据字符串
            string formattedData = testData.GetFormattedCoordinateString();
            var data = Encoding.UTF8.GetBytes(formattedData);
            
            _logger.LogInformation($"发送测试UDP数据: {formattedData} 到 {serverIp}:{serverPort}");
            
            bool result = await _connectionPoolManager.SendDataAsync(tempConnection, data);
            
            // 释放临时连接
            await _connectionPoolManager.ReleaseChannelConnectionAsync(0);
            
            // 等待响应（可选，取决于服务器是否回复）
            await Task.Delay(1000);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"测试连接失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> StartRealTimeCommunicationAsync(string serverIp, int serverPort, int sceneId = 0, int channelId = 0)
    {
        try
        {
            if (_isRealTimeMode)
                await StopRealTimeCommunicationAsync();

            // 设置当前连接信息
            _currentServerIp = serverIp;
            _currentServerPort = serverPort;
            
            // 使用传入的场景ID和通道ID，如果没有传入则使用默认值
            _currentSceneId = sceneId > 0 ? sceneId : 1;
            _currentChannelId = channelId > 0 ? channelId : 1;
            
            _logger.LogInformation($"启动实时通信，使用场景ID: {_currentSceneId}, 通道ID: {_currentChannelId}");
            
            if (_useDirectUdp)
            {
                // 使用直接UDP服务，测试连接
                bool testResult = await _directUdpService.TestConnectionAsync(serverIp, serverPort);
                if (testResult)
                {
                    _logger.LogInformation($"直接UDP服务测试连接成功: {serverIp}:{serverPort}");
                }
                else
                {
                    _logger.LogWarning($"直接UDP服务测试连接失败: {serverIp}:{serverPort}，但仍将继续");
                }
            }
            else
            {
                // 使用连接池
                // 创建端点
                var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                
                // 获取或创建通道连接
                _currentConnection = await _connectionPoolManager.GetChannelConnectionAsync(_currentChannelId, _currentSceneId, endpoint);
            }
            
            _isRealTimeMode = true;
            _allowSending = true; // 允许发送数据
            
            _logger.LogInformation($"已启动实时通信模式，服务器: {serverIp}:{serverPort}，场景ID: {_currentSceneId}，通道ID: {_currentChannelId}");
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"启动实时通信失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> StopRealTimeCommunicationAsync()
    {
        try
        {
            // 只有当实际处于实时模式或有当前连接时才输出日志
            bool wasActive = _isRealTimeMode || _currentConnection != null;
            
            if (wasActive)
            {
                _logger.LogInformation("开始停止实时通信...");
            }
            
            // 首先设置标志，禁止发送数据
            lock (_sendingLock)
            {
                _allowSending = false;
                if (wasActive)
                {
                    _logger.LogDebug("已设置禁止发送标志（在锁内）");
                }
            }
            
            // 等待一小段时间，确保正在进行的发送操作完成
            if (wasActive)
            {
                await Task.Delay(100);
            }
            
            if (!_useDirectUdp)
            {
                // 释放当前通道连接
                if (_currentConnection != null && _currentChannelId > 0)
                {
                    try
                    {
                        // 先关闭和释放UDP客户端，确保下次重新创建
                        try
                        {
                            _currentConnection.UdpClient?.Close();
                            _currentConnection.UdpClient?.Dispose();
                            _logger.LogInformation($"已关闭UDP客户端: 通道ID={_currentChannelId}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"关闭UDP客户端时出错: {ex.Message}");
                        }
                        
                        // 然后释放通道连接
                        await _connectionPoolManager.ReleaseChannelConnectionAsync(_currentChannelId);
                        _logger.LogInformation($"已释放通道连接: 通道ID={_currentChannelId}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"释放通道连接时出错: {ex.Message}");
                    }
                    finally
                    {
                        _currentConnection = null;
                    }
                }
            }
            
            // 重置实时通信模式标志
            _isRealTimeMode = false;
            
            // 重要：在停止通信后，重置允许发送标志，以便下次可以正常发送
            lock (_sendingLock)
            {
                _allowSending = true;
                _logger.LogDebug("已重置允许发送标志，为下次通信做准备");
            }
            
            if (wasActive)
            {
                _logger.LogInformation("实时通信已完全停止，并已重置状态为可用");
            }
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"停止实时通信失败: {ex.Message}");
            // 即使出错也要确保标志被正确设置
            lock (_sendingLock)
            {
                _isRealTimeMode = false;
                _allowSending = true; // 重置为可用状态
                _logger.LogDebug("异常处理中已重置通信状态为可用");
            }
            return false;
        }
    }
    
    /// <summary>
    /// 检查实时通信状态
    /// </summary>
    /// <returns>如果实时通信已启动返回true，否则返回false</returns>
    public bool IsRealTimeCommunicationStarted()
    {
        lock (_sendingLock)
        {
            return _isRealTimeMode && _currentConnection != null;
        }
    }
    
    /// <summary>
    /// 检查是否允许发送数据
    /// </summary>
    /// <returns>如果允许发送数据返回true，否则返回false</returns>
    public bool IsAllowedToSend()
    {
        lock (_sendingLock)
        {
            return _allowSending && _isRealTimeMode && _currentConnection != null;
        }
    }
    
    /// <summary>
    /// 设置当前场景和通道ID
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <param name="channelId">通道ID</param>
    public void SetSceneAndChannel(int sceneId, int channelId)
    {
        _currentSceneId = sceneId;
        _currentChannelId = channelId;
        _logger.LogInformation($"已设置当前场景ID: {sceneId}, 通道ID: {channelId}");
    }
    
    /// <summary>
    /// 停止指定场景的所有通信
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    public async Task StopSceneCommunicationAsync(int sceneId)
    {
        try
        {
            // 如果当前场景正在使用，先停止实时通信
            if (_currentSceneId == sceneId && _isRealTimeMode)
            {
                await StopRealTimeCommunicationAsync();
            }
            
            // 释放场景下所有连接
            await _connectionPoolManager.ReleaseAllSceneConnectionsAsync(sceneId);
            _logger.LogInformation($"已停止场景通信: 场景ID={sceneId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"停止场景通信失败: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 停止指定通道的所有通信
    /// </summary>
    /// <param name="channelId">通道ID</param>
    public async Task StopChannelCommunicationAsync(int channelId)
    {
        try
        {
            // 如果当前通道正在使用，先停止实时通信
            if (_currentChannelId == channelId && _isRealTimeMode)
            {
                await StopRealTimeCommunicationAsync();
            }
            
            // 释放通道下所有连接
            await _connectionPoolManager.ReleaseAllChannelConnectionsAsync(channelId);
            _logger.LogInformation($"已停止通道通信: 通道ID={channelId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"停止通道通信失败: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 清理空闲连接
    /// </summary>
    /// <param name="idleTimeoutMinutes">空闲超时时间（分钟）</param>
    public async Task<int> CleanupIdleConnectionsAsync(int idleTimeoutMinutes = 10)
    {
        try
        {
            int cleanedCount = await _connectionPoolManager.CleanupIdleConnectionsAsync(idleTimeoutMinutes);
            _logger.LogInformation($"已清理{cleanedCount}个空闲UDP连接");
            return cleanedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"清理空闲连接失败: {ex.Message}");
            return 0;
        }
    }

    public void Dispose()
    {
        // 同步释放资源
        StopRealTimeCommunicationAsync().GetAwaiter().GetResult();
    }
    
    public async ValueTask DisposeAsync()
    {
        // 异步释放资源
        await StopRealTimeCommunicationAsync();
    }
}