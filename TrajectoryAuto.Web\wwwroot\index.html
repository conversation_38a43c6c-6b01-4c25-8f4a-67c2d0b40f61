<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>舞台轨迹自动化系统</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 头部导航 -->
        <header class="app-header">
            <h1>舞台轨迹自动化系统</h1>
            <div class="header-controls">
                <button id="connectBtn" class="btn btn-primary">连接服务器</button>
                <span id="connectionStatus" class="status-indicator">未连接</span>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="app-main">
            <!-- 左侧面板：场景管理 -->
            <aside class="scene-panel">
                <div class="panel-header">
                    <h2>场景管理</h2>
                    <button id="createSceneBtn" class="btn btn-small">新建场景</button>
                </div>
                <div class="scene-list" id="sceneList">
                    <!-- 场景列表将在这里动态生成 -->
                </div>
            </aside>

            <!-- 通道管理面板 -->
            <aside class="channel-panel">
                <div class="panel-header">
                    <h2>通道管理</h2>
                    <button id="createChannelBtn" class="btn btn-small">新建通道</button>
                </div>
                <div class="channel-list" id="channelList">
                    <!-- 通道列表将在这里动态生成 -->
                </div>
                
                <div class="trajectory-params">
                    <h3>轨迹参数</h3>
                    <div class="param-group">
                        <label for="trajectoryDuration">运行时间(秒):</label>
                        <input type="number" id="trajectoryDuration" value="10" min="1" max="3600">
                    </div>
                    <div class="param-group">
                        <label for="trajectoryLoop">
                            <input type="checkbox" id="trajectoryLoop">
                            循环播放
                        </label>
                    </div>
                </div>
            </aside>

            <!-- 中间面板：画布区域 -->
            <section class="canvas-panel">
                <div class="canvas-container">
                    <div class="canvas-toolbar">
                        <div class="tool-group">
                            <button id="selectTool" class="tool-btn active" data-tool="select" title="选择工具">
                                <span>选择</span>
                            </button>
                            <button id="circleTool" class="tool-btn" data-tool="circle" title="圆形轨迹">
                                <span>圆形</span>
                            </button>
                            <button id="rectangleTool" class="tool-btn" data-tool="rectangle" title="矩形轨迹">
                                <span>矩形</span>
                            </button>
                            <button id="polygonTool" class="tool-btn" data-tool="polygon" title="多边形轨迹">
                                <span>多边形</span>
                            </button>
                            <button id="freeDrawTool" class="tool-btn" data-tool="freedraw" title="自由绘制">
                                <span>自由绘制</span>
                            </button>
                            <button id="realTimeTool" class="tool-btn" data-tool="realtime" title="实时轨迹">
                                <span>实时</span>
                            </button>
                        </div>
                        <div class="control-group">
                            <button id="playBtn" class="btn btn-success">播放</button>
                            <button id="stopBtn" class="btn btn-danger">停止</button>
                            <button id="clearBtn" class="btn btn-warning">清除</button>
                        </div>
                    </div>
                    <div class="canvas-wrapper">
                        <canvas id="trajectoryCanvas" width="800" height="600"></canvas>
                        <div id="coordinateDisplay" class="coordinate-display">坐标: (0, 0)</div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 模态对话框 -->
    <div id="sceneModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="sceneModalTitle">新建场景</h3>
                <span class="close" id="sceneModalClose">&times;</span>
            </div>
            <div class="modal-body">
                <form id="sceneForm">
                    <div class="form-group">
                        <label for="sceneName">场景名称:</label>
                        <input type="text" id="sceneName" required>
                    </div>
                    <div class="form-group">
                        <label for="sceneWidth">宽度(像素):</label>
                        <input type="number" id="sceneWidth" value="800" min="100" max="2000">
                    </div>
                    <div class="form-group">
                        <label for="sceneHeight">高度(像素):</label>
                        <input type="number" id="sceneHeight" value="600" min="100" max="1500">
                    </div>
                    <div class="form-group">
                        <label for="serverIp">服务器IP:</label>
                        <input type="text" id="serverIp" value="127.0.0.1">
                    </div>
                    <div class="form-group">
                        <label for="serverPort">服务器端口:</label>
                        <input type="number" id="serverPort" value="8080" min="1" max="65535">
                    </div>
                    <div class="form-group">
                        <label for="backgroundImage">背景图片:</label>
                        <input type="file" id="backgroundImage" accept="image/*">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="sceneCancelBtn">取消</button>
                <button type="button" class="btn btn-primary" id="sceneSaveBtn">保存</button>
            </div>
        </div>
    </div>

    <div id="channelModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="channelModalTitle">新建通道</h3>
                <span class="close" id="channelModalClose">&times;</span>
            </div>
            <div class="modal-body">
                <form id="channelForm">
                    <div class="form-group">
                        <label for="channelName">通道名称:</label>
                        <input type="text" id="channelName" required>
                    </div>
                    <div class="form-group">
                        <label for="channelNumber">通道编号:</label>
                        <input type="number" id="channelNumber" min="1" max="999" required>
                    </div>
                    <div class="form-group">
                        <label for="channelIp">IP地址:</label>
                        <input type="text" id="channelIp" value="127.0.0.1">
                    </div>
                    <div class="form-group">
                        <label for="channelPort">端口:</label>
                        <input type="number" id="channelPort" value="8080" min="1" max="65535">
                    </div>
                    <div class="form-group">
                        <label for="channelColor">颜色:</label>
                        <input type="color" id="channelColor" value="#FF0000">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="channelCancelBtn">取消</button>
                <button type="button" class="btn btn-primary" id="channelSaveBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <script src="js/app.js"></script>
    <script src="js/canvas.js"></script>
    <script src="js/trajectory.js"></script>
</body>
</html>