using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Infrastructure.Services
{
    /// <summary>
    /// 直接UDP服务，不使用连接池，每次发送都创建新的UDP客户端
    /// </summary>
    public class DirectUdpService
    {
        private readonly ILogger<DirectUdpService> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public DirectUdpService(ILogger<DirectUdpService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 发送坐标数据
        /// </summary>
        /// <param name="serverIp">服务器IP</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="coordinateData">坐标数据</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> SendCoordinateDataAsync(string serverIp, int serverPort, CoordinateData coordinateData)
        {
            try
            {
                // 使用格式化的坐标数据字符串：{channelAddress}{X},{Y},{Z}
                string formattedData = coordinateData.GetFormattedCoordinateString();
                var data = Encoding.UTF8.GetBytes(formattedData);
                
                // 创建目标端点
                var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                
                // 每次发送都创建新的UDP客户端
                using (var udpClient = new UdpClient())
                {
                    // 发送数据
                    int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
                    
                    // 记录日志
                    _logger.LogInformation($"直接UDP服务发送数据: {formattedData} 到 {serverIp}:{serverPort}, 发送字节数: {bytesSent}");
                    
                    return bytesSent == data.Length;
                }
            }
            catch (Exception ex)
            {
                // 记录日志
                _logger.LogError(ex, $"直接UDP服务发送坐标数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试UDP连接
        /// </summary>
        /// <param name="serverIp">服务器IP</param>
        /// <param name="serverPort">服务器端口</param>
        /// <returns>是否连接成功</returns>
        public async Task<bool> TestConnectionAsync(string serverIp, int serverPort)
        {
            try
            {
                // 创建测试数据
                string testMessage = "TEST_CONNECTION";
                var data = Encoding.UTF8.GetBytes(testMessage);
                
                // 创建目标端点
                var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                
                // 创建UDP客户端
                using (var udpClient = new UdpClient())
                {
                    // 发送测试数据
                    int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
                    
                    // 记录日志
                    _logger.LogInformation($"UDP连接测试成功: {serverIp}:{serverPort}, 发送字节数: {bytesSent}");
                    
                    return bytesSent == data.Length;
                }
            }
            catch (Exception ex)
            {
                // 记录日志
                _logger.LogError(ex, $"UDP连接测试失败: {serverIp}:{serverPort}, 错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送轨迹数据
        /// </summary>
        /// <param name="serverIp">服务器IP</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="data">数据</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> SendTrajectoryDataAsync(string serverIp, int serverPort, byte[] data)
        {
            try
            {
                // 创建目标端点
                var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                
                // 创建UDP客户端
                using (var udpClient = new UdpClient())
                {
                    // 发送数据
                    int bytesSent = await udpClient.SendAsync(data, data.Length, endpoint);
                    
                    // 记录日志
                    _logger.LogInformation($"直接UDP服务发送轨迹数据到 {serverIp}:{serverPort}, 发送字节数: {bytesSent}");
                    
                    return bytesSent == data.Length;
                }
            }
            catch (Exception ex)
            {
                // 记录日志
                _logger.LogError(ex, $"直接UDP服务发送轨迹数据失败: {ex.Message}");
                return false;
            }
        }
    }
}