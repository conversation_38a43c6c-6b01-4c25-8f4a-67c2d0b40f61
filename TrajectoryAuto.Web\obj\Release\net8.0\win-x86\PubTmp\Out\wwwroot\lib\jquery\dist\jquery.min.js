/*! jQuery v3.7.1 - 简化版本用于轨迹自动化系统 */
(function(global, factory) {
    "use strict";
    if (typeof module === "object" && typeof module.exports === "object") {
        module.exports = global.document ?
            factory(global, true) :
            function(w) {
                if (!w.document) {
                    throw new Error("jQuery requires a window with a document");
                }
                return factory(w);
            };
    } else {
        factory(global);
    }
})(typeof window !== "undefined" ? window : this, function(window, noGlobal) {
    "use strict";
    
    var jQuery = function(selector, context) {
        return new jQuery.fn.init(selector, context);
    };
    
    jQuery.fn = jQuery.prototype = {
        jquery: "3.7.1",
        constructor: jQuery,
        length: 0,
        
        ready: function(fn) {
            if (document.readyState === "complete" || 
                (document.readyState !== "loading" && !document.documentElement.doScroll)) {
                setTimeout(fn, 1);
            } else {
                document.addEventListener("DOMContentLoaded", fn);
            }
            return this;
        },
        
        each: function(callback) {
            for (var i = 0; i < this.length; i++) {
                callback.call(this[i], i, this[i]);
            }
            return this;
        },
        
        on: function(events, handler) {
            return this.each(function() {
                this.addEventListener(events, handler);
            });
        },
        
        off: function(events, handler) {
            return this.each(function() {
                this.removeEventListener(events, handler);
            });
        },
        
        click: function(handler) {
            return handler ? this.on("click", handler) : this.trigger("click");
        },
        
        trigger: function(event) {
            return this.each(function() {
                var evt = new Event(event);
                this.dispatchEvent(evt);
            });
        },
        
        val: function(value) {
            if (arguments.length === 0) {
                return this[0] ? this[0].value : undefined;
            }
            return this.each(function() {
                this.value = value;
            });
        },
        
        text: function(text) {
            if (arguments.length === 0) {
                return this[0] ? this[0].textContent : undefined;
            }
            return this.each(function() {
                this.textContent = text;
            });
        },
        
        html: function(html) {
            if (arguments.length === 0) {
                return this[0] ? this[0].innerHTML : undefined;
            }
            return this.each(function() {
                this.innerHTML = html;
            });
        },
        
        addClass: function(className) {
            return this.each(function() {
                this.classList.add(className);
            });
        },
        
        removeClass: function(className) {
            return this.each(function() {
                this.classList.remove(className);
            });
        },
        
        hide: function() {
            return this.each(function() {
                this.style.display = "none";
            });
        },
        
        show: function() {
            return this.each(function() {
                this.style.display = "";
            });
        }
    };
    
    jQuery.fn.init = function(selector, context) {
        if (!selector) {
            return this;
        }
        
        if (typeof selector === "string") {
            var elements = (context || document).querySelectorAll(selector);
            for (var i = 0; i < elements.length; i++) {
                this[i] = elements[i];
            }
            this.length = elements.length;
        } else if (selector.nodeType) {
            this[0] = selector;
            this.length = 1;
        } else if (typeof selector === "function") {
            jQuery(document).ready(selector);
        }
        
        return this;
    };
    
    jQuery.fn.init.prototype = jQuery.fn;
    
    // AJAX support
    jQuery.ajax = function(options) {
        var xhr = new XMLHttpRequest();
        var method = options.type || options.method || 'GET';
        var url = options.url;
        var data = options.data;
        
        xhr.open(method, url, true);
        
        if (options.contentType !== false) {
            xhr.setRequestHeader('Content-Type', options.contentType || 'application/json');
        }
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status >= 200 && xhr.status < 300) {
                    if (options.success) {
                        var response = xhr.responseText;
                        try {
                            response = JSON.parse(response);
                        } catch (e) {}
                        options.success(response);
                    }
                } else {
                    if (options.error) {
                        options.error(xhr, xhr.statusText);
                    }
                }
            }
        };
        
        xhr.send(data ? JSON.stringify(data) : null);
    };
    
    // Global jQuery
    if (!noGlobal) {
        window.jQuery = window.$ = jQuery;
    }
    
    return jQuery;
});