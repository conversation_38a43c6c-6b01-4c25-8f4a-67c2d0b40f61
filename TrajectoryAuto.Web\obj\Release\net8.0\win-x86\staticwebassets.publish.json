{"Version": 1, "Hash": "KpOEJRI20beLt+p0GVcK4tXJIMHN4sQi0sOQ2NQgkKE=", "Source": "TrajectoryAuto.Web", "BasePath": "_content/TrajectoryAuto.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "TrajectoryAuto.Web\\wwwroot", "Source": "TrajectoryAuto.Web", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\all.min.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/all.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ywkkukpd6z", "Integrity": "HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\all.min.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\bootstrap.min.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u5b947y67y", "Integrity": "Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap.min.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\custom.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/custom#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "atdhu7b3gl", "Integrity": "0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\custom.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\modal.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/modal#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qedwpqxz3l", "Integrity": "4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\modal.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\playback.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/playback#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "flizyfk2xy", "Integrity": "4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\playback.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\site.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gekhf0kgg3", "Integrity": "iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\style.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/style#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xubv7qtpf4", "Integrity": "yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\style.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\trajectory.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/trajectory#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "etg9qljj4e", "Integrity": "YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\trajectory.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\trajectory-actions.css", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "css/trajectory-actions#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zrzw13xroc", "Integrity": "4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\trajectory-actions.css"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\index.html", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfc0agseey", "Integrity": "BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\app.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gtlg8ms8g6", "Integrity": "jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\canvas.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/canvas#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "uqsvneiks2", "Integrity": "RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\canvas.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\channelManager.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/channelManager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "trl6al0oog", "Integrity": "uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\channelManager.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\modalTest.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/modalTest#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qemmm9cnpe", "Integrity": "Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\modalTest.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\playbackManager.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/playbackManager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n541x1vj06", "Integrity": "S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\playbackManager.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\sceneManager.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/sceneManager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hmsqx3cwiz", "Integrity": "aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\sceneManager.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\site.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "50b1q86w11", "Integrity": "CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectory.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/trajectory#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "outbabz928", "Integrity": "4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\trajectory.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectoryManager.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/trajectoryManager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sf1oj4kvz9", "Integrity": "uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\trajectoryManager.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectoryManager.js.backup", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/trajectoryManager.js#[.{fingerprint}]?.backup", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "32cvhp1tpd", "Integrity": "NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\trajectoryManager.js.backup"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\uiManager.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "js/uiManager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o8c5mi9y29", "Integrity": "yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\uiManager.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4r87du48le", "Integrity": "EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ikicaa7rus", "Integrity": "AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\signalr\\signalr.min.js", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "lib/signalr/signalr.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ai9bojo3f9", "Integrity": "HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\signalr\\signalr.min.js"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hgzbx4oip4", "Integrity": "vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v5rxafksj3", "Integrity": "PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v5rxafksj3", "Integrity": "PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v5rxafksj3", "Integrity": "PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v5rxafksj3", "Integrity": "PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v5rxafksj3", "Integrity": "PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v5rxafksj3", "Integrity": "PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-brands-400.woff2", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "webfonts/fa-brands-400#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gjto57buk3", "Integrity": "dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\webfonts\\fa-brands-400.woff2"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-regular-400.woff2", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "webfonts/fa-regular-400#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yrqo96mk2j", "Integrity": "jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\webfonts\\fa-regular-400.woff2"}, {"Identity": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-solid-900.woff2", "SourceId": "TrajectoryAuto.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\", "BasePath": "_content/TrajectoryAuto.Web", "RelativePath": "webfonts/fa-solid-900#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bxa1shs6v7", "Integrity": "cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\webfonts\\fa-solid-900.woff2"}], "Endpoints": [{"Route": "css/all.min.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\all.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "102025"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:46:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8="}]}, {"Route": "css/all.min.ywkkukpd6z.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\all.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "102025"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:46:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ywkkukpd6z"}, {"Name": "label", "Value": "css/all.min.css"}, {"Name": "integrity", "Value": "sha256-HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8="}]}, {"Route": "css/bootstrap.min.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12783"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 09:04:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM="}]}, {"Route": "css/bootstrap.min.u5b947y67y.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12783"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 09:04:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u5b947y67y"}, {"Name": "label", "Value": "css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM="}]}, {"Route": "css/custom.atdhu7b3gl.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7051"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 15:25:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "atdhu7b3gl"}, {"Name": "label", "Value": "css/custom.css"}, {"Name": "integrity", "Value": "sha256-0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY="}]}, {"Route": "css/custom.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7051"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 15:25:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY="}]}, {"Route": "css/modal.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\modal.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11422"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 09:12:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M="}]}, {"Route": "css/modal.qedwpqxz3l.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\modal.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11422"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 09:12:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qedwpqxz3l"}, {"Name": "label", "Value": "css/modal.css"}, {"Name": "integrity", "Value": "sha256-4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M="}]}, {"Route": "css/playback.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\playback.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "983"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 08:21:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0="}]}, {"Route": "css/playback.flizyfk2xy.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\playback.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "983"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 08:21:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "flizyfk2xy"}, {"Name": "label", "Value": "css/playback.css"}, {"Name": "integrity", "Value": "sha256-4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0="}]}, {"Route": "css/site.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "559"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:37:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E="}]}, {"Route": "css/site.gekhf0kgg3.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "559"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:37:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gekhf0kgg3"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E="}]}, {"Route": "css/style.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22448"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 08:12:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ="}]}, {"Route": "css/style.xubv7qtpf4.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22448"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 08:12:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xubv7qtpf4"}, {"Name": "label", "Value": "css/style.css"}, {"Name": "integrity", "Value": "sha256-yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ="}]}, {"Route": "css/trajectory.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\trajectory.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8155"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:34:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA="}]}, {"Route": "css/trajectory.etg9qljj4e.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\trajectory.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8155"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:34:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "etg9qljj4e"}, {"Name": "label", "Value": "css/trajectory.css"}, {"Name": "integrity", "Value": "sha256-YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA="}]}, {"Route": "css/trajectory-actions.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\trajectory-actions.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1070"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:27:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA="}]}, {"Route": "css/trajectory-actions.zrzw13xroc.css", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\css\\trajectory-actions.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1070"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:27:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zrzw13xroc"}, {"Name": "label", "Value": "css/trajectory-actions.css"}, {"Name": "integrity", "Value": "sha256-4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA="}]}, {"Route": "index.html", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8832"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:14:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ="}]}, {"Route": "index.vfc0agseey.html", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8832"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:14:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfc0agseey"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ="}]}, {"Route": "js/app.gtlg8ms8g6.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48746"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:07:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gtlg8ms8g6"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q="}]}, {"Route": "js/app.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48746"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:07:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q="}]}, {"Route": "js/canvas.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\canvas.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "75498"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:17:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4="}]}, {"Route": "js/canvas.uqsvneiks2.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\canvas.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "75498"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:17:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uqsvneiks2"}, {"Name": "label", "Value": "js/canvas.js"}, {"Name": "integrity", "Value": "sha256-RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4="}]}, {"Route": "js/channelManager.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\channelManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26101"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 12:03:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus="}]}, {"Route": "js/channelManager.trl6al0oog.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\channelManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26101"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 12:03:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "trl6al0oog"}, {"Name": "label", "Value": "js/channelManager.js"}, {"Name": "integrity", "Value": "sha256-uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus="}]}, {"Route": "js/modalTest.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\modalTest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3888"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 09:10:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c="}]}, {"Route": "js/modalTest.qemmm9cnpe.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\modalTest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3888"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 09:10:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qemmm9cnpe"}, {"Name": "label", "Value": "js/modalTest.js"}, {"Name": "integrity", "Value": "sha256-Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c="}]}, {"Route": "js/playbackManager.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\playbackManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15500"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 16:43:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg="}]}, {"Route": "js/playbackManager.n541x1vj06.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\playbackManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15500"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 16:43:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n541x1vj06"}, {"Name": "label", "Value": "js/playbackManager.js"}, {"Name": "integrity", "Value": "sha256-S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg="}]}, {"Route": "js/sceneManager.hmsqx3cwiz.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\sceneManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "36236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:12:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmsqx3cwiz"}, {"Name": "label", "Value": "js/sceneManager.js"}, {"Name": "integrity", "Value": "sha256-aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw="}]}, {"Route": "js/sceneManager.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\sceneManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "36236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:12:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw="}]}, {"Route": "js/site.50b1q86w11.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7623"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:51:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "50b1q86w11"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4="}]}, {"Route": "js/site.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7623"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:51:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4="}]}, {"Route": "js/trajectory.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectory.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 16:59:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c="}]}, {"Route": "js/trajectory.outbabz928.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectory.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 16:59:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "outbabz928"}, {"Name": "label", "Value": "js/trajectory.js"}, {"Name": "integrity", "Value": "sha256-4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c="}]}, {"Route": "js/trajectoryManager.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectoryManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "67293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 17:03:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w="}]}, {"Route": "js/trajectoryManager.js.32cvhp1tpd.backup", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectoryManager.js.backup", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "67680"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 05:24:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "32cvhp1tpd"}, {"Name": "label", "Value": "js/trajectoryManager.js.backup"}, {"Name": "integrity", "Value": "sha256-NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So="}]}, {"Route": "js/trajectoryManager.js.backup", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectoryManager.js.backup", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "67680"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 05:24:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So="}]}, {"Route": "js/trajectoryManager.sf1oj4kvz9.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\trajectoryManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "67293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 17:03:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sf1oj4kvz9"}, {"Name": "label", "Value": "js/trajectoryManager.js"}, {"Name": "integrity", "Value": "sha256-uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w="}]}, {"Route": "js/uiManager.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\uiManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5412"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 02:24:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA="}]}, {"Route": "js/uiManager.o8c5mi9y29.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\js\\uiManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5412"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 02:24:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o8c5mi9y29"}, {"Name": "label", "Value": "js/uiManager.js"}, {"Name": "integrity", "Value": "sha256-yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.4r87du48le.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:51:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4r87du48le"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:51:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU="}]}, {"Route": "lib/jquery/dist/jquery.min.ikicaa7rus.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5675"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:50:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ikicaa7rus"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5675"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:50:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8="}]}, {"Route": "lib/signalr/signalr.min.ai9bojo3f9.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\signalr\\signalr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48701"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:59:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ai9bojo3f9"}, {"Name": "label", "Value": "lib/signalr/signalr.min.js"}, {"Name": "integrity", "Value": "sha256-HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU="}]}, {"Route": "lib/signalr/signalr.min.js", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\lib\\signalr\\signalr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48701"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:59:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.hgzbx4oip4.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "409227"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:05:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hgzbx4oip4"}, {"Name": "label", "Value": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg"}, {"Name": "integrity", "Value": "sha256-vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "409227"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:05:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:27:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.v5rxafksj3.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:27:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "label", "Value": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:22:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.v5rxafksj3.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:22:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "label", "Value": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:27:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.v5rxafksj3.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:27:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "label", "Value": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:36:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.v5rxafksj3.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:36:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "label", "Value": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:43:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.v5rxafksj3.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:43:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "label", "Value": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:21:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.v5rxafksj3.jpg", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\uploads\\becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:21:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "label", "Value": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "webfonts/fa-brands-400.gjto57buk3.woff2", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-brands-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "108020"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:52:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gjto57buk3"}, {"Name": "label", "Value": "webfonts/fa-brands-400.woff2"}, {"Name": "integrity", "Value": "sha256-dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc="}]}, {"Route": "webfonts/fa-brands-400.woff2", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-brands-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "108020"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:52:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc="}]}, {"Route": "webfonts/fa-regular-400.woff2", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-regular-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24948"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:51:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU="}]}, {"Route": "webfonts/fa-regular-400.yrqo96mk2j.woff2", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-regular-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24948"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:51:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yrqo96mk2j"}, {"Name": "label", "Value": "webfonts/fa-regular-400.woff2"}, {"Name": "integrity", "Value": "sha256-jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU="}]}, {"Route": "webfonts/fa-solid-900.bxa1shs6v7.woff2", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-solid-900.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "150124"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:51:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bxa1shs6v7"}, {"Name": "label", "Value": "webfonts/fa-solid-900.woff2"}, {"Name": "integrity", "Value": "sha256-cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA="}]}, {"Route": "webfonts/fa-solid-900.woff2", "AssetFile": "D:\\Project\\TrajectoryAuto\\code\\TrajectoryAuto.Web\\wwwroot\\webfonts\\fa-solid-900.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "150124"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:51:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA="}]}]}