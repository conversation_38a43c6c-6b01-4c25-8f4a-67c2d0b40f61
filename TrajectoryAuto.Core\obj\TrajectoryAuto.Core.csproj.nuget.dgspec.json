{"format": 1, "restore": {"D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj": {}}, "projects": {"D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj", "projectName": "TrajectoryAuto.Core", "projectPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}