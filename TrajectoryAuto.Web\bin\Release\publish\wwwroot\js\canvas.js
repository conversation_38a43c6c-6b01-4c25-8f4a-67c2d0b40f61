// 画布管理器
class CanvasManager {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.currentTool = 'select';
        this.isDrawing = false;
        this.currentPath = [];
        this.trajectories = [];
        this.backgroundImage = null;
        
        // 形状绘制相关属性
        this.isDrawingShape = false;
        this.shapeType = null;
        this.shapeStartPoint = null;
        this.shapeCurrentPoint = null;
        this.polygonPoints = null;
        
        // 轨迹选择相关属性
        this.selectedTrajectory = null;
        
        // 临时轨迹相关属性
        this.temporaryPaths = []; // 存储临时轨迹
        this.fadeOutDuration = 3000; // 淡出持续时间（毫秒）
        
        // 防止画布被选中
        this.preventCanvasSelection();
        
        this.setupEventListeners();
        this.setupCoordinateDisplay();
        
        // 初始化轨迹管理器
        this.trajectoryManager = new TrajectoryManager(this);
        
        // 设置全局引用，确保其他代码可以访问
        window.trajectoryManager = this.trajectoryManager;
        
        // 初始化时绘制白色背景
        this.drawWhiteBackground();
        
        // 启动动画循环，用于处理临时轨迹的淡出效果
        this.startAnimationLoop();
        
        //console.log('画布管理器初始化完成');
    }

    // 设置事件监听器
    setupEventListeners() {
        // 防止默认的文本选中行为
        this.canvas.addEventListener('selectstart', (e) => e.preventDefault());
        
        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('click', (e) => this.handleClick(e));
        this.canvas.addEventListener('dblclick', (e) => {
            e.preventDefault();
            const pos = this.getMousePos(e);
            
            if (this.currentTool === 'select') {
                // 在选择模式下，检查是否双击了轨迹
                const trajectory = this.findTrajectoryAtPosition(pos);
                if (trajectory) {
                    this.selectTrajectory(trajectory);
                    this.trajectoryManager.openTrajectorySettings(trajectory);
                }
            }
        });
        
        // 触摸事件支持
        this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        
        // 自定义右键菜单
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            if (this.currentTool === 'select') {
                const pos = this.getMousePos(e);
                const trajectory = this.findTrajectoryAtPosition(pos);
                if (trajectory) {
                    this.selectTrajectory(trajectory);
                    this.trajectoryManager.openTrajectorySettings(trajectory);
                }
            }
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            //console.log('窗口大小变化，触发画布重绘');
            // 延迟重绘，确保所有元素尺寸已更新
            setTimeout(() => this.redrawCanvas(), 100);
        });
    }

    // 设置坐标显示
    setupCoordinateDisplay() {
        this.canvas.addEventListener('mousemove', (e) => {
            const pos = this.getMousePos(e);
            const x = Math.round(pos.x);
            const y = Math.round(pos.y);
            
            // 获取当前场景的坐标转换参数
            const scene = window.app?.currentScene;
            
            if (scene && (scene.originX !== undefined || scene.originY !== undefined || 
                         scene.scaleX !== undefined || scene.scaleY !== undefined)) {
                // 应用坐标转换
                const realX = ((x - (scene.originX || 0)) / (scene.scaleX || 1)).toFixed(2);
                let realY;
                
                if (scene.invertY) {
                    // Y轴反转
                    realY = ((scene.originY || 0) - y) / (scene.scaleY || 1);
                } else {
                    realY = ((y - (scene.originY || 0)) / (scene.scaleY || 1));
                }
                
                realY = realY.toFixed(2);
                
                // 显示原始坐标和转换后的实际坐标
                document.getElementById('coordinateDisplay').textContent = 
                    `画布坐标: (${x}, ${y}) | 实际坐标: (${realX}, ${realY})`;
            } else {
                // 如果没有场景或转换参数，只显示原始坐标
                document.getElementById('coordinateDisplay').textContent = `坐标: (${x}, ${y})`;
            }
        });
    }

    // 获取鼠标位置
    getMousePos(e) {
        const rect = this.canvas.getBoundingClientRect();
        // 计算画布的缩放比例
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        // 直接获取画布上的原始像素坐标
        // 不再进行额外的缩放调整，保持与绘制时使用相同的坐标系统
        return {
            x: (e.clientX - rect.left) * scaleX,
            y: (e.clientY - rect.top) * scaleY
        };
    }

    // 获取触摸位置
    getTouchPos(e) {
        const rect = this.canvas.getBoundingClientRect();
        // 计算画布的缩放比例
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        // 直接获取画布上的原始像素坐标
        // 不再进行额外的缩放调整，保持与绘制时使用相同的坐标系统
        return {
            x: (e.touches[0].clientX - rect.left) * scaleX,
            y: (e.touches[0].clientY - rect.top) * scaleY
        };
    }
    
    // 防止画布被选中
    preventCanvasSelection() {
        // 通过JavaScript防止选中
        this.canvas.onselectstart = () => false;
        this.canvas.onmousedown = (e) => {
            // 防止默认的选中行为
            e.preventDefault();
            return false;
        };
        
        // 防止触摸高亮
        this.canvas.style.webkitTapHighlightColor = 'rgba(0,0,0,0)';
        this.canvas.style.webkitUserSelect = 'none';
        this.canvas.style.userSelect = 'none';
        this.canvas.setAttribute('unselectable', 'on');
    }
    
    // 绘制白色背景
    drawWhiteBackground() {
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    // 鼠标按下事件
    handleMouseDown(e) {
        e.preventDefault(); // 防止默认选中行为
        const pos = this.getMousePos(e);
        
        if (this.currentTool === 'select') {
            // 在选择模式下，检查是否点击了轨迹
            const trajectory = this.findTrajectoryAtPosition(pos);
            if (trajectory) {
                this.selectTrajectory(trajectory);
            } else {
                this.deselectTrajectory();
            }
        } else {
            this.startDrawing(pos);
        }
    }

    // 鼠标移动事件
    handleMouseMove(e) {
        e.preventDefault(); // 防止默认选中行为
        const pos = this.getMousePos(e);
        this.continueDrawing(pos);
    }

    // 鼠标释放事件
    async handleMouseUp(e) {
        e.preventDefault(); // 防止默认选中行为
        const pos = this.getMousePos(e);
        await this.endDrawing(pos);
    }

    // 点击事件
    handleClick(e) {
        e.preventDefault(); // 防止默认选中行为
        const pos = this.getMousePos(e);
        this.handleToolClick(pos);
    }

    // 触摸开始事件
    handleTouchStart(e) {
        e.preventDefault(); // 防止默认选中行为
        e.stopPropagation(); // 防止事件冒泡
        const pos = this.getTouchPos(e);
        this.startDrawing(pos);
    }

    // 触摸移动事件
    handleTouchMove(e) {
        e.preventDefault(); // 防止默认选中行为
        e.stopPropagation(); // 防止事件冒泡
        const pos = this.getTouchPos(e);
        this.continueDrawing(pos);
    }

    // 触摸结束事件
    async handleTouchEnd(e) {
        e.preventDefault(); // 防止默认选中行为
        e.stopPropagation(); // 防止事件冒泡
        // 使用最后一个触摸点作为结束点
        if (this.currentPath.length > 0) {
            const lastPoint = this.currentPath[this.currentPath.length - 1];
            await this.endDrawing(lastPoint);
        }
    }

    // 开始绘制
    startDrawing(pos) {
        if (this.currentTool === 'freedraw' || this.currentTool === 'realtime') {
            this.isDrawing = true;
            this.currentPath = [{ x: pos.x, y: pos.y, timestamp: 0 }];
            this.startTime = Date.now();
        }
    }

    // 继续绘制
    continueDrawing(pos) {
        if (!this.isDrawing) return;

        if (this.currentTool === 'freedraw' || this.currentTool === 'realtime') {
            const timestamp = (Date.now() - this.startTime) / 1000;
            this.currentPath.push({ x: pos.x, y: pos.y, timestamp });
            
            // 实时绘制路径
            this.drawCurrentPath();
            
            // 如果是实时模式，立即发送坐标数据
            if (this.currentTool === 'realtime' && window.app.currentChannel && window.app.currentScene) {
                this.sendRealTimeCoordinate(pos);
                
                // 对于实时模式，我们不保存轨迹，而是添加到临时轨迹数组中
                // 每次移动都创建一个新的临时路径段
                if (this.currentPath.length >= 2) {
                    const lastTwoPoints = this.currentPath.slice(-2);
                    this.addTemporaryPathSegment(lastTwoPoints);
                }
            }
        }
    }

    // 结束绘制
    async endDrawing(pos) {
        if (!this.isDrawing) return;
        
        this.isDrawing = false;
        
        // 只有在非实时模式下才保存轨迹
        if (this.currentPath.length > 1 && this.currentTool !== 'realtime') {
            await this.saveCurrentTrajectory();
        }
        
        this.currentPath = [];
    }
    
    // 发送实时坐标数据
    sendRealTimeCoordinate(pos) {
        // 确保当前通道和场景存在
        const currentChannel = window.app.currentChannel;
        const currentScene = window.app.currentScene;
        
        if (!currentChannel || !currentScene) {
            console.warn('无法发送实时坐标：当前通道或场景未设置');
            return;
        }
        
        // 获取通道地址、IP和端口
        const channelAddress = currentChannel.address || '';
        const ipAddress = currentChannel.ipAddress || '127.0.0.1';
        const port = currentChannel.port || 8080;
        const channelNumber = currentChannel.channelNumber || 1;
        
        // 获取X和Y坐标
        const x = pos.x;
        const y = pos.y;
        
        // 计算Z坐标（如果启用了Z轴）
        let z = 0;
        if (currentChannel.useZAxis) {
            const zMin = currentChannel.zAxisMin || 0;
            const zMax = currentChannel.zAxisMax || 1;
            
            // 使用正弦函数计算Z值，在zMin和zMax之间振荡
            const time = Date.now() / 1000; // 当前时间（秒）
            const frequency = 0.5; // 振荡频率（Hz）
            const amplitude = (zMax - zMin) / 2;
            const offset = zMin + amplitude;
            
            // 正弦波振荡公式：z = offset + amplitude * sin(2π * frequency * time)
            z = offset + amplitude * Math.sin(2 * Math.PI * frequency * time);
            
            // 确保Z值在范围内
            z = Math.max(zMin, Math.min(zMax, z));
        }
        
        // 格式化坐标数据字符串（用于日志显示）
        const coordinateDataString = `${channelAddress}${x.toFixed(2)},${y.toFixed(2)},${z.toFixed(2)}`;
        
        // 创建坐标数据对象（用于SignalR发送）
        const coordinateData = {
            channelNumber: channelNumber,
            x: x,
            y: y,
            z: z, // 添加Z坐标
            channelAddress: channelAddress, // 添加通道地址
            timestamp: new Date()
        };
        
        //console.log(`发送实时坐标数据: ${coordinateDataString} 到 ${ipAddress}:${port}`);
        
        // 使用SignalR发送坐标数据
        if (window.app.connection && window.app.connection.state === signalR.HubConnectionState.Connected) {
            // 调用SignalR Hub方法发送坐标数据
            window.app.connection.invoke('SendRealTimeCoordinate', ipAddress, port, coordinateData)
                .then(() => {
                    // 发送成功
                    //console.log('通过SignalR发送坐标数据成功');
                })
                .catch(err => {
                    console.error('通过SignalR发送坐标数据失败:', err);
                    // 如果SignalR发送失败，尝试通过API发送
                    this.sendCoordinateViaApi(ipAddress, port, coordinateDataString);
                });
        } else {
            console.warn('SignalR连接未建立或未就绪，尝试通过API发送');
            // 尝试通过API发送数据
            this.sendCoordinateViaApi(ipAddress, port, coordinateDataString);
        }
    }
    
    // 通过API发送坐标数据（备用方法）
    async sendCoordinateViaApi(ip, port, data) {
        try {
            const response = await fetch('/api/udp/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ip: ip,
                    port: port,
                    data: data
                })
            });
            
            if (!response.ok) {
                throw new Error(`发送失败: ${response.status}`);
            }
            
            const result = await response.json();
            //console.log('通过API发送UDP数据成功:', result);
        } catch (error) {
            console.error('通过API发送UDP数据失败:', error);
        }
    }

    // 处理工具点击
    handleToolClick(pos) {
        switch (this.currentTool) {
            case 'circle':
                this.startCircleDrawing(pos);
                break;
            case 'rectangle':
                this.startRectangleDrawing(pos);
                break;
            case 'polygon':
                this.handlePolygonClick(pos);
                break;
        }
    }

    // 开始圆形绘制
    startCircleDrawing(center) {
        this.isDrawingShape = true;
        this.shapeType = 'circle';
        this.shapeStartPoint = center;
        this.shapeCurrentPoint = center;
        
        // 添加鼠标移动和松开事件
        document.addEventListener('mousemove', this.circleMouseMoveHandler = (e) => {
            if (this.isDrawingShape && this.shapeType === 'circle') {
                const pos = this.getMousePos(e);
                this.shapeCurrentPoint = pos;
                this.drawCurrentCircle();
            }
        });
        
        document.addEventListener('mouseup', this.circleMouseUpHandler = async (e) => {
            if (this.isDrawingShape && this.shapeType === 'circle') {
                const pos = this.getMousePos(e);
                this.shapeCurrentPoint = pos;
                await this.finishCircleDrawing();
                
                // 移除事件监听器
                document.removeEventListener('mousemove', this.circleMouseMoveHandler);
                document.removeEventListener('mouseup', this.circleMouseUpHandler);
            }
        }, { once: true });
        
        // 绘制初始圆形
        this.drawCurrentCircle();
    }
    
    // 绘制当前圆形
    drawCurrentCircle() {
        // 重绘画布，但不清除已保存的轨迹
        this.redrawCanvas();
        
        // 注意：redrawCanvas已经包含了绘制背景和已保存轨迹的逻辑
        
        // 计算半径
        const dx = this.shapeCurrentPoint.x - this.shapeStartPoint.x;
        const dy = this.shapeCurrentPoint.y - this.shapeStartPoint.y;
        const radius = Math.sqrt(dx * dx + dy * dy);
        
        // 绘制圆形
        this.ctx.strokeStyle = window.app.currentChannel?.color || '#ff0000';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.arc(this.shapeStartPoint.x, this.shapeStartPoint.y, radius, 0, 2 * Math.PI);
        this.ctx.stroke();
        
        // 绘制中心点
        this.ctx.fillStyle = '#0000ff';
        this.ctx.beginPath();
        this.ctx.arc(this.shapeStartPoint.x, this.shapeStartPoint.y, 3, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 绘制半径线
        this.ctx.strokeStyle = '#0000ff';
        this.ctx.beginPath();
        this.ctx.moveTo(this.shapeStartPoint.x, this.shapeStartPoint.y);
        this.ctx.lineTo(this.shapeCurrentPoint.x, this.shapeCurrentPoint.y);
        this.ctx.stroke();
    }
    
    // 完成圆形绘制
    async finishCircleDrawing() {
        //console.log('开始完成圆形绘制');
        // 计算半径
        const dx = this.shapeCurrentPoint.x - this.shapeStartPoint.x;
        const dy = this.shapeCurrentPoint.y - this.shapeStartPoint.y;
        const radius = Math.sqrt(dx * dx + dy * dy);
        //console.log(`圆形半径: ${radius}, 中心点: (${this.shapeStartPoint.x}, ${this.shapeStartPoint.y})`);
        
        if (radius < 5) {
            // 半径太小，取消绘制
            //console.log('半径太小，取消圆形绘制');
            this.isDrawingShape = false;
            this.redrawCanvas();
            return;
        }
        
        const center = this.shapeStartPoint;
        const points = [];
        // 使用默认值，因为trajectoryDuration已被移除
        const duration = 60;
        const pointCount = 300; // 增加到300个点形成圆形，确保轨迹平滑
        
        //console.log(`开始生成圆形点，点数: ${pointCount}, 时长: ${duration}`);
        
        try {
            // 生成圆形点
            for (let i = 0; i < pointCount; i++) {
                const angle = (i / pointCount) * 2 * Math.PI;
                const x = center.x + radius * Math.cos(angle);
                const y = center.y + radius * Math.sin(angle);
                const timestamp = (i / pointCount) * duration;
                
                points.push({ x, y, timestamp, order: i });
            }
            
            // 闭合圆形
            points.push({
                x: points[0].x,
                y: points[0].y,
                timestamp: duration,
                order: pointCount
            });
            
            //console.log(`圆形点生成完成，实际点数: ${points.length}`);
            //console.log('第一个点:', points[0]);
            //console.log('最后一个点:', points[points.length-1]);
            
            // 检查点数组是否有效
            if (!points || points.length < 2) {
                console.error('生成的圆形点数不足');
                alert('生成的圆形点数不足，请重新绘制');
                this.isDrawingShape = false;
                this.redrawCanvas();
                return;
            }
            
            //console.log('开始创建圆形轨迹...');
            // 传递点数组的副本而不是引用
            const savedTrajectory = await this.createTrajectory('circle', [...points], duration);
            //console.log('圆形轨迹创建结果:', savedTrajectory);
        } catch (error) {
            console.error('创建圆形轨迹时出错:', error);
            alert('创建圆形轨迹失败: ' + error.message);
        } finally {
            this.isDrawingShape = false;
            this.redrawCanvas();
            //console.log('圆形绘制完成');
        }
    }

    // 开始矩形绘制
    startRectangleDrawing(startPoint) {
        this.isDrawingShape = true;
        this.shapeType = 'rectangle';
        this.shapeStartPoint = startPoint;
        this.shapeCurrentPoint = startPoint;
        
        // 添加鼠标移动和松开事件
        document.addEventListener('mousemove', this.rectMouseMoveHandler = (e) => {
            if (this.isDrawingShape && this.shapeType === 'rectangle') {
                const pos = this.getMousePos(e);
                this.shapeCurrentPoint = pos;
                this.drawCurrentRectangle();
            }
        });
        
        document.addEventListener('mouseup', this.rectMouseUpHandler = async (e) => {
            if (this.isDrawingShape && this.shapeType === 'rectangle') {
                const pos = this.getMousePos(e);
                this.shapeCurrentPoint = pos;
                await this.finishRectangleDrawing();
                
                // 移除事件监听器
                document.removeEventListener('mousemove', this.rectMouseMoveHandler);
                document.removeEventListener('mouseup', this.rectMouseUpHandler);
            }
        }, { once: true });
        
        // 绘制初始矩形
        this.drawCurrentRectangle();
    }
    
    // 绘制当前矩形
    drawCurrentRectangle() {
        // 重绘画布，但不清除已保存的轨迹
        this.redrawCanvas();
        
        // 注意：redrawCanvas已经包含了绘制背景和已保存轨迹的逻辑
        
        // 计算矩形尺寸
        const width = Math.abs(this.shapeCurrentPoint.x - this.shapeStartPoint.x);
        const height = Math.abs(this.shapeCurrentPoint.y - this.shapeStartPoint.y);
        
        // 计算矩形左上角坐标
        const x = Math.min(this.shapeStartPoint.x, this.shapeCurrentPoint.x);
        const y = Math.min(this.shapeStartPoint.y, this.shapeCurrentPoint.y);
        
        // 绘制矩形
        this.ctx.strokeStyle = window.app.currentChannel?.color || '#ff0000';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(x, y, width, height);
        
        // 绘制角点
        this.ctx.fillStyle = '#0000ff';
        
        // 左上角
        this.ctx.beginPath();
        this.ctx.arc(x, y, 3, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 右上角
        this.ctx.beginPath();
        this.ctx.arc(x + width, y, 3, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 右下角
        this.ctx.beginPath();
        this.ctx.arc(x + width, y + height, 3, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 左下角
        this.ctx.beginPath();
        this.ctx.arc(x, y + height, 3, 0, 2 * Math.PI);
        this.ctx.fill();
    }
    
    // 完成矩形绘制
    async finishRectangleDrawing() {
        //console.log('开始完成矩形绘制');
        // 计算矩形尺寸
        const width = Math.abs(this.shapeCurrentPoint.x - this.shapeStartPoint.x);
        const height = Math.abs(this.shapeCurrentPoint.y - this.shapeStartPoint.y);
        
        if (width < 10 || height < 10) {
            // 矩形太小，取消绘制
            //console.log('矩形太小，取消绘制');
            this.isDrawingShape = false;
            this.redrawCanvas();
            return;
        }
        
        // 计算矩形四个角的坐标
        const x1 = Math.min(this.shapeStartPoint.x, this.shapeCurrentPoint.x);
        const y1 = Math.min(this.shapeStartPoint.y, this.shapeCurrentPoint.y);
        const x2 = Math.max(this.shapeStartPoint.x, this.shapeCurrentPoint.x);
        const y2 = Math.max(this.shapeStartPoint.y, this.shapeCurrentPoint.y);
        
        //console.log(`矩形坐标: (${x1},${y1}) - (${x2},${y2}), 宽度: ${width}, 高度: ${height}`);
        
        try {
            const points = [];
            // 使用默认值，因为trajectoryDuration已被移除
            const duration = 60;
            const totalPoints = 300; // 总共生成300个点
            
            //console.log(`开始生成矩形轨迹点，目标点数: ${totalPoints}`);
            
            // 每条边分配的点数（总共四条边）
            const pointsPerSide = Math.floor(totalPoints / 4);
            let currentOrder = 0;
            
            // 生成左上到右上的点
            for (let i = 0; i <= pointsPerSide; i++) {
                const ratio = i / pointsPerSide;
                const x = x1 + ratio * (x2 - x1);
                const y = y1;
                const timestamp = (currentOrder / totalPoints) * duration;
                points.push({ x, y, timestamp, order: currentOrder });
                currentOrder++;
            }
            
            // 生成右上到右下的点
            for (let i = 0; i <= pointsPerSide; i++) {
                const ratio = i / pointsPerSide;
                const x = x2;
                const y = y1 + ratio * (y2 - y1);
                const timestamp = (currentOrder / totalPoints) * duration;
                points.push({ x, y, timestamp, order: currentOrder });
                currentOrder++;
            }
            
            // 生成右下到左下的点
            for (let i = 0; i <= pointsPerSide; i++) {
                const ratio = i / pointsPerSide;
                const x = x2 - ratio * (x2 - x1);
                const y = y2;
                const timestamp = (currentOrder / totalPoints) * duration;
                points.push({ x, y, timestamp, order: currentOrder });
                currentOrder++;
            }
            
            // 生成左下到左上的点
            for (let i = 0; i < pointsPerSide; i++) { // 这里用<而不是<=来避免重复第一个点
                const ratio = i / pointsPerSide;
                const x = x1;
                const y = y2 - ratio * (y2 - y1);
                const timestamp = (currentOrder / totalPoints) * duration;
                points.push({ x, y, timestamp, order: currentOrder });
                currentOrder++;
            }
            
            //console.log(`矩形轨迹点生成完成，实际点数: ${points.length}`);
            //console.log('第一个点:', points[0]);
            //console.log('最后一个点:', points[points.length-1]);
            
            // 检查点数组是否有效
            if (!points || points.length < 2) {
                console.error('生成的矩形点数不足');
                alert('生成的矩形点数不足，请重新绘制');
                this.isDrawingShape = false;
                this.redrawCanvas();
                return;
            }
            
            //console.log('开始创建矩形轨迹...');
            // 传递点数组的副本而不是引用
            const savedTrajectory = await this.createTrajectory('rectangle', [...points], duration);
            //console.log('矩形轨迹创建结果:', savedTrajectory);
        } catch (error) {
            console.error('创建矩形轨迹时出错:', error);
            alert('创建矩形轨迹失败: ' + error.message);
        } finally {
            this.isDrawingShape = false;
            this.redrawCanvas();
            //console.log('矩形绘制完成');
        }
    }

    // 处理多边形点击
    handlePolygonClick(pos) {
        if (!this.polygonPoints) {
            this.polygonPoints = [];
        }
        
        this.polygonPoints.push(pos);
        
        // 重绘画布以显示当前多边形
        this.drawCurrentPolygon();
        
        // 双击完成多边形
        if (this.polygonPoints.length >= 3) {
            // 这里可以添加双击检测逻辑
            // 暂时用右键点击完成多边形
            this.canvas.addEventListener('contextmenu', async (e) => {
                e.preventDefault();
                await this.finishPolygon();
            }, { once: true });
        }
    }

    // 绘制当前多边形
    drawCurrentPolygon() {
        if (!this.polygonPoints || this.polygonPoints.length < 1) return;
        
        // 重绘画布，但不清除已保存的轨迹
        this.redrawCanvas();
        
        // 注意：redrawCanvas已经包含了绘制背景和已保存轨迹的逻辑
        
        // 绘制当前多边形的点
        this.ctx.fillStyle = '#ff0000';
        this.polygonPoints.forEach(point => {
            this.ctx.beginPath();
            this.ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI);
            this.ctx.fill();
        });
        
        // 绘制多边形线段
        if (this.polygonPoints.length > 1) {
            this.ctx.strokeStyle = window.app.currentChannel?.color || '#ff0000';
            this.ctx.lineWidth = 2;
            this.ctx.beginPath();
            
            this.polygonPoints.forEach((point, index) => {
                if (index === 0) {
                    this.ctx.moveTo(point.x, point.y);
                } else {
                    this.ctx.lineTo(point.x, point.y);
                }
            });
            
            this.ctx.stroke();
        }
    }
    
    // 完成多边形绘制
    async finishPolygon() {
        //console.log('开始完成多边形绘制');
        
        if (!this.polygonPoints || this.polygonPoints.length < 3) {
            console.warn('多边形点数不足，无法完成绘制，点数:', this.polygonPoints?.length || 0);
            return;
        }
        
        try {
            // 使用默认值，因为trajectoryDuration已被移除
            const duration = 60;
            // 创建原始点的副本，避免引用问题
            const originalPoints = [...this.polygonPoints];
            const points = [];
            const totalPoints = 300; // 总共生成300个点
            
            //console.log(`开始生成多边形轨迹点，原始点数: ${originalPoints.length}, 目标点数: ${totalPoints}`);
            
            // 每条边分配的点数
            const sides = originalPoints.length;
            const pointsPerSide = Math.floor(totalPoints / sides);
            let currentOrder = 0;
            
            // 生成每条边上的点
            for (let side = 0; side < sides; side++) {
                const startPoint = originalPoints[side];
                const endPoint = originalPoints[(side + 1) % sides]; // 使用模运算确保最后一条边连接到第一个点
                
                //console.log(`处理第${side+1}条边，从(${startPoint.x},${startPoint.y})到(${endPoint.x},${endPoint.y})`);
                
                // 如果是最后一条边，使用剩余的点数
                const currentSidePoints = (side === sides - 1) ? 
                    (totalPoints - currentOrder) : pointsPerSide;
                
                for (let i = 0; i < currentSidePoints; i++) {
                    const ratio = i / currentSidePoints;
                    const x = startPoint.x + ratio * (endPoint.x - startPoint.x);
                    const y = startPoint.y + ratio * (endPoint.y - startPoint.y);
                    const timestamp = (currentOrder / totalPoints) * duration;
                    
                    points.push({ x, y, timestamp, order: currentOrder });
                    currentOrder++;
                }
            }
            
            // 闭合多边形，确保最后一个点与第一个点相同
            points.push({
                x: points[0].x,
                y: points[0].y,
                timestamp: duration,
                order: currentOrder
            });
            
            //console.log(`多边形轨迹点生成完成，实际点数: ${points.length}`);
            //console.log('第一个点:', points[0]);
            //console.log('最后一个点:', points[points.length-1]);
            
            // 检查点数组是否有效
            if (!points || points.length < 2) {
                console.error('生成的多边形点数不足');
                alert('生成的多边形点数不足，请重新绘制');
                return;
            }
            
            //console.log('开始创建多边形轨迹...');
            // 传递点数组的副本而不是引用
            const savedTrajectory = await this.createTrajectory('polygon', [...points], duration);
            //console.log('多边形轨迹创建结果:', savedTrajectory);
        } catch (error) {
            console.error('创建多边形轨迹时出错:', error);
            alert('创建多边形轨迹失败: ' + error.message);
        } finally {
            this.polygonPoints = null;
            this.redrawCanvas();
            //console.log('多边形绘制完成');
        }
    }

    // 创建轨迹
    async createTrajectory(type, points, duration) {
        //console.log('创建轨迹开始，类型:', type);
        //console.log('点数组是否存在:', points ? '是' : '否');
        //console.log('点数组长度:', points?.length || 0);
        //console.log('点数组内容示例:', points && points.length > 0 ? JSON.stringify(points[0]) : '无点');
        //console.log('检查通道:', window.app?.currentChannel);
        
        // 检查点数组是否有效
        if (!points) {
            console.error('点数组为空，无法创建轨迹');
            alert('轨迹点数组为空，请重新绘制');
            return null;
        }
        
        // 检查通道是否存在
        if (!window.app || !window.app.currentChannel) {
            console.error('通道未选择，window.app.currentChannel:', window.app?.currentChannel);
            alert('请先选择一个通道');
            return null;
        }
        
        // 检查通道ID是否存在
        if (!window.app.currentChannel.id) {
            console.error('通道ID不存在:', window.app.currentChannel);
            alert('通道信息不完整，请重新选择通道');
            return null;
        }
        
        // 确保点数组有足够的点
        if (points.length < 2) {
            console.error('轨迹点数不足，无法创建轨迹，点数:', points.length);
            alert('轨迹点数不足，请重新绘制');
            return null;
        }
        
        // 创建点数组的深拷贝，确保数据不会丢失
        const pointsCopy = points.map((point, index) => ({
            x: point.x,
            y: point.y,
            timestamp: point.timestamp || (index / points.length * duration),
            order: point.order !== undefined ? point.order : index
        }));
        
        //console.log('点数组深拷贝后长度:', pointsCopy.length);
        //console.log('深拷贝后第一个点:', JSON.stringify(pointsCopy[0]));

        const trajectory = {
            name: `${type}_${Date.now()}`,
            type: this.getTrajectoryTypeEnum(type),
            duration: duration,
            isLoop: false, // 默认不循环，因为trajectoryLoop已被移除
            channelId: window.app.currentChannel.id,
            points: pointsCopy
        };
        
        //console.log('创建轨迹对象后检查点数组:', trajectory.points.length);
        //console.log('轨迹对象中的第一个点:', JSON.stringify(trajectory.points[0]));

        try {
            //console.log('发送轨迹数据到服务器:', trajectory);
            const response = await fetch('/api/trajectories', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trajectory)
            });

            if (response.ok) {
                const savedTrajectory = await response.json();
                //console.log('轨迹保存成功:', savedTrajectory);
                
                // 重要修复：确保返回的轨迹对象包含点数组
                // 如果服务器返回的轨迹对象没有点数组，使用原始点数组
                if (!savedTrajectory.points || savedTrajectory.points.length === 0) {
                    //console.log('服务器返回的轨迹没有点数组，使用原始点数组');
                    savedTrajectory.points = trajectory.points;
                    //console.log('添加点数组后的轨迹:', savedTrajectory.points.length);
                }
                
                // 同步更新本地和app的轨迹数组
                this.trajectories.unshift(savedTrajectory);
                
                // 确保window.app.trajectories存在并更新
                if (!window.app.trajectories) {
                    window.app.trajectories = [];
                }
                window.app.trajectories.unshift(savedTrajectory);
                
                //console.log('更新后的轨迹数组:', this.trajectories.length, window.app.trajectories.length);
                //console.log('第一个轨迹的点数:', this.trajectories[0].points?.length || 0);
                return savedTrajectory;
            } else {
                const errorText = await response.text();
                console.error('保存轨迹失败:', response.status, errorText);
                alert(`保存轨迹失败: ${response.status}`);
                return null;
            }
        } catch (error) {
            console.error('保存轨迹失败:', error);
            alert('保存轨迹失败: ' + error.message);
            return null;
        }
    }

    // 保存当前轨迹（自由绘制）
    async saveCurrentTrajectory() {
        // 使用默认值，因为trajectoryDuration已被移除
        const duration = 60;
        
        // 检查是否有足够的点
        if (this.currentPath.length === 0) {
            console.warn('没有轨迹点，无法保存');
            return;
        }
        
        // 如果只有一个点，复制该点以确保至少有两个点
        if (this.currentPath.length === 1) {
            //console.log('只有一个点，复制该点以创建最小轨迹');
            const point = this.currentPath[0];
            this.currentPath.push({
                x: point.x + 1,  // 稍微偏移一个像素
                y: point.y + 1,
                timestamp: point.timestamp + 0.1
            });
        }
        
        //console.log('原始轨迹点数:', this.currentPath.length);
        
        // 如果原始点数少于300个，生成更多的插值点
        let processedPoints = this.currentPath;
        const targetPointCount = 300;
        
        if (this.currentPath.length < targetPointCount) {
            //console.log(`原始点数少于${targetPointCount}，生成插值点`);
            processedPoints = this.generateInterpolatedPoints(this.currentPath, targetPointCount);
            //console.log('插值后的点数:', processedPoints.length);
        }
        
        // 重新计算时间戳，使总时长符合设定
        const maxTimestamp = Math.max(...processedPoints.map(p => p.timestamp || 0));
        
        // 规范化时间戳并确保每个点都有order属性
        const normalizedPoints = processedPoints.map((point, index) => {
            if (maxTimestamp <= 0) {
                return {
                    ...point,
                    timestamp: (index / (processedPoints.length - 1)) * duration,
                    order: index
                };
            } else {
                return {
                    ...point,
                    timestamp: (point.timestamp / maxTimestamp) * duration,
                    order: index
                };
            }
        });
        
        //console.log('规范化后的点:', normalizedPoints.length);
        //console.log('第一个点:', normalizedPoints[0]);
        //console.log('最后一个点:', normalizedPoints[normalizedPoints.length - 1]);
        
        const savedTrajectory = await this.createTrajectory('freedraw', normalizedPoints, duration);
        
        if (savedTrajectory) {
            //console.log('轨迹保存成功，重绘画布');
            // 确保重绘画布以显示新添加的轨迹
            this.redrawCanvas();
        } else {
            console.error('轨迹保存失败');
        }
    }

    // 获取轨迹类型枚举值
    getTrajectoryTypeEnum(type) {
        const typeMap = {
            'circle': 1,
            'rectangle': 2,
            'polygon': 3,
            'freedraw': 4,
            'realtime': 5
        };
        return typeMap[type] || 4;
    }
    
    // 生成插值点
    generateInterpolatedPoints(originalPoints, targetCount) {
        if (!originalPoints || originalPoints.length < 2) {
            return originalPoints;
        }
        
        //console.log(`开始生成插值点，原始点数: ${originalPoints.length}, 目标点数: ${targetCount}`);
        
        // 如果原始点数已经足够，直接返回
        if (originalPoints.length >= targetCount) {
            return originalPoints;
        }
        
        const result = [];
        
        // 计算需要插入的点数
        const segments = originalPoints.length - 1;
        const pointsPerSegment = Math.ceil((targetCount - originalPoints.length) / segments);
        
        //console.log(`总段数: ${segments}, 每段插入点数: ${pointsPerSegment}`);
        
        // 对每个线段进行插值
        for (let i = 0; i < originalPoints.length - 1; i++) {
            const startPoint = originalPoints[i];
            const endPoint = originalPoints[i + 1];
            
            // 添加起始点
            result.push(startPoint);
            
            // 添加插值点
            for (let j = 1; j <= pointsPerSegment; j++) {
                const ratio = j / (pointsPerSegment + 1);
                const x = startPoint.x + ratio * (endPoint.x - startPoint.x);
                const y = startPoint.y + ratio * (endPoint.y - startPoint.y);
                const timestamp = startPoint.timestamp + ratio * (endPoint.timestamp - startPoint.timestamp);
                
                result.push({
                    x: x,
                    y: y,
                    timestamp: timestamp
                    // 不设置order，留给调用者设置
                });
            }
        }
        
        // 添加最后一个点
        result.push(originalPoints[originalPoints.length - 1]);
        
        //console.log(`插值完成，生成点数: ${result.length}`);
        return result;
    }

    // 绘制当前路径
    drawCurrentPath() {
        if (this.currentPath.length < 2) return;
        
        // 重绘画布，但不清除已保存的轨迹
        this.redrawCanvas();
        
        // 绘制当前路径
        this.ctx.strokeStyle = window.app.currentChannel?.color || '#ff0000';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        
        this.currentPath.forEach((point, index) => {
            if (index === 0) {
                this.ctx.moveTo(point.x, point.y);
            } else {
                this.ctx.lineTo(point.x, point.y);
            }
        });
        
        this.ctx.stroke();
    }
    
    // 添加临时路径段
    addTemporaryPathSegment(points) {
        if (!points || points.length < 2) return;
        
        const color = window.app.currentChannel?.color || '#ff0000';
        const now = Date.now();
        
        // 创建新的临时路径段
        const tempPath = {
            points: [...points], // 复制点数组
            color: color,
            createdAt: now,
            expiresAt: now + this.fadeOutDuration,
            opacity: 1.0 // 初始不透明度
        };
        
        // 添加到临时路径数组
        this.temporaryPaths.push(tempPath);
        
        //console.log(`添加临时路径段，点数: ${points.length}，将在 ${this.fadeOutDuration/1000} 秒后淡出`);
    }
    
    // 启动动画循环
    startAnimationLoop() {
        // 使用requestAnimationFrame创建动画循环
        const animate = () => {
            // 更新和绘制临时路径
            this.updateTemporaryPaths();
            
            // 继续动画循环
            requestAnimationFrame(animate);
        };
        
        // 开始动画循环
        animate();
    }
    
    // 更新临时路径
    updateTemporaryPaths() {
        const now = Date.now();
        let needsRedraw = false;
        
        // 更新每个临时路径的不透明度
        this.temporaryPaths = this.temporaryPaths.filter(path => {
            // 如果路径已过期，移除它
            if (now >= path.expiresAt) {
                needsRedraw = true;
                return false;
            }
            
            // 计算剩余时间比例
            const remainingTime = path.expiresAt - now;
            const timeRatio = remainingTime / this.fadeOutDuration;
            
            // 更新不透明度
            path.opacity = timeRatio;
            needsRedraw = true;
            
            return true;
        });
        
        // 如果有路径需要更新，重绘画布
        if (needsRedraw) {
            this.redrawCanvas();
        }
    }

    // 重新绘制画布
    redrawCanvas() {
        const timestamp = new Date().getTime();
        //console.log(`重绘画布调用 [${timestamp}]`);
        
        // 检查画布是否存在并已初始化
        if (!this.canvas || !this.ctx) {
            console.error(`[${timestamp}] 画布或上下文不存在，无法重绘`);
            return;
        }
        
        // 检查画布尺寸是否有效
        if (this.canvas.width <= 0 || this.canvas.height <= 0) {
            console.error(`[${timestamp}] 画布尺寸无效，无法重绘:`, this.canvas.width, 'x', this.canvas.height);
            return;
        }
        
        //console.log(`[${timestamp}] 重绘画布开始，尺寸:`, this.canvas.width, 'x', this.canvas.height, 
                   //'可见性:', this.canvas.style.display, 
                   //'DOM位置:', this.canvas.getBoundingClientRect().toString());
        
        // 确保画布可见
        if (this.canvas.style.display === 'none') {
            console.warn(`[${timestamp}] 画布当前不可见，但仍将继续重绘`);
        }
        
        // 强制重绘前先确保画布已挂载到DOM
        if (!this.canvas.parentElement) {
            console.error(`[${timestamp}] 画布未挂载到DOM，无法重绘`);
            return;
        }
        
        try {
            const timestamp = new Date().getTime();
            
            // 强制清除整个画布
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            
            // 设置默认背景为白色 - 始终绘制白色背景
            this.ctx.fillStyle = '#ffffff';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            
            // 重新应用防止选中的设置
            this.preventCanvasSelection();
            
            // 绘制背景图片（如果有）
            if (this.backgroundImage) {
                // 按原始像素显示图片，不进行缩放
                // 计算图片在画布中心的位置
                const x = (this.canvas.width - this.backgroundImage.width) / 2;
                const y = (this.canvas.height - this.backgroundImage.height) / 2;
                
                // 绘制原始大小的图片
                this.ctx.drawImage(this.backgroundImage, x, y, this.backgroundImage.width, this.backgroundImage.height);
                //console.log(`[${timestamp}] 绘制背景图片，宽度:`, this.backgroundImage.width, '高度:', this.backgroundImage.height);
            } else {
                //console.log(`[${timestamp}] 没有背景图片，保持白色背景`);
            }
            
            // 强制更新画布显示
            this.canvas.style.visibility = 'visible';
            this.canvas.style.display = 'block';
            
            // 检查当前通道
            const currentChannelId = window.app?.currentChannel?.id;
            //console.log('当前通道ID:', currentChannelId || '无');
            
            // 绘制轨迹，优先使用当前通道的轨迹
            let trajectoriesToDraw = [];
            
            // 先检查本地轨迹数组
            if (this.trajectories && this.trajectories.length > 0) {
                //console.log('使用画布管理器的本地轨迹，数量:', this.trajectories.length);
                trajectoriesToDraw = this.trajectories;
            }
            // 然后检查全局应用的轨迹数组
            else if (window.app?.trajectories && window.app.trajectories.length > 0) {
                // 如果有当前通道，过滤该通道的轨迹
                if (currentChannelId) {
                    trajectoriesToDraw = window.app.trajectories.filter(t => t.channelId === currentChannelId);
                    //console.log(`使用全局应用中当前通道 ${currentChannelId} 的轨迹，数量:`, trajectoriesToDraw.length);
                } else {
                    // 如果没有当前通道，使用所有轨迹
                    trajectoriesToDraw = window.app.trajectories;
                    //console.log('使用全局应用的所有轨迹，数量:', trajectoriesToDraw.length);
                }
            } else {
                //console.log('没有轨迹可绘制');
            }
            
            // 绘制轨迹
            if (trajectoriesToDraw.length > 0) {
                //console.log('开始绘制轨迹，数量:', trajectoriesToDraw.length);
                this.drawTrajectories(trajectoriesToDraw);
            } else {
                console.warn('没有找到要绘制的轨迹，请检查轨迹数据');
            }
            
            // 绘制临时路径（实时轨迹）
            if (this.temporaryPaths && this.temporaryPaths.length > 0) {
                this.drawTemporaryPaths();
                //console.log(`绘制临时路径，数量: ${this.temporaryPaths.length}`);
            }
            
            // 绘制场景原点标记
            this.drawOriginMarker();
        } catch (error) {
            console.error('重绘画布时发生错误:', error);
        }
    }

    // 绘制场景原点标记
    drawOriginMarker() {
        // 检查是否有当前场景
        if (!window.app?.currentScene) {
            //console.log('没有当前场景，无法绘制原点标记');
            return;
        }
        
        // 获取场景原点坐标
        const originX = window.app.currentScene.originX || 0;
        const originY = window.app.currentScene.originY || 0;
        
        //console.log(`绘制场景原点标记，坐标: (${originX}, ${originY})`);
        
        // 保存当前绘图状态
        this.ctx.save();
        
        // 设置原点标记样式
        this.ctx.strokeStyle = '#0000FF'; // 蓝色
        this.ctx.fillStyle = '#0000FF';
        this.ctx.lineWidth = 2;
        
        // 绘制十字标记
        const markerSize = 10; // 标记大小
        
        // 绘制水平线
        this.ctx.beginPath();
        this.ctx.moveTo(originX - markerSize, originY);
        this.ctx.lineTo(originX + markerSize, originY);
        this.ctx.stroke();
        
        // 绘制垂直线
        this.ctx.beginPath();
        this.ctx.moveTo(originX, originY - markerSize);
        this.ctx.lineTo(originX, originY + markerSize);
        this.ctx.stroke();
        
        // 绘制中心点
        this.ctx.beginPath();
        this.ctx.arc(originX, originY, 3, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // 绘制标签
        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = '#0000FF';
        this.ctx.textAlign = 'left';
        this.ctx.textBaseline = 'top';
        this.ctx.fillText(`原点 (${originX}, ${originY})`, originX + markerSize + 2, originY + markerSize + 2);
        
        // 恢复绘图状态
        this.ctx.restore();
    }

    // 绘制轨迹列表
    drawTrajectories(trajectories) {
        //console.log('开始绘制轨迹列表，数量:', trajectories.length);
        trajectories.forEach((trajectory, index) => {
            //console.log(`绘制轨迹 ${index+1}/${trajectories.length}, ID: ${trajectory.id}, 类型: ${trajectory.type}`);
            this.drawTrajectory(trajectory);
        });
    }
    
    // 绘制临时路径（实时轨迹）
    drawTemporaryPaths() {
        if (!this.temporaryPaths || this.temporaryPaths.length === 0) return;
        
        // 保存当前上下文状态
        this.ctx.save();
        
        // 绘制每个临时路径
        this.temporaryPaths.forEach(path => {
            if (path.points.length < 2) return;
            
            // 设置路径样式
            this.ctx.strokeStyle = path.color;
            this.ctx.lineWidth = 2;
            this.ctx.globalAlpha = path.opacity; // 应用不透明度
            
            // 绘制路径
            this.ctx.beginPath();
            this.ctx.moveTo(path.points[0].x, path.points[0].y);
            
            for (let i = 1; i < path.points.length; i++) {
                this.ctx.lineTo(path.points[i].x, path.points[i].y);
            }
            
            this.ctx.stroke();
        });
        
        // 恢复上下文状态
        this.ctx.restore();
    }

    // 绘制单个轨迹
    drawTrajectory(trajectory) {
        //console.log('尝试绘制轨迹:', trajectory.id, trajectory.type);
        
        if (!trajectory.points) {
            console.warn('轨迹没有点数组，无法绘制:', trajectory);
            return;
        }
        
        if (trajectory.points.length < 2) {
            console.warn('轨迹点数不足，无法绘制:', trajectory);
            console.warn('轨迹点详情:', JSON.stringify(trajectory.points));
            return;
        }
        
        // 检查是否是选中的轨迹
        const isSelected = this.selectedTrajectory && this.selectedTrajectory.id === trajectory.id;
        
        // 如果是选中的轨迹，使用更粗的线条和高亮颜色
        if (isSelected) {
            this.ctx.lineWidth = 4; // 加粗线条
            this.ctx.shadowBlur = 10; // 添加阴影效果
            this.ctx.shadowColor = 'rgba(0, 0, 255, 0.5)';
        } else {
            this.ctx.lineWidth = 2;
            this.ctx.shadowBlur = 0;
        }
        
        // 确保轨迹点有有效的x和y坐标
        const initialValidPoints = trajectory.points.filter(p => 
            p !== null && 
            p.x !== undefined && p.y !== undefined && 
            !isNaN(p.x) && !isNaN(p.y)
        );
        
        if (initialValidPoints.length < 2) {
            console.warn('有效轨迹点数不足，无法绘制:', initialValidPoints.length);
            return;
        }
        
        //console.log(`绘制轨迹 ID: ${trajectory.id}, 点数: ${trajectory.points.length}, 通道ID: ${trajectory.channelId}`);
        
        // 先检查window.app.channels是否存在
        try {
            // 首先检查全局通道数组
            if (!window.app?.channels || !Array.isArray(window.app.channels)) {
                console.warn('全局通道列表不存在或不是数组');
                // 使用默认颜色
                this.ctx.strokeStyle = '#ff0000';
            } else {
                // 尝试从全局通道数组中查找
                let channel = window.app.channels.find(c => c.id === trajectory.channelId);
                
                // 如果在全局通道数组中找不到，尝试从当前通道中获取
                if (!channel && window.app.currentChannel && window.app.currentChannel.id === trajectory.channelId) {
                    channel = window.app.currentChannel;
                    //console.log('使用当前通道作为轨迹的通道对象');
                    
                    // 将当前通道添加到全局通道数组中
                    window.app.channels.push(window.app.currentChannel);
                    //console.log('已将当前通道添加到全局通道数组中');
                }
                
                if (!channel) {
                    console.warn(`找不到通道对象: ${trajectory.channelId}`);
                    // 使用默认颜色
                    this.ctx.strokeStyle = '#ff0000';
                } else {
                    this.ctx.strokeStyle = channel.color || '#ff0000';
                    //console.log(`使用通道 ${channel.id} 的颜色: ${channel.color || '#ff0000'}`);
                }
            }
        } catch (error) {
            console.error('设置轨迹颜色时出错:', error);
            // 出错时使用默认颜色
            this.ctx.strokeStyle = '#ff0000';
        }
        
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        
        // 检查点是否有order属性
        const hasOrderProperty = trajectory.points.every(p => p.order !== undefined);
        //console.log(`轨迹点是否都有order属性: ${hasOrderProperty}`);
        
        if (!hasOrderProperty) {
            console.warn('部分轨迹点缺少order属性，添加默认值');
            trajectory.points.forEach((p, idx) => {
                if (p.order === undefined) p.order = idx;
            });
        }
        
        // 按顺序绘制轨迹点
        // 创建一个副本进行排序，避免修改原始数组
        const sortedPoints = [...trajectory.points].sort((a, b) => a.order - b.order);
        //console.log(`排序后的点数: ${sortedPoints.length}, 第一个点: (${sortedPoints[0].x}, ${sortedPoints[0].y})`);
        //console.log('前5个点的order值:', sortedPoints.slice(0, 5).map(p => p.order));
        
        // 只过滤无效点，不要求点必须在画布范围内
        const validPoints = sortedPoints.filter(point => {
            return point && 
                   !isNaN(point.x) && !isNaN(point.y);
            // 移除了画布范围限制
            // point.x >= 0 && point.x <= this.canvas.width &&
            // point.y >= 0 && point.y <= this.canvas.height;
        });
        
        //console.log(`过滤后的有效点数: ${validPoints.length}/${sortedPoints.length}`);
        
        if (validPoints.length < 2) {
            console.warn('有效点数不足，无法绘制轨迹');
            return;
        }
        
        // 绘制轨迹点
        validPoints.forEach((point, index) => {
            if (index === 0) {
                this.ctx.moveTo(point.x, point.y);
            } else {
                this.ctx.lineTo(point.x, point.y);
            }
        });
        
        this.ctx.stroke();
        //console.log(`轨迹 ${trajectory.id} 绘制完成`);
        
        // 绘制起点和终点标记
        if (sortedPoints.length > 0) {
            // 起点（绿色圆圈）
            this.ctx.fillStyle = '#00ff00';
            this.ctx.beginPath();
            this.ctx.arc(sortedPoints[0].x, sortedPoints[0].y, 4, 0, 2 * Math.PI);
            this.ctx.fill();
            
            // 终点（红色圆圈）
            const lastPoint = sortedPoints[sortedPoints.length - 1];
            this.ctx.fillStyle = '#ff0000';
            this.ctx.beginPath();
            this.ctx.arc(lastPoint.x, lastPoint.y, 4, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    // 发送实时坐标数据
    async sendRealTimeCoordinate(pos) {
        if (!window.app.connection || window.app.connection.state !== signalR.HubConnectionState.Connected) {
            return;
        }

        const scene = window.app.currentScene;
        const channel = window.app.currentChannel;
        
        if (!scene || !channel) return;

        // 转换为物理坐标
        const physicalX = (pos.x - scene.originX) * scene.pixelToMeterRatio;
        const physicalY = (scene.originY - pos.y) * scene.pixelToMeterRatio;

        // 计算Z坐标（如果启用了Z轴）
        let z = 0;
        if (channel.useZAxis) {
            const zMin = channel.zAxisMin || 0;
            const zMax = channel.zAxisMax || 1;
            
            // 使用正弦函数计算Z值，在zMin和zMax之间振荡
            const time = Date.now() / 1000; // 当前时间（秒）
            const frequency = 0.5; // 振荡频率（Hz）
            const amplitude = (zMax - zMin) / 2;
            const offset = zMin + amplitude;
            
            // 正弦波振荡公式：z = offset + amplitude * sin(2π * frequency * time)
            z = offset + amplitude * Math.sin(2 * Math.PI * frequency * time);
            
            // 确保Z值在范围内
            z = Math.max(zMin, Math.min(zMax, z));
        }

        // 格式化X、Y、Z坐标，保留3位小数
        const formattedX = Math.round(physicalX * 1000) / 1000;
        const formattedY = Math.round(physicalY * 1000) / 1000;
        const formattedZ = Math.round(z * 1000) / 1000;

        const coordinateData = {
            channelNumber: channel.channelNumber,
            channelAddress: channel.address, // 添加通道地址
            x: formattedX,
            y: formattedY,
            z: formattedZ,
            timestamp: new Date().toISOString()
        };

        try {
            // 使用正确的方法名 SendRealTimeCoordinate
            await window.app.connection.invoke("SendRealTimeCoordinate", 
                scene.serverIp, scene.serverPort, coordinateData, 
                channel.useZAxis, channel.zAxisMin, channel.zAxisMax);
            
            // 正确格式的坐标数据字符串：{channelAddress}{X},{Y},{Z}
            const formattedCoordinateString = `${channel.address}${formattedX},${formattedY},${formattedZ}`;
            console.log(`发送实时坐标: ${formattedCoordinateString}`);
        } catch (error) {
            console.error("发送坐标数据失败:", error);
        }
    }

    // 设置当前工具
    async setCurrentTool(tool) {
        // 检查是否有未完成的多边形绘制
        if (this.currentTool === 'polygon' && tool !== 'polygon' && this.polygonPoints && this.polygonPoints.length >= 3) {
            //console.log('检测到未完成的多边形绘制，自动完成多边形');
            await this.finishPolygon();
        }
        
        this.currentTool = tool;
        
        // 更新画布光标样式
        switch (tool) {
            case 'select':
                this.canvas.style.cursor = 'default';
                break;
            case 'freedraw':
            case 'realtime':
                this.canvas.style.cursor = 'crosshair';
                break;
            case 'circle':
            case 'rectangle':
            case 'polygon':
                this.canvas.style.cursor = 'pointer';
                break;
            default:
                this.canvas.style.cursor = 'default';
        }
    }

    // 设置背景图片
    setBackground(imageSrc) {
        const timestamp = new Date().getTime();
        //console.log(`[${timestamp}] 开始设置背景图片:`, imageSrc);
        
        const img = new Image();
        img.onload = () => {
            //console.log(`[${timestamp}] 背景图片加载成功:`, img.src, '尺寸:', img.width, 'x', img.height);
            this.backgroundImage = img;
            
            // 立即重绘画布
            this.redrawCanvas();
            
            // 延迟重绘，确保背景图片显示
            setTimeout(() => {
                //console.log(`[${timestamp}] 延迟重绘画布，确保背景图片显示`);
                this.redrawCanvas();
            }, 50);
        };
        
        img.onerror = (error) => {
            console.error(`[${timestamp}] 加载背景图片失败:`, imageSrc, error);
            
            // 尝试使用其他路径
            if (!imageSrc.startsWith('/uploads/')) {
                //console.log(`[${timestamp}] 尝试使用/uploads/路径加载图片`);
                const correctedPath = `/uploads/${imageSrc}`;
                this.setBackground(correctedPath);
            } else {
                // 清除背景图片并重绘
                console.warn(`[${timestamp}] 无法加载背景图片，清除背景`);
                this.backgroundImage = null;
                this.redrawCanvas();
            }
        };
        
        // 如果路径不包含/uploads/前缀，自动添加
        if (imageSrc && !imageSrc.startsWith('/') && !imageSrc.startsWith('http') && !imageSrc.startsWith('/uploads/')) {
            img.src = `/uploads/${imageSrc}`;
            //console.log(`[${timestamp}] 自动添加/uploads/前缀:`, img.src);
        } else {
            img.src = imageSrc;
            //console.log(`[${timestamp}] 使用原始路径:`, img.src);
        }
    }
    
    // 清除背景图片
    clearBackground() {
        this.backgroundImage = null;
        this.redrawCanvas();
        //console.log('背景图片已清除');
    }

    // 清除画布
    clearCanvas() {
        this.trajectories = [];
        this.redrawCanvas();
    }

    // 获取画布数据URL（用于导出）
    getCanvasDataURL() {
        return this.canvas.toDataURL('image/png');
    }

    // 缩放画布
    zoomCanvas(scale) {
        // 保存当前绘制内容
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        
        // 调整画布样式尺寸
        this.canvas.style.width = (this.canvas.width * scale) + 'px';
        this.canvas.style.height = (this.canvas.height * scale) + 'px';
        
        // 重绘内容
        this.redrawCanvas();
    }

    // 重置画布缩放
    resetZoom() {
        this.canvas.style.width = this.canvas.width + 'px';
        this.canvas.style.height = this.canvas.height + 'px';
    }
    
    // 查找指定位置的轨迹
    findTrajectoryAtPosition(pos) {
        if (!this.trajectories || this.trajectories.length === 0) {
            return null;
        }
        
        // 设置点击容差（像素）
        const tolerance = 10;
        
        // 遍历所有轨迹，查找最近的轨迹
        for (const trajectory of this.trajectories) {
            if (!trajectory.points || trajectory.points.length < 2) {
                continue;
            }
            
            // 检查点是否在轨迹附近
            for (let i = 0; i < trajectory.points.length - 1; i++) {
                const p1 = trajectory.points[i];
                const p2 = trajectory.points[i + 1];
                
                // 计算点到线段的距离
                const distance = this.pointToLineDistance(pos, p1, p2);
                
                if (distance <= tolerance) {
                    return trajectory;
                }
            }
        }
        
        return null;
    }
    
    // 计算点到线段的距离
    pointToLineDistance(point, lineStart, lineEnd) {
        const A = point.x - lineStart.x;
        const B = point.y - lineStart.y;
        const C = lineEnd.x - lineStart.x;
        const D = lineEnd.y - lineStart.y;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        let param = -1;
        
        if (lenSq !== 0) {
            param = dot / lenSq;
        }
        
        let xx, yy;
        
        if (param < 0) {
            xx = lineStart.x;
            yy = lineStart.y;
        } else if (param > 1) {
            xx = lineEnd.x;
            yy = lineEnd.y;
        } else {
            xx = lineStart.x + param * C;
            yy = lineStart.y + param * D;
        }
        
        const dx = point.x - xx;
        const dy = point.y - yy;
        
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    // 选择轨迹
    selectTrajectory(trajectory) {
        this.selectedTrajectory = trajectory;
        this.redrawCanvas();
        
        // 通知轨迹管理器
        if (this.trajectoryManager) {
            this.trajectoryManager.selectTrajectory(trajectory);
        }
    }
    
    // 取消选择轨迹
    deselectTrajectory() {
        this.selectedTrajectory = null;
        this.redrawCanvas();
        
        // 通知轨迹管理器
        if (this.trajectoryManager) {
            this.trajectoryManager.deselectTrajectory();
        }
    }
    
    // 更新轨迹
    updateTrajectory(trajectory) {
        // 查找并更新轨迹
        const index = this.trajectories.findIndex(t => t.id === trajectory.id);
        if (index !== -1) {
            this.trajectories[index] = trajectory;
            this.redrawCanvas();
        }
    }
    
    // 删除轨迹
    deleteTrajectory(trajectory) {
        // 从数组中移除轨迹
        this.trajectories = this.trajectories.filter(t => t.id !== trajectory.id);
        
        // 如果是当前选中的轨迹，取消选择
        if (this.selectedTrajectory && this.selectedTrajectory.id === trajectory.id) {
            this.selectedTrajectory = null;
        }
        
        // 从服务器删除轨迹
        this.deleteTrajectoryFromServer(trajectory.id);
        
        // 重绘画布
        this.redrawCanvas();
    }
    
    // 从服务器删除轨迹
    async deleteTrajectoryFromServer(trajectoryId) {
        try {
            const response = await fetch(`/api/trajectories/${trajectoryId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                //console.log(`轨迹 ${trajectoryId} 已从服务器删除`);
            } else {
                console.error(`删除轨迹 ${trajectoryId} 失败:`, response.status);
            }
        } catch (error) {
            console.error(`删除轨迹 ${trajectoryId} 时出错:`, error);
        }
    }
}

// 在全局范围内声明canvasManager变量
window.canvasManager = null;

// 初始化画布管理器
document.addEventListener('DOMContentLoaded', () => {
    //console.log('canvas.js: DOMContentLoaded事件触发');
    
    // 确保在DOM加载完成后立即初始化canvasManager
    const canvas = document.getElementById('trajectoryCanvas');
    if (canvas) {
        //console.log('canvas.js: 找到trajectoryCanvas元素，初始化CanvasManager');
        window.canvasManager = new CanvasManager('trajectoryCanvas');
        
        // 将canvasManager关联到应用程序
        if (window.app) {
            window.app.canvasManager = window.canvasManager;
            //console.log('画布管理器已关联到应用程序');
        }
        
        // 在控制台输出，方便调试
        //console.log('canvas.js: canvasManager初始化完成', window.canvasManager);
        
        // 发布一个自定义事件，通知其他脚本canvasManager已初始化
        const event = new CustomEvent('canvasManagerReady', { detail: window.canvasManager });
        document.dispatchEvent(event);
    } else {
        console.error('canvas.js: 找不到trajectoryCanvas元素');
    }
    
    // 确保画布尺寸与CSS尺寸一致
    const resizeCanvas = () => {
        const canvas = document.getElementById('trajectoryCanvas');
        if (canvas) {
            const container = canvas.parentElement;
            if (container) {
                // 获取容器的计算样式
                const style = window.getComputedStyle(container);
                const width = parseInt(style.width, 10);
                const height = parseInt(style.height, 10);
                
                // 如果容器尺寸有效，则调整画布尺寸
                if (width > 0 && height > 0) {
                    // 设置画布的实际尺寸与显示尺寸一致
                    canvas.width = width;
                    canvas.height = height;
                    canvas.style.width = width + 'px';
                    canvas.style.height = height + 'px';
                    
                    // 重绘画布
                    if (window.canvasManager) {
                        window.canvasManager.redrawCanvas();
                    }
                    
                    //console.log('画布尺寸已调整为:', width, 'x', height);
                }
            }
        }
    };
    
    // 初始调整画布尺寸
    setTimeout(resizeCanvas, 100);
    
    // 监听窗口大小变化
    window.addEventListener('resize', resizeCanvas);
});

// 确保CanvasManager类在全局范围内可用
window.CanvasManager = CanvasManager;
