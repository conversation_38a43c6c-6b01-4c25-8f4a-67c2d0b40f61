# 舞台轨迹自动化系统 - 项目需求规划文档

## 项目概述

### 项目名称
舞台轨迹自动化系统 (Stage Trajectory Automation System)

### 项目背景
开发一个专业级的舞台轨迹控制系统，主要用于音频灯光联动场景，要求毫秒级响应和高并发处理能力，满足现场演出的严格时序要求。

### 核心价值
- 提供毫秒级精确轨迹控制
- 支持音频灯光实时联动
- 实现多场景并发播放
- 提供专业级性能优化

## 技术架构要求

### 后端技术栈
- **.NET Core 8.0** - 主要开发框架
- **NewLife.XCode** - 高性能ORM框架（替代EF Core，性能提升75%）
- **SQLite WAL模式** - 数据库，优化并发读写
- **SignalR** - 实时通信
- **UDP协议** - 与外部设备通信
- **Swagger** - API文档

### 前端技术栈
- **HTML5 Canvas** - 轨迹绘制和可视化
- **JavaScript ES6** - 前端交互逻辑
- **SignalR Client** - 实时通信
- **CSS3** - 界面样式

### 架构模式
采用分层架构设计：
```
Web层 (API + 前端界面)
├── Controllers - RESTful API
├── Hubs - SignalR实时通信
└── Views - Web界面

Core层 (核心业务)
├── Entities - 实体模型
├── Interfaces - 服务接口
└── Models - 数据传输对象

Infrastructure层 (基础设施)
├── Data - 数据访问层
└── Services - 服务实现
```

## 核心功能需求

### 1. 场景管理模块
**功能描述**: 管理舞台场景的基础配置
**核心需求**:
- 场景创建、编辑、删除
- 场景尺寸配置（宽度、高度）
- 背景图片上传和管理
- 坐标系配置（原点、缩放比例、Y轴反转）
- 服务器连接配置（IP地址、端口）

**数据模型**:
```csharp
Scene {
    Id, Name, Width, Height, 
    ServerIp, ServerPort, BackgroundImagePath,
    OriginX, OriginY, PixelToMeterRatio,
    ScaleX, ScaleY, InvertY
}
```

### 2. 通道管理模块
**功能描述**: 管理控制通道，每个通道对应一个控制设备
**核心需求**:
- 通道创建、编辑、删除
- 通道激活状态控制
- 网络配置（IP地址、端口、通道号）
- Z轴支持配置（启用/禁用、数据范围）
- 通道颜色标识

**数据模型**:
```csharp
Channel {
    Id, Name, IsActive, ChannelNumber,
    IpAddress, Port, Address, Color,
    UseZAxis, ZAxisMin, ZAxisMax,
    SceneId
}
```

### 3. 轨迹管理模块
**功能描述**: 轨迹的创建、编辑和可视化管理
**核心需求**:
- 多种轨迹类型支持：
  - 圆形轨迹
  - 矩形轨迹  
  - 多边形轨迹
  - 自由绘制轨迹
  - 实时轨迹
- 轨迹属性配置：
  - 运行时间、循环设置
  - 逆向播放支持
  - Z轴数据配置
- 轨迹样式设置：
  - 线条颜色、宽度、样式、透明度
  - 轨迹点显示、大小、颜色
  - 起点终点标记
  - 阴影效果

**数据模型**:
```csharp
Trajectory {
    Id, Name, Type, Duration, IsLoop, LoopCount,
    IsReverse, UseZAxis, ZAxisMin, ZAxisMax,
    LineColor, LineWidth, LineStyle, LineOpacity,
    ShowPoints, PointSize, PointColor,
    ShowEndpoints, StartPointColor, EndPointColor,
    EnableShadow, ShadowColor, ShadowBlur,
    ChannelId
}
```

### 4. 轨迹点管理模块
**功能描述**: 管理轨迹的具体坐标点和时间戳
**核心需求**:
- 精确坐标存储（X、Y坐标）
- 时间戳管理（相对于轨迹开始时间）
- 点位顺序管理
- 物理坐标转换（像素到米）

**数据模型**:
```csharp
TrajectoryPoint {
    Id, X, Y, Timestamp, Order, TrajectoryId
}
```

## 高性能播放控制需求

### 1. 播放控制模块
**功能描述**: 实现高性能的轨迹播放控制
**核心需求**:
- **多层级播放控制**:
  - 单个轨迹播放
  - 通道级别播放（播放通道下所有轨迹）
  - 场景级别播放（播放场景下所有轨迹）
- **播放状态管理**:
  - 播放、暂停、停止
  - 循环播放、逆向播放
  - 播放进度跟踪
- **并发播放支持**:
  - 多场景同时播放
  - 智能重复请求处理
  - 线程安全的状态管理

### 2. 实时通信模块
**功能描述**: 实现客户端与服务器的实时数据同步
**核心需求**:
- **SignalR Hub**:
  - 实时播放状态推送
  - 轨迹数据实时同步
  - 客户端连接管理
- **UDP通信**:
  - 与外部设备的高效数据传输
  - 坐标数据实时发送
  - Z轴数据支持

### 3. UDP连接池管理
**功能描述**: 高效管理UDP网络连接
**核心需求**:
- **分层连接管理**:
  - 场景级别连接（所有轨迹共享）
  - 通道级别连接（通道内轨迹共享）
  - 轨迹级别连接（独立连接，最高性能）
- **连接优化**:
  - 智能连接复用
  - 引用计数管理
  - 自动资源清理
  - 连接健康检查

## 性能要求

### 1. 响应时间要求
- **轨迹播放延迟**: < 10ms（目标2-5ms）
- **数据库查询时间**: < 5ms（目标1-3ms）
- **UDP数据发送延迟**: < 100ms
- **SignalR消息推送**: < 50ms

### 2. 并发性能要求
- **同时支持通道数**: 100+
- **并发播放场景数**: 10+
- **API并发请求**: 1000/秒
- **WebSocket连接数**: 100+

### 3. 资源使用要求
- **内存占用**: < 100MB（空载），< 1GB（满载）
- **CPU占用**: < 10%（空载），< 50%（满载）
- **数据库大小**: 支持TB级数据
- **启动时间**: < 3秒

### 4. 音频灯光联动性能
- **音频同步延迟**: < 10ms
- **节拍检测精度**: ±5ms
- **多声道支持**: 8声道
- **采样率**: 48kHz

## 用户界面需求

### 1. 主界面布局
- **左侧面板**: 场景管理列表
- **中左面板**: 通道管理列表  
- **中央区域**: Canvas轨迹绘制区域
- **右侧面板**: 轨迹列表和属性配置
- **底部面板**: 播放控制和状态显示

### 2. 交互功能
- **轨迹绘制工具**:
  - 选择工具、圆形工具、矩形工具
  - 多边形工具、自由绘制工具
  - 实时轨迹工具
- **Canvas操作**:
  - 缩放、平移、选择
  - 轨迹编辑、删除
  - 背景图片显示
- **播放控制**:
  - 播放/暂停/停止按钮
  - 进度条显示
  - 播放状态指示

### 3. 实时反馈
- **播放状态实时更新**
- **轨迹播放进度可视化**
- **连接状态指示**
- **性能监控面板**

## API接口需求

### 1. RESTful API
- **场景管理**: CRUD操作
- **通道管理**: CRUD操作
- **轨迹管理**: CRUD操作
- **播放控制**: 播放/停止/状态查询
- **文件上传**: 背景图片上传

### 2. SignalR实时接口
- **播放状态推送**
- **轨迹数据同步**
- **实时坐标发送**
- **系统状态通知**

## 部署和配置需求

### 1. 部署环境
- **操作系统**: Windows/Linux
- **运行时**: .NET 8.0
- **数据库**: SQLite（支持WAL模式）
- **Web服务器**: Kestrel

### 2. 配置管理
- **数据库连接配置**
- **性能参数配置**
- **音频灯光同步配置**
- **UDP连接池配置**

### 3. 监控和日志
- **性能监控面板**
- **实时性能指标**
- **错误日志记录**
- **操作审计日志**

## 项目规划建议

请基于以上需求，提供：
1. **详细的项目架构设计**
2. **开发阶段划分和里程碑**
3. **技术选型的具体建议**
4. **数据库设计方案**
5. **API设计规范**
6. **性能优化策略**
7. **测试策略和质量保证**
8. **部署和运维方案**

## 特别关注点

1. **性能优化**: 如何实现毫秒级响应和高并发
2. **实时性**: SignalR和UDP通信的最佳实践
3. **可扩展性**: 系统架构的可扩展设计
4. **稳定性**: 错误处理和容错机制
5. **用户体验**: 前端交互的流畅性和响应性
