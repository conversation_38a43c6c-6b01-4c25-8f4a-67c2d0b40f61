using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;

namespace TrajectoryAuto.Core.Interfaces
{
    /// <summary>
    /// UDP连接池管理接口，负责管理和复用UDP连接
    /// </summary>
    public interface IUdpConnectionPoolManager : IDisposable
    {
        /// <summary>
        /// 获取或创建轨迹级别的UDP连接
        /// </summary>
        /// <param name="trajectoryId">轨迹ID</param>
        /// <param name="channelId">通道ID</param>
        /// <param name="sceneId">场景ID</param>
        /// <param name="targetEndpoint">目标端点</param>
        /// <returns>UDP连接信息</returns>
        Task<UdpConnectionInfo> GetTrajectoryConnectionAsync(int trajectoryId, int channelId, int sceneId, IPEndPoint targetEndpoint);

        /// <summary>
        /// 获取或创建通道级别的UDP连接
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="sceneId">场景ID</param>
        /// <param name="targetEndpoint">目标端点</param>
        /// <returns>UDP连接信息</returns>
        Task<UdpConnectionInfo> GetChannelConnectionAsync(int channelId, int sceneId, IPEndPoint targetEndpoint);

        /// <summary>
        /// 释放轨迹级别的UDP连接
        /// </summary>
        /// <param name="trajectoryId">轨迹ID</param>
        Task ReleaseTrajectoryConnectionAsync(int trajectoryId);

        /// <summary>
        /// 释放通道级别的UDP连接
        /// </summary>
        /// <param name="channelId">通道ID</param>
        Task ReleaseChannelConnectionAsync(int channelId);

        /// <summary>
        /// 释放场景下所有连接（包括通道和轨迹）
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        Task ReleaseAllSceneConnectionsAsync(int sceneId);

        /// <summary>
        /// 释放通道下所有轨迹连接
        /// </summary>
        /// <param name="channelId">通道ID</param>
        Task ReleaseAllChannelConnectionsAsync(int channelId);

        /// <summary>
        /// 手动清理空闲连接
        /// </summary>
        /// <param name="idleTimeoutMinutes">空闲超时时间（分钟）</param>
        /// <returns>清理的连接数量</returns>
        Task<int> CleanupIdleConnectionsAsync(int idleTimeoutMinutes = 10);

        /// <summary>
        /// 获取连接池统计信息
        /// </summary>
        /// <returns>连接池统计</returns>
        Task<UdpConnectionPoolStats> GetConnectionPoolStatsAsync();

        /// <summary>
        /// 发送数据到指定连接
        /// </summary>
        /// <param name="connectionInfo">连接信息</param>
        /// <param name="data">要发送的数据</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendDataAsync(UdpConnectionInfo connectionInfo, byte[] data);

        /// <summary>
        /// 批量发送数据
        /// </summary>
        /// <param name="connectionInfo">连接信息</param>
        /// <param name="dataList">数据列表</param>
        /// <returns>成功发送的数据包数量</returns>
        Task<int> SendBatchDataAsync(UdpConnectionInfo connectionInfo, IList<byte[]> dataList);
    }

    /// <summary>
    /// UDP连接信息
    /// </summary>
    public class UdpConnectionInfo
    {
        /// <summary>
        /// 连接唯一标识
        /// </summary>
        public string ConnectionId { get; set; } = string.Empty;

        /// <summary>
        /// UDP客户端
        /// </summary>
        public UdpClient UdpClient { get; set; } = null!;

        /// <summary>
        /// 目标端点
        /// </summary>
        public IPEndPoint TargetEndpoint { get; set; } = null!;

        /// <summary>
        /// 连接类型
        /// </summary>
        public UdpConnectionType ConnectionType { get; set; }

        /// <summary>
        /// 关联的场景ID
        /// </summary>
        public int SceneId { get; set; }

        /// <summary>
        /// 关联的通道ID（如果是通道或轨迹级别连接）
        /// </summary>
        public int? ChannelId { get; set; }

        /// <summary>
        /// 关联的轨迹ID（如果是轨迹级别连接）
        /// </summary>
        public int? TrajectoryId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime LastUsedAt { get; set; }

        /// <summary>
        /// 引用计数（多少个任务在使用这个连接）
        /// </summary>
        public int ReferenceCount { get; set; }

        /// <summary>
        /// 是否正在使用中
        /// </summary>
        public bool IsInUse => ReferenceCount > 0;

        /// <summary>
        /// 发送统计
        /// </summary>
        public UdpSendStats SendStats { get; set; } = new();
    }

    /// <summary>
    /// UDP连接类型
    /// </summary>
    public enum UdpConnectionType
    {
        /// <summary>
        /// 通道级别连接，用于实时绘制的UDP通讯
        /// </summary>
        Channel = 1,

        /// <summary>
        /// 轨迹级别连接，用于发送轨迹数据
        /// </summary>
        Trajectory = 2
    }

    /// <summary>
    /// UDP发送统计
    /// </summary>
    public class UdpSendStats
    {
        /// <summary>
        /// 总发送次数
        /// </summary>
        public long TotalSendCount { get; set; }

        /// <summary>
        /// 总发送字节数
        /// </summary>
        public long TotalBytesSent { get; set; }

        /// <summary>
        /// 发送失败次数
        /// </summary>
        public long FailedSendCount { get; set; }

        /// <summary>
        /// 平均发送延迟（毫秒）
        /// </summary>
        public double AverageLatencyMs { get; set; }

        /// <summary>
        /// 最后发送时间
        /// </summary>
        public DateTime? LastSendTime { get; set; }
    }

    /// <summary>
    /// UDP连接池统计信息
    /// </summary>
    public class UdpConnectionPoolStats
    {
        /// <summary>
        /// 通道级别连接数
        /// </summary>
        public int ChannelConnectionCount { get; set; }

        /// <summary>
        /// 轨迹级别连接数
        /// </summary>
        public int TrajectoryConnectionCount { get; set; }

        /// <summary>
        /// 总连接数（不再包含场景级别连接）
        /// </summary>
        public int TotalConnectionCount => ChannelConnectionCount + TrajectoryConnectionCount;

        /// <summary>
        /// 活跃连接数（正在使用的）
        /// </summary>
        public int ActiveConnectionCount { get; set; }

        /// <summary>
        /// 空闲连接数
        /// </summary>
        public int IdleConnectionCount { get; set; }

        /// <summary>
        /// 总发送次数
        /// </summary>
        public long TotalSendCount { get; set; }

        /// <summary>
        /// 总发送字节数
        /// </summary>
        public long TotalBytesSent { get; set; }

        /// <summary>
        /// 平均连接年龄（分钟）
        /// </summary>
        public double AverageConnectionAgeMinutes { get; set; }

        /// <summary>
        /// 连接池效率（复用率）
        /// </summary>
        public double PoolEfficiency { get; set; }

        /// <summary>
        /// 最后清理时间
        /// </summary>
        public DateTime? LastCleanupTime { get; set; }

        /// <summary>
        /// 清理的连接数量
        /// </summary>
        public int CleanedConnectionCount { get; set; }
    }
}