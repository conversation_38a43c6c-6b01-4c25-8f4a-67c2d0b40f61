﻿#Software: TrajectoryAuto.Web
#ProcessID: 15112 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:09:53.2650000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
00:27:26.197  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
00:27:26.202  1 N - NewLife组件核心库 ©2002-2024 NewLife
00:27:26.203  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
00:27:26.203  1 N - TrajectoryAuto.Web 
00:27:26.203  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
00:27:26.203  1 N - NewLife数据中间件 ©2002-2024 NewLife
00:27:26.203  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
00:27:26.649  1 N - [TrajectoryAuto]待检查数据表：Scenes
00:27:26.682  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
00:27:26.751  1 N - [TrajectoryAuto] select * from sqlite_master
00:27:26.759  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
00:27:26.760  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
00:27:26.828  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
00:27:26.834  1 N - [TrajectoryAuto] Select Count(*) From Scenes
00:27:26.848  1 N - [TrajectoryAuto]待检查数据表：Channels
00:27:26.848  1 N - [TrajectoryAuto] select * from sqlite_master
00:27:26.862  1 N - [TrajectoryAuto] Alter Table Channels Add Address nvarchar(100) NULL COLLATE NOCASE
00:27:26.875  1 N - [TrajectoryAuto] Alter Table Channels Add UseZAxis bit NULL
00:27:26.889  1 N - [TrajectoryAuto] Alter Table Channels Add ZAxisMin real NULL
00:27:26.899  1 N - [TrajectoryAuto] Alter Table Channels Add ZAxisMax real NULL
00:27:26.911  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
00:27:26.911  1 N - [TrajectoryAuto] Select Count(*) From Channels
00:27:26.922  1 N - [TrajectoryAuto]待检查数据表：Trajectories
00:27:26.923  1 N - [TrajectoryAuto] select * from sqlite_master
00:27:26.932  1 N - [TrajectoryAuto] Alter Table Trajectories Add LoopCount int NULL
00:27:26.945  1 N - [TrajectoryAuto] Alter Table Trajectories Add IsReverse bit NULL
00:27:26.958  1 N - [TrajectoryAuto] Alter Table Trajectories Add UseZAxis bit NULL
00:27:26.970  1 N - [TrajectoryAuto] Alter Table Trajectories Add ZAxisMin real NULL
00:27:26.982  1 N - [TrajectoryAuto] Alter Table Trajectories Add ZAxisMax real NULL
00:27:26.994  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
00:27:26.996  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
00:27:27.005  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
00:27:27.005  1 N - [TrajectoryAuto] select * from sqlite_master
00:27:27.015  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
00:27:27.016  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
00:27:27.024  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 3424 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 06:44:31.5930000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:02:04.524  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:02:04.531  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:02:04.532  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:02:04.532  1 N - TrajectoryAuto.Web 
01:02:04.532  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:02:04.532  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:02:04.532  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:02:05.014  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:02:05.021  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:02:05.041  1 N - [TrajectoryAuto] select * from sqlite_master
01:02:05.050  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:02:05.051  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:02:05.106  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 3
01:02:05.111  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:02:05.123  1 N - [TrajectoryAuto]待检查数据表：Channels
01:02:05.124  1 N - [TrajectoryAuto] select * from sqlite_master
01:02:05.134  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
01:02:05.134  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:02:05.145  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:02:05.146  1 N - [TrajectoryAuto] select * from sqlite_master
01:02:05.156  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
01:02:05.157  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:02:05.169  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:02:05.170  1 N - [TrajectoryAuto] select * from sqlite_master
01:02:05.180  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
01:02:05.181  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:02:05.194  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 13152 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 13:40:38.0150000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
07:58:10.940  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
07:58:10.946  1 N - NewLife组件核心库 ©2002-2024 NewLife
07:58:10.947  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
07:58:10.947  1 N - TrajectoryAuto.Web 
07:58:10.947  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
07:58:10.947  1 N - NewLife数据中间件 ©2002-2024 NewLife
07:58:10.947  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
07:58:11.464  1 N - [TrajectoryAuto]待检查数据表：Scenes
07:58:11.471  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
07:58:11.493  1 N - [TrajectoryAuto] select * from sqlite_master
07:58:11.503  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
07:58:11.504  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
07:58:11.560  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 3
07:58:11.565  1 N - [TrajectoryAuto] Select Count(*) From Scenes
07:58:11.578  1 N - [TrajectoryAuto]待检查数据表：Channels
07:58:11.579  1 N - [TrajectoryAuto] select * from sqlite_master
07:58:11.588  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 2
07:58:11.589  1 N - [TrajectoryAuto] Select Count(*) From Channels
07:58:11.602  1 N - [TrajectoryAuto]待检查数据表：Trajectories
07:58:11.603  1 N - [TrajectoryAuto] select * from sqlite_master
07:58:11.613  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
07:58:11.614  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
07:58:11.624  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
07:58:11.625  1 N - [TrajectoryAuto] select * from sqlite_master
07:58:11.633  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
07:58:11.634  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
07:58:11.642  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 25508 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 13:47:19.5310000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
08:04:52.462  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
08:04:52.466  1 N - NewLife组件核心库 ©2002-2024 NewLife
08:04:52.466  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
08:04:52.466  1 N - TrajectoryAuto.Web 
08:04:52.466  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
08:04:52.466  1 N - NewLife数据中间件 ©2002-2024 NewLife
08:04:52.467  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
08:04:52.902  1 N - [TrajectoryAuto]待检查数据表：Scenes
08:04:52.911  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
08:04:52.933  1 N - [TrajectoryAuto] select * from sqlite_master
08:04:52.942  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
08:04:52.943  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
08:04:52.995  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 3
08:04:52.999  1 N - [TrajectoryAuto] Select Count(*) From Scenes
08:04:53.012  1 N - [TrajectoryAuto]待检查数据表：Channels
08:04:53.013  1 N - [TrajectoryAuto] select * from sqlite_master
08:04:53.023  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 6
08:04:53.023  1 N - [TrajectoryAuto] Select Count(*) From Channels
08:04:53.034  1 N - [TrajectoryAuto]待检查数据表：Trajectories
08:04:53.035  1 N - [TrajectoryAuto] select * from sqlite_master
08:04:53.044  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
08:04:53.045  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
08:04:53.055  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
08:04:53.056  1 N - [TrajectoryAuto] select * from sqlite_master
08:04:53.066  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
08:04:53.067  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
08:04:53.074  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 9280 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:05:36.8430000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
09:18:37.796  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
09:18:37.803  1 N - NewLife组件核心库 ©2002-2024 NewLife
09:18:37.804  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
09:18:37.804  1 N - TrajectoryAuto.Web 
09:18:37.804  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
09:18:37.804  1 N - NewLife数据中间件 ©2002-2024 NewLife
09:18:37.804  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
09:18:38.155  1 N - [TrajectoryAuto]待检查数据表：Scenes
09:18:38.161  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
09:18:38.180  1 N - [TrajectoryAuto] select * from sqlite_master
09:18:38.188  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
09:18:38.189  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
09:18:38.237  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 3
09:18:38.243  1 N - [TrajectoryAuto] Select Count(*) From Scenes
09:18:38.252  1 N - [TrajectoryAuto]待检查数据表：Channels
09:18:38.253  1 N - [TrajectoryAuto] select * from sqlite_master
09:18:38.259  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
09:18:38.259  1 N - [TrajectoryAuto] Select Count(*) From Channels
09:18:38.266  1 N - [TrajectoryAuto]待检查数据表：Trajectories
09:18:38.267  1 N - [TrajectoryAuto] select * from sqlite_master
09:18:38.273  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
09:18:38.274  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
09:18:38.280  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
09:18:38.281  1 N - [TrajectoryAuto] select * from sqlite_master
09:18:38.286  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
09:18:38.287  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
09:18:38.300  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23348 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:21:02.2810000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
10:34:03.227  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
10:34:03.232  1 N - NewLife组件核心库 ©2002-2024 NewLife
10:34:03.232  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
10:34:03.232  1 N - TrajectoryAuto.Web 
10:34:03.232  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
10:34:03.232  1 N - NewLife数据中间件 ©2002-2024 NewLife
10:34:03.232  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
10:34:03.824  1 N - [TrajectoryAuto]待检查数据表：Scenes
10:34:03.834  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
10:34:03.867  1 N - [TrajectoryAuto] select * from sqlite_master
10:34:03.880  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
10:34:03.881  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
10:34:03.954  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 3
10:34:03.960  1 N - [TrajectoryAuto] Select Count(*) From Scenes
10:34:03.975  1 N - [TrajectoryAuto]待检查数据表：Channels
10:34:03.976  1 N - [TrajectoryAuto] select * from sqlite_master
10:34:03.985  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
10:34:03.986  1 N - [TrajectoryAuto] Select Count(*) From Channels
10:34:03.998  1 N - [TrajectoryAuto]待检查数据表：Trajectories
10:34:03.998  1 N - [TrajectoryAuto] select * from sqlite_master
10:34:04.010  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
10:34:04.010  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
10:34:04.022  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
10:34:04.022  1 N - [TrajectoryAuto] select * from sqlite_master
10:34:04.033  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
10:34:04.033  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
10:34:04.043  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 22752 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:27:18.8590000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
10:40:19.811  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
10:40:19.816  1 N - NewLife组件核心库 ©2002-2024 NewLife
10:40:19.817  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
10:40:19.817  1 N - TrajectoryAuto.Web 
10:40:19.817  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
10:40:19.817  1 N - NewLife数据中间件 ©2002-2024 NewLife
10:40:19.817  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
10:40:20.296  1 N - [TrajectoryAuto]待检查数据表：Scenes
10:40:20.304  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
10:40:20.329  1 N - [TrajectoryAuto] select * from sqlite_master
10:40:20.339  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
10:40:20.339  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
10:40:20.398  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
10:40:20.403  1 N - [TrajectoryAuto] Select Count(*) From Scenes
10:40:20.415  1 N - [TrajectoryAuto]待检查数据表：Channels
10:40:20.416  1 N - [TrajectoryAuto] select * from sqlite_master
10:40:20.425  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
10:40:20.426  1 N - [TrajectoryAuto] Select Count(*) From Channels
10:40:20.437  1 N - [TrajectoryAuto]待检查数据表：Trajectories
10:40:20.438  1 N - [TrajectoryAuto] select * from sqlite_master
10:40:20.447  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
10:40:20.448  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
10:40:20.457  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
10:40:20.458  1 N - [TrajectoryAuto] select * from sqlite_master
10:40:20.467  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
10:40:20.467  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
10:40:20.478  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 17916 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:31:47.3430000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
10:44:48.297  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
10:44:48.303  1 N - NewLife组件核心库 ©2002-2024 NewLife
10:44:48.304  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
10:44:48.304  1 N - TrajectoryAuto.Web 
10:44:48.304  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
10:44:48.304  1 N - NewLife数据中间件 ©2002-2024 NewLife
10:44:48.304  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
10:44:49.085  1 N - [TrajectoryAuto]待检查数据表：Scenes
10:44:49.096  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
10:44:49.125  1 N - [TrajectoryAuto] select * from sqlite_master
10:44:49.140  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
10:44:49.140  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
10:44:49.233  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
10:44:49.239  1 N - [TrajectoryAuto] Select Count(*) From Scenes
10:44:49.260  1 N - [TrajectoryAuto]待检查数据表：Channels
10:44:49.260  1 N - [TrajectoryAuto] select * from sqlite_master
10:44:49.279  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
10:44:49.280  1 N - [TrajectoryAuto] Select Count(*) From Channels
10:44:49.303  1 N - [TrajectoryAuto]待检查数据表：Trajectories
10:44:49.303  1 N - [TrajectoryAuto] select * from sqlite_master
10:44:49.319  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
10:44:49.319  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
10:44:49.336  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
10:44:49.337  1 N - [TrajectoryAuto] select * from sqlite_master
10:44:49.350  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
10:44:49.351  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
10:44:49.369  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 25408 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:34:40.9210000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
10:47:41.872  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
10:47:41.875  1 N - NewLife组件核心库 ©2002-2024 NewLife
10:47:41.876  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
10:47:41.876  1 N - TrajectoryAuto.Web 
10:47:41.876  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
10:47:41.876  1 N - NewLife数据中间件 ©2002-2024 NewLife
10:47:41.876  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
10:47:42.387  1 N - [TrajectoryAuto]待检查数据表：Scenes
10:47:42.395  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
10:47:42.421  1 N - [TrajectoryAuto] select * from sqlite_master
10:47:42.431  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
10:47:42.432  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
10:47:42.488  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
10:47:42.493  1 N - [TrajectoryAuto] Select Count(*) From Scenes
10:47:42.506  1 N - [TrajectoryAuto]待检查数据表：Channels
10:47:42.507  1 N - [TrajectoryAuto] select * from sqlite_master
10:47:42.517  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
10:47:42.517  1 N - [TrajectoryAuto] Select Count(*) From Channels
10:47:42.528  1 N - [TrajectoryAuto]待检查数据表：Trajectories
10:47:42.529  1 N - [TrajectoryAuto] select * from sqlite_master
10:47:42.538  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
10:47:42.539  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
10:47:42.548  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
10:47:42.549  1 N - [TrajectoryAuto] select * from sqlite_master
10:47:42.559  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
10:47:42.560  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
10:47:42.573  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 25720 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:49:18.2500000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:02:19.190  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
11:02:19.196  1 N - NewLife组件核心库 ©2002-2024 NewLife
11:02:19.196  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
11:02:19.196  1 N - TrajectoryAuto.Web 
11:02:19.196  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
11:02:19.196  1 N - NewLife数据中间件 ©2002-2024 NewLife
11:02:19.196  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
11:02:19.833  1 N - [TrajectoryAuto]待检查数据表：Scenes
11:02:19.845  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
11:02:19.874  1 N - [TrajectoryAuto] select * from sqlite_master
11:02:19.886  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
11:02:19.887  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
11:02:19.962  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
11:02:19.968  1 N - [TrajectoryAuto] Select Count(*) From Scenes
11:02:19.985  1 N - [TrajectoryAuto]待检查数据表：Channels
11:02:19.985  1 N - [TrajectoryAuto] select * from sqlite_master
11:02:19.996  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
11:02:19.997  1 N - [TrajectoryAuto] Select Count(*) From Channels
11:02:20.010  1 N - [TrajectoryAuto]待检查数据表：Trajectories
11:02:20.010  1 N - [TrajectoryAuto] select * from sqlite_master
11:02:20.021  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
11:02:20.021  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
11:02:20.033  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
11:02:20.034  1 N - [TrajectoryAuto] select * from sqlite_master
11:02:20.044  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
11:02:20.045  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
11:02:20.059  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 19020 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:50:26.0780000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:03:27.026  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
11:03:27.032  1 N - NewLife组件核心库 ©2002-2024 NewLife
11:03:27.032  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
11:03:27.033  1 N - TrajectoryAuto.Web 
11:03:27.033  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
11:03:27.033  1 N - NewLife数据中间件 ©2002-2024 NewLife
11:03:27.033  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
11:03:27.611  1 N - [TrajectoryAuto]待检查数据表：Scenes
11:03:27.619  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
11:03:27.646  1 N - [TrajectoryAuto] select * from sqlite_master
11:03:27.657  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
11:03:27.658  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
11:03:27.725  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
11:03:27.730  1 N - [TrajectoryAuto] Select Count(*) From Scenes
11:03:27.745  1 N - [TrajectoryAuto]待检查数据表：Channels
11:03:27.745  1 N - [TrajectoryAuto] select * from sqlite_master
11:03:27.756  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
11:03:27.756  1 N - [TrajectoryAuto] Select Count(*) From Channels
11:03:27.769  1 N - [TrajectoryAuto]待检查数据表：Trajectories
11:03:27.770  1 N - [TrajectoryAuto] select * from sqlite_master
11:03:27.781  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
11:03:27.782  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
11:03:27.792  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
11:03:27.794  1 N - [TrajectoryAuto] select * from sqlite_master
11:03:27.804  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
11:03:27.805  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
11:03:27.821  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 10304 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:54:22.7810000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:07:23.727  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
11:07:23.735  1 N - NewLife组件核心库 ©2002-2024 NewLife
11:07:23.736  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
11:07:23.736  1 N - TrajectoryAuto.Web 
11:07:23.736  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
11:07:23.736  1 N - NewLife数据中间件 ©2002-2024 NewLife
11:07:23.736  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
11:07:24.398  1 N - [TrajectoryAuto]待检查数据表：Scenes
11:07:24.407  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
11:07:24.441  1 N - [TrajectoryAuto] select * from sqlite_master
11:07:24.453  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
11:07:24.454  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
11:07:24.524  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
11:07:24.531  1 N - [TrajectoryAuto] Select Count(*) From Scenes
11:07:24.545  1 N - [TrajectoryAuto]待检查数据表：Channels
11:07:24.546  1 N - [TrajectoryAuto] select * from sqlite_master
11:07:24.557  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
11:07:24.557  1 N - [TrajectoryAuto] Select Count(*) From Channels
11:07:24.569  1 N - [TrajectoryAuto]待检查数据表：Trajectories
11:07:24.570  1 N - [TrajectoryAuto] select * from sqlite_master
11:07:24.580  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
11:07:24.581  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
11:07:24.592  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
11:07:24.593  1 N - [TrajectoryAuto] select * from sqlite_master
11:07:24.607  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
11:07:24.608  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
11:07:24.650  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 10260 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:05:36.6400000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:18:37.602  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
11:18:37.605  1 N - NewLife组件核心库 ©2002-2024 NewLife
11:18:37.605  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
11:18:37.606  1 N - TrajectoryAuto.Web 
11:18:37.606  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
11:18:37.606  1 N - NewLife数据中间件 ©2002-2024 NewLife
11:18:37.606  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
11:18:38.073  1 N - [TrajectoryAuto]待检查数据表：Scenes
11:18:38.080  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
11:18:38.101  1 N - [TrajectoryAuto] select * from sqlite_master
11:18:38.110  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
11:18:38.111  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
11:18:38.167  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
11:18:38.172  1 N - [TrajectoryAuto] Select Count(*) From Scenes
11:18:38.195  1 N - [TrajectoryAuto]待检查数据表：Channels
11:18:38.195  1 N - [TrajectoryAuto] select * from sqlite_master
11:18:38.205  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
11:18:38.205  1 N - [TrajectoryAuto] Select Count(*) From Channels
11:18:38.217  1 N - [TrajectoryAuto]待检查数据表：Trajectories
11:18:38.217  1 N - [TrajectoryAuto] select * from sqlite_master
11:18:38.226  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
11:18:38.227  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
11:18:38.236  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
11:18:38.237  1 N - [TrajectoryAuto] select * from sqlite_master
11:18:38.248  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
11:18:38.248  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
11:18:38.264  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 7496 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:15:08.4680000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:28:09.427  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
11:28:09.431  1 N - NewLife组件核心库 ©2002-2024 NewLife
11:28:09.431  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
11:28:09.431  1 N - TrajectoryAuto.Web 
11:28:09.431  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
11:28:09.431  1 N - NewLife数据中间件 ©2002-2024 NewLife
11:28:09.431  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
11:28:09.915  1 N - [TrajectoryAuto]待检查数据表：Scenes
11:28:09.921  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
11:28:09.943  1 N - [TrajectoryAuto] select * from sqlite_master
11:28:09.958  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
11:28:09.959  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
11:28:10.015  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
11:28:10.020  1 N - [TrajectoryAuto] Select Count(*) From Scenes
11:28:10.033  1 N - [TrajectoryAuto]待检查数据表：Channels
11:28:10.034  1 N - [TrajectoryAuto] select * from sqlite_master
11:28:10.043  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
11:28:10.044  1 N - [TrajectoryAuto] Select Count(*) From Channels
11:28:10.057  1 N - [TrajectoryAuto]待检查数据表：Trajectories
11:28:10.057  1 N - [TrajectoryAuto] select * from sqlite_master
11:28:10.068  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
11:28:10.069  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
11:28:10.078  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
11:28:10.079  1 N - [TrajectoryAuto] select * from sqlite_master
11:28:10.088  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
11:28:10.088  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
11:28:10.103  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 22836 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 02:20:32.6710000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:33:33.622  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
11:33:33.626  1 N - NewLife组件核心库 ©2002-2024 NewLife
11:33:33.626  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
11:33:33.626  1 N - TrajectoryAuto.Web 
11:33:33.626  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
11:33:33.626  1 N - NewLife数据中间件 ©2002-2024 NewLife
11:33:33.626  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
11:33:34.174  1 N - [TrajectoryAuto]待检查数据表：Scenes
11:33:34.181  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
11:33:34.202  1 N - [TrajectoryAuto] select * from sqlite_master
11:33:34.213  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
11:33:34.213  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
11:33:34.275  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
11:33:34.280  1 N - [TrajectoryAuto] Select Count(*) From Scenes
11:33:34.303  1 N - [TrajectoryAuto]待检查数据表：Channels
11:33:34.304  1 N - [TrajectoryAuto] select * from sqlite_master
11:33:34.313  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
11:33:34.314  1 N - [TrajectoryAuto] Select Count(*) From Channels
11:33:34.327  1 N - [TrajectoryAuto]待检查数据表：Trajectories
11:33:34.327  1 N - [TrajectoryAuto] select * from sqlite_master
11:33:34.337  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
11:33:34.338  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
11:33:34.348  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
11:33:34.349  1 N - [TrajectoryAuto] select * from sqlite_master
11:33:34.359  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
11:33:34.359  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
11:33:34.374  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 23672 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:09:05.4210000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
14:22:06.372  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
14:22:06.377  1 N - NewLife组件核心库 ©2002-2024 NewLife
14:22:06.378  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
14:22:06.378  1 N - TrajectoryAuto.Web 
14:22:06.378  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
14:22:06.378  1 N - NewLife数据中间件 ©2002-2024 NewLife
14:22:06.378  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
14:22:06.940  1 N - [TrajectoryAuto]待检查数据表：Scenes
14:22:06.952  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
14:22:06.977  1 N - [TrajectoryAuto] select * from sqlite_master
14:22:06.989  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
14:22:06.989  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
14:22:07.063  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
14:22:07.069  1 N - [TrajectoryAuto] Select Count(*) From Scenes
14:22:07.086  1 N - [TrajectoryAuto]待检查数据表：Channels
14:22:07.087  1 N - [TrajectoryAuto] select * from sqlite_master
14:22:07.097  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
14:22:07.097  1 N - [TrajectoryAuto] Select Count(*) From Channels
14:22:07.109  1 N - [TrajectoryAuto]待检查数据表：Trajectories
14:22:07.110  1 N - [TrajectoryAuto] select * from sqlite_master
14:22:07.120  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 21
14:22:07.121  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
14:22:07.132  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
14:22:07.132  1 N - [TrajectoryAuto] select * from sqlite_master
14:22:07.142  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 2,021
14:22:07.143  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
14:22:07.165  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 26408 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:06:50.7340000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:19:51.681  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:19:51.692  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:19:51.693  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:19:51.693  1 N - TrajectoryAuto.Web 
16:19:51.693  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:19:51.693  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:19:51.694  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:19:52.250  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:19:52.262  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:19:52.306  1 N - [TrajectoryAuto] select * from sqlite_master
16:19:52.319  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:19:52.320  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:19:52.525  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:19:52.532  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:19:52.552  1 N - [TrajectoryAuto]待检查数据表：Channels
16:19:52.552  1 N - [TrajectoryAuto] select * from sqlite_master
16:19:52.565  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
16:19:52.565  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:19:52.581  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:19:52.582  1 N - [TrajectoryAuto] select * from sqlite_master
16:19:52.594  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 88
16:19:52.594  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:19:52.606  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:19:52.606  1 N - [TrajectoryAuto] select * from sqlite_master
16:19:52.617  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 8,365
16:19:52.617  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:19:52.648  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 29948 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:16:41.2960000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:29:42.244  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:29:42.250  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:29:42.250  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:29:42.251  1 N - TrajectoryAuto.Web 
16:29:42.251  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:29:42.251  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:29:42.251  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:29:42.910  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:29:42.919  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:29:42.947  1 N - [TrajectoryAuto] select * from sqlite_master
16:29:42.964  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:29:42.965  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:29:43.068  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:29:43.076  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:29:43.092  1 N - [TrajectoryAuto]待检查数据表：Channels
16:29:43.093  1 N - [TrajectoryAuto] select * from sqlite_master
16:29:43.105  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
16:29:43.106  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:29:43.118  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:29:43.118  1 N - [TrajectoryAuto] select * from sqlite_master
16:29:43.130  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 100
16:29:43.131  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:29:43.142  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:29:43.142  1 N - [TrajectoryAuto] select * from sqlite_master
16:29:43.153  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
16:29:43.154  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:29:43.184  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 29880 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:19:26.6090000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:32:27.567  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:32:27.573  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:32:27.574  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:32:27.574  1 N - TrajectoryAuto.Web 
16:32:27.574  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:32:27.574  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:32:27.574  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:32:28.055  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:32:28.063  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:32:28.087  1 N - [TrajectoryAuto] select * from sqlite_master
16:32:28.099  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:32:28.100  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:32:28.177  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:32:28.184  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:32:28.200  1 N - [TrajectoryAuto]待检查数据表：Channels
16:32:28.201  1 N - [TrajectoryAuto] select * from sqlite_master
16:32:28.212  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
16:32:28.213  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:32:28.224  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:32:28.225  1 N - [TrajectoryAuto] select * from sqlite_master
16:32:28.235  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 100
16:32:28.236  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:32:28.247  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:32:28.248  1 N - [TrajectoryAuto] select * from sqlite_master
16:32:28.257  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
16:32:28.258  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:32:28.286  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 24272 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:39:26.5780000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:52:27.528  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:52:27.533  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:52:27.533  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:52:27.533  1 N - TrajectoryAuto.Web 
16:52:27.533  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:52:27.533  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:52:27.533  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:52:28.174  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:52:28.183  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:52:28.211  1 N - [TrajectoryAuto] select * from sqlite_master
16:52:28.222  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:52:28.222  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:52:28.334  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:52:28.343  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:52:28.366  1 N - [TrajectoryAuto]待检查数据表：Channels
16:52:28.366  1 N - [TrajectoryAuto] select * from sqlite_master
16:52:28.381  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
16:52:28.382  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:52:28.397  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:52:28.398  1 N - [TrajectoryAuto] select * from sqlite_master
16:52:28.416  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 100
16:52:28.416  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:52:28.430  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:52:28.431  1 N - [TrajectoryAuto] select * from sqlite_master
16:52:28.445  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
16:52:28.446  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:52:28.478  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 29812 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:42:43.5930000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:55:44.540  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:55:44.547  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:55:44.547  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:55:44.547  1 N - TrajectoryAuto.Web 
16:55:44.547  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:55:44.548  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:55:44.548  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:55:44.978  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:55:44.984  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:55:45.006  1 N - [TrajectoryAuto] select * from sqlite_master
16:55:45.016  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:55:45.016  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:55:45.084  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:55:45.090  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:55:45.104  1 N - [TrajectoryAuto]待检查数据表：Channels
16:55:45.105  1 N - [TrajectoryAuto] select * from sqlite_master
16:55:45.112  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
16:55:45.113  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:55:45.126  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:55:45.127  1 N - [TrajectoryAuto] select * from sqlite_master
16:55:45.136  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
16:55:45.136  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:55:45.145  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:55:45.146  1 N - [TrajectoryAuto] select * from sqlite_master
16:55:45.154  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
16:55:45.155  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:55:45.182  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 24936 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:45:28.1090000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:58:29.070  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
16:58:29.075  1 N - NewLife组件核心库 ©2002-2024 NewLife
16:58:29.076  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
16:58:29.076  1 N - TrajectoryAuto.Web 
16:58:29.076  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
16:58:29.076  1 N - NewLife数据中间件 ©2002-2024 NewLife
16:58:29.076  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
16:58:29.462  1 N - [TrajectoryAuto]待检查数据表：Scenes
16:58:29.468  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
16:58:29.488  1 N - [TrajectoryAuto] select * from sqlite_master
16:58:29.496  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
16:58:29.497  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
16:58:29.556  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
16:58:29.561  1 N - [TrajectoryAuto] Select Count(*) From Scenes
16:58:29.576  1 N - [TrajectoryAuto]待检查数据表：Channels
16:58:29.576  1 N - [TrajectoryAuto] select * from sqlite_master
16:58:29.586  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
16:58:29.587  1 N - [TrajectoryAuto] Select Count(*) From Channels
16:58:29.599  1 N - [TrajectoryAuto]待检查数据表：Trajectories
16:58:29.599  1 N - [TrajectoryAuto] select * from sqlite_master
16:58:29.609  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
16:58:29.609  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
16:58:29.619  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
16:58:29.620  1 N - [TrajectoryAuto] select * from sqlite_master
16:58:29.629  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
16:58:29.629  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
16:58:29.639  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 27656 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 07:55:20.3120000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:08:23.643  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:08:23.648  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:08:23.649  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:08:23.649  1 N - TrajectoryAuto.Web 
17:08:23.649  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:08:23.649  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:08:23.649  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:08:23.931  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:08:23.940  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:08:23.970  1 N - [TrajectoryAuto] select * from sqlite_master
17:08:23.979  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:08:23.979  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:08:24.055  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:08:24.061  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:08:24.077  1 N - [TrajectoryAuto]待检查数据表：Channels
17:08:24.077  1 N - [TrajectoryAuto] select * from sqlite_master
17:08:24.088  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
17:08:24.089  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:08:24.099  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:08:24.099  1 N - [TrajectoryAuto] select * from sqlite_master
17:08:24.109  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
17:08:24.110  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:08:24.120  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:08:24.121  1 N - [TrajectoryAuto] select * from sqlite_master
17:08:24.130  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
17:08:24.131  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:08:24.156  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 3916 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 08:00:06.0310000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:13:09.371  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:13:09.376  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:13:09.376  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:13:09.376  1 N - TrajectoryAuto.Web 
17:13:09.376  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:13:09.376  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:13:09.376  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:13:09.778  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:13:09.784  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:13:09.804  1 N - [TrajectoryAuto] select * from sqlite_master
17:13:09.814  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:13:09.815  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:13:09.942  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:13:09.957  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:13:09.976  1 N - [TrajectoryAuto]待检查数据表：Channels
17:13:09.977  1 N - [TrajectoryAuto] select * from sqlite_master
17:13:09.999  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
17:13:10.000  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:13:10.019  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:13:10.019  1 N - [TrajectoryAuto] select * from sqlite_master
17:13:10.030  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
17:13:10.031  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:13:10.050  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:13:10.050  1 N - [TrajectoryAuto] select * from sqlite_master
17:13:10.064  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
17:13:10.065  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:13:10.103  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 30460 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 08:08:41.6250000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:21:44.948  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:21:44.958  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:21:44.959  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:21:44.960  1 N - TrajectoryAuto.Web 
17:21:44.960  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:21:44.960  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:21:44.960  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:21:46.022  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:21:46.036  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:21:46.089  1 N - [TrajectoryAuto] select * from sqlite_master
17:21:46.109  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:21:46.110  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:21:46.367  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:21:46.381  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:21:46.417  1 N - [TrajectoryAuto]待检查数据表：Channels
17:21:46.417  1 N - [TrajectoryAuto] select * from sqlite_master
17:21:46.435  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
17:21:46.436  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:21:46.458  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:21:46.458  1 N - [TrajectoryAuto] select * from sqlite_master
17:21:46.482  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
17:21:46.482  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:21:46.500  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:21:46.501  1 N - [TrajectoryAuto] select * from sqlite_master
17:21:46.522  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
17:21:46.522  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:21:46.634  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 28908 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 08:14:39.8750000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:27:43.193  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:27:43.203  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:27:43.204  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:27:43.204  1 N - TrajectoryAuto.Web 
17:27:43.204  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:27:43.204  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:27:43.204  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:27:44.247  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:27:44.263  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:27:44.297  1 N - [TrajectoryAuto] select * from sqlite_master
17:27:44.314  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:27:44.314  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:27:44.426  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:27:44.436  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:27:44.456  1 N - [TrajectoryAuto]待检查数据表：Channels
17:27:44.456  1 N - [TrajectoryAuto] select * from sqlite_master
17:27:44.470  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
17:27:44.471  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:27:44.487  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:27:44.488  1 N - [TrajectoryAuto] select * from sqlite_master
17:27:44.505  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
17:27:44.506  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:27:44.535  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:27:44.542  1 N - [TrajectoryAuto] select * from sqlite_master
17:27:44.566  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
17:27:44.567  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:27:44.609  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
17:27:45.183  1 N - System.IO.IOException: Failed to bind to address https://127.0.0.1:55483: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 115
17:27:45.186  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 27752 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 08:17:54.3900000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:30:57.708  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:30:57.719  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:30:57.720  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:30:57.720  1 N - TrajectoryAuto.Web 
17:30:57.720  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:30:57.720  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:30:57.720  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:30:58.481  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:30:58.493  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:30:58.533  1 N - [TrajectoryAuto] select * from sqlite_master
17:30:58.555  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:30:58.555  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:30:58.795  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:30:58.813  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:30:58.842  1 N - [TrajectoryAuto]待检查数据表：Channels
17:30:58.842  1 N - [TrajectoryAuto] select * from sqlite_master
17:30:58.861  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
17:30:58.861  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:30:58.884  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:30:58.884  1 N - [TrajectoryAuto] select * from sqlite_master
17:30:58.898  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
17:30:58.898  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:30:58.917  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:30:58.917  1 N - [TrajectoryAuto] select * from sqlite_master
17:30:58.931  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
17:30:58.932  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:30:58.968  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
17:30:59.397  1 N - System.IO.IOException: Failed to bind to address https://127.0.0.1:55483: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 115
17:30:59.398  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 15748 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 08:19:12.8590000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:32:16.190  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:32:16.199  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:32:16.200  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:32:16.201  1 N - TrajectoryAuto.Web 
17:32:16.201  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:32:16.201  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:32:16.201  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:32:17.344  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:32:17.362  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:32:17.402  1 N - [TrajectoryAuto] select * from sqlite_master
17:32:17.421  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:32:17.422  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:32:17.585  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:32:17.600  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:32:17.640  1 N - [TrajectoryAuto]待检查数据表：Channels
17:32:17.640  1 N - [TrajectoryAuto] select * from sqlite_master
17:32:17.661  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
17:32:17.662  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:32:17.693  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:32:17.695  1 N - [TrajectoryAuto] select * from sqlite_master
17:32:17.714  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
17:32:17.715  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:32:17.732  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:32:17.733  1 N - [TrajectoryAuto] select * from sqlite_master
17:32:17.746  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
17:32:17.747  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:32:17.800  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
17:32:18.360  1 N - System.IO.IOException: Failed to bind to address https://127.0.0.1:55483: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 115
17:32:18.365  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 24984 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 08:27:30.9840000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:40:34.302  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:40:34.321  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:40:34.322  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:40:34.322  1 N - TrajectoryAuto.Web 
17:40:34.322  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:40:34.322  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:40:34.322  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:40:35.167  1 N - [TrajectoryAuto]待检查数据表：Scenes
17:40:35.184  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:40:35.263  1 N - [TrajectoryAuto] select * from sqlite_master
17:40:35.290  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:40:35.293  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:40:35.469  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
17:40:35.505  1 N - [TrajectoryAuto] Select Count(*) From Scenes
17:40:35.529  1 N - [TrajectoryAuto]待检查数据表：Channels
17:40:35.530  1 N - [TrajectoryAuto] select * from sqlite_master
17:40:35.559  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
17:40:35.560  1 N - [TrajectoryAuto] Select Count(*) From Channels
17:40:35.582  1 N - [TrajectoryAuto]待检查数据表：Trajectories
17:40:35.583  1 N - [TrajectoryAuto] select * from sqlite_master
17:40:35.605  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
17:40:35.606  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
17:40:35.629  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:40:35.630  1 N - [TrajectoryAuto] select * from sqlite_master
17:40:35.657  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
17:40:35.663  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
17:40:35.710  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
17:40:36.274  1 N - System.IO.IOException: Failed to bind to address https://127.0.0.1:55483: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\Program.cs:line 115
17:40:36.278  1 N - 异常退出！

#Software: TrajectoryAuto.Web
#ProcessID: 18836 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32764/1000
#SystemStarted: 00:02:24.2650000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:41:00.199  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
18:41:00.214  1 N - NewLife组件核心库 ©2002-2024 NewLife
18:41:00.215  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
18:41:00.215  1 N - TrajectoryAuto.Web 
18:41:00.215  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
18:41:00.215  1 N - NewLife数据中间件 ©2002-2024 NewLife
18:41:00.216  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
18:41:01.075  1 N - [TrajectoryAuto]待检查数据表：Scenes
18:41:01.085  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
18:41:01.124  1 N - [TrajectoryAuto] select * from sqlite_master
18:41:01.137  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
18:41:01.138  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
18:41:01.245  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
18:41:01.254  1 N - [TrajectoryAuto] Select Count(*) From Scenes
18:41:01.272  1 N - [TrajectoryAuto]待检查数据表：Channels
18:41:01.272  1 N - [TrajectoryAuto] select * from sqlite_master
18:41:01.281  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
18:41:01.281  1 N - [TrajectoryAuto] Select Count(*) From Channels
18:41:01.292  1 N - [TrajectoryAuto]待检查数据表：Trajectories
18:41:01.293  1 N - [TrajectoryAuto] select * from sqlite_master
18:41:01.303  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
18:41:01.303  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
18:41:01.313  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
18:41:01.314  1 N - [TrajectoryAuto] select * from sqlite_master
18:41:01.324  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
18:41:01.324  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
18:41:01.355  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 14500 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:10:34.5310000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
19:49:10.480  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
19:49:10.484  1 N - NewLife组件核心库 ©2002-2024 NewLife
19:49:10.485  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
19:49:10.485  1 N - TrajectoryAuto.Web 
19:49:10.485  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
19:49:10.485  1 N - NewLife数据中间件 ©2002-2024 NewLife
19:49:10.485  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
19:49:10.928  1 N - [TrajectoryAuto]待检查数据表：Scenes
19:49:10.935  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
19:49:10.959  1 N - [TrajectoryAuto] select * from sqlite_master
19:49:10.968  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
19:49:10.969  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
19:49:11.023  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
19:49:11.028  1 N - [TrajectoryAuto] Select Count(*) From Scenes
19:49:11.041  1 N - [TrajectoryAuto]待检查数据表：Channels
19:49:11.041  1 N - [TrajectoryAuto] select * from sqlite_master
19:49:11.050  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
19:49:11.051  1 N - [TrajectoryAuto] Select Count(*) From Channels
19:49:11.060  1 N - [TrajectoryAuto]待检查数据表：Trajectories
19:49:11.060  1 N - [TrajectoryAuto] select * from sqlite_master
19:49:11.070  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 93
19:49:11.071  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
19:49:11.080  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
19:49:11.080  1 N - [TrajectoryAuto] select * from sqlite_master
19:49:11.088  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 12,037
19:49:11.089  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
19:49:11.098  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 14328 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:07:51.6710000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:46:27.627  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:46:27.632  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:46:27.632  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:46:27.632  1 N - TrajectoryAuto.Web 
23:46:27.632  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:46:27.632  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:46:27.632  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:46:28.117  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:46:28.124  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:46:28.147  1 N - [TrajectoryAuto] select * from sqlite_master
23:46:28.157  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:46:28.157  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:46:28.325  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:46:28.331  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:46:28.345  1 N - [TrajectoryAuto]待检查数据表：Channels
23:46:28.345  1 N - [TrajectoryAuto] select * from sqlite_master
23:46:28.354  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 4
23:46:28.354  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:46:28.364  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:46:28.364  1 N - [TrajectoryAuto] select * from sqlite_master
23:46:28.375  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 81
23:46:28.375  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:46:28.386  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:46:28.386  1 N - [TrajectoryAuto] select * from sqlite_master
23:46:28.397  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 15,137
23:46:28.398  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:46:28.415  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 15656 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:19:26.5930000
#Date: 2025-08-06
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
23:58:02.546  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
23:58:02.549  1 N - NewLife组件核心库 ©2002-2024 NewLife
23:58:02.550  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
23:58:02.550  1 N - TrajectoryAuto.Web 
23:58:02.550  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
23:58:02.550  1 N - NewLife数据中间件 ©2002-2024 NewLife
23:58:02.550  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
23:58:03.062  1 N - [TrajectoryAuto]待检查数据表：Scenes
23:58:03.079  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
23:58:03.103  1 N - [TrajectoryAuto] select * from sqlite_master
23:58:03.113  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
23:58:03.114  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
23:58:03.169  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
23:58:03.173  1 N - [TrajectoryAuto] Select Count(*) From Scenes
23:58:03.187  1 N - [TrajectoryAuto]待检查数据表：Channels
23:58:03.187  1 N - [TrajectoryAuto] select * from sqlite_master
23:58:03.196  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
23:58:03.197  1 N - [TrajectoryAuto] Select Count(*) From Channels
23:58:03.208  1 N - [TrajectoryAuto]待检查数据表：Trajectories
23:58:03.208  1 N - [TrajectoryAuto] select * from sqlite_master
23:58:03.217  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 94
23:58:03.218  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
23:58:03.229  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
23:58:03.230  1 N - [TrajectoryAuto] select * from sqlite_master
23:58:03.239  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 20,147
23:58:03.240  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
23:58:03.273  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
