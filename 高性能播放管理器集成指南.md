# 高性能播放管理器集成指南

## 🎯 集成目标

将 `PerformantPlaybackManager` 集成到现有的 `TrajectoryPlaybackController` 中，替换原有的无限Task创建模式，实现资源可控的高性能多场景并发播放。

## 📋 集成步骤

### 第1步：更新控制器依赖注入

在 `TrajectoryPlaybackController` 中注入高性能播放管理器：

```csharp
public class TrajectoryPlaybackController : ControllerBase
{
    private readonly ILogger<TrajectoryPlaybackController> _logger;
    private readonly ISceneService _sceneService;
    private readonly IChannelService _channelService;
    private readonly ITrajectoryService _trajectoryService;
    private readonly ICommunicationService _communicationService;
    private readonly IPlaybackStateService _playbackStateService;
    private readonly IPerformantPlaybackManager _performantPlaybackManager; // 新增

    public TrajectoryPlaybackController(
        ILogger<TrajectoryPlaybackController> logger,
        ISceneService sceneService,
        IChannelService channelService,
        ITrajectoryService trajectoryService,
        ICommunicationService communicationService,
        IPlaybackStateService playbackStateService,
        IPerformantPlaybackManager performantPlaybackManager) // 新增
    {
        _logger = logger;
        _sceneService = sceneService;
        _channelService = channelService;
        _trajectoryService = trajectoryService;
        _communicationService = communicationService;
        _playbackStateService = playbackStateService;
        _performantPlaybackManager = performantPlaybackManager; // 新增
    }
}
```

### 第2步：修改播放场景方法

替换 `PlaySceneTrajectories` 方法中的Task创建逻辑：

```csharp
[HttpPost("play-scene/{sceneId}")]
public async Task<IActionResult> PlaySceneTrajectories(int sceneId)
{
    try
    {
        // 检查场景是否已在播放
        if (_playbackStateService.IsScenePlaybackActive(sceneId))
        {
            _logger.LogInformation($"场景 {sceneId} 已在播放中，忽略重复请求");
            return Ok(new { success = true, message = "场景已在播放中" });
        }

        var scene = await GetSceneById(sceneId);
        if (scene == null)
        {
            return NotFound(new { success = false, message = "场景不存在" });
        }

        var channels = await _channelService.GetChannelsBySceneIdAsync(sceneId);
        if (!channels.Any())
        {
            return BadRequest(new { success = false, message = "场景中没有通道" });
        }

        // 启动场景播放状态
        _playbackStateService.StartScenePlayback(sceneId);

        // 使用高性能播放管理器启动场景播放
        await _performantPlaybackManager.StartScenePlaybackAsync(sceneId, channels.ToList());

        _logger.LogInformation($"场景 {sceneId} 开始播放，包含 {channels.Count()} 个通道");

        return Ok(new { 
            success = true, 
            message = $"场景 {sceneId} 播放已启动",
            sceneId = sceneId,
            channelCount = channels.Count()
        });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"播放场景 {sceneId} 时发生错误");
        return StatusCode(500, new { success = false, message = "播放场景时发生错误" });
    }
}
```

### 第3步：修改播放通道方法

替换 `PlayChannelTrajectories` 方法：

```csharp
[HttpPost("play-channel/{channelId}")]
public async Task<IActionResult> PlayChannelTrajectories(int channelId)
{
    try
    {
        // 检查通道是否已在播放
        if (_playbackStateService.IsChannelPlaybackActive(channelId))
        {
            _logger.LogInformation($"通道 {channelId} 已在播放中，忽略重复请求");
            return Ok(new { success = true, message = "通道已在播放中" });
        }

        var channel = await GetChannelById(channelId);
        if (channel == null)
        {
            return NotFound(new { success = false, message = "通道不存在" });
        }

        var trajectories = await _trajectoryService.GetTrajectoriesByChannelIdAsync(channelId);
        if (!trajectories.Any())
        {
            return BadRequest(new { success = false, message = "通道中没有轨迹" });
        }

        // 启动通道播放状态
        _playbackStateService.StartChannelPlayback(channelId, channel.SceneId);

        // 使用高性能播放管理器启动通道播放
        await _performantPlaybackManager.StartChannelPlaybackAsync(channelId, channel.SceneId, trajectories.ToList());

        _logger.LogInformation($"通道 {channelId} 开始播放，包含 {trajectories.Count()} 个轨迹");

        return Ok(new { 
            success = true, 
            message = $"通道 {channelId} 播放已启动",
            channelId = channelId,
            trajectoryCount = trajectories.Count()
        });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"播放通道 {channelId} 时发生错误");
        return StatusCode(500, new { success = false, message = "播放通道时发生错误" });
    }
}
```

### 第4步：移除旧的播放逻辑

删除或注释掉以下方法，因为它们将被高性能播放管理器替代：

- `PlayTrajectoryInternalAsync` - 移动到 `PerformantPlaybackManager`
- 控制器中的Task.Run调用
- 手动的CancellationTokenSource管理

### 第5步：更新停止方法

修改停止方法以使用高性能播放管理器：

```csharp
[HttpPost("stop-all")]
public async Task<IActionResult> StopAllPlayback()
{
    try
    {
        _logger.LogInformation("开始停止所有播放");

        // 使用高性能播放管理器停止所有播放
        await _performantPlaybackManager.StopAllPlaybackAsync();

        // 停止通信服务
        await _communicationService.StopRealTimeCommunicationAsync();

        _logger.LogInformation("所有播放已停止");

        return Ok(new { success = true, message = "所有播放已停止" });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "停止所有播放时发生错误");
        return StatusCode(500, new { success = false, message = "停止播放时发生错误" });
    }
}

[HttpPost("stop-scene/{sceneId}")]
public async Task<IActionResult> StopScenePlayback(int sceneId)
{
    try
    {
        _logger.LogInformation($"停止场景 {sceneId} 播放");

        // 使用高性能播放管理器停止场景播放
        await _performantPlaybackManager.StopScenePlaybackAsync(sceneId);

        _logger.LogInformation($"场景 {sceneId} 播放已停止");

        return Ok(new { success = true, message = $"场景 {sceneId} 播放已停止" });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"停止场景 {sceneId} 播放时发生错误");
        return StatusCode(500, new { success = false, message = "停止场景播放时发生错误" });
    }
}

[HttpPost("stop-channel/{channelId}")]
public async Task<IActionResult> StopChannelPlayback(int channelId)
{
    try
    {
        _logger.LogInformation($"停止通道 {channelId} 播放");

        // 使用高性能播放管理器停止通道播放
        await _performantPlaybackManager.StopChannelPlaybackAsync(channelId);

        _logger.LogInformation($"通道 {channelId} 播放已停止");

        return Ok(new { success = true, message = $"通道 {channelId} 播放已停止" });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"停止通道 {channelId} 播放时发生错误");
        return StatusCode(500, new { success = false, message = "停止通道播放时发生错误" });
    }
}
```

## 🔧 配置说明

### appsettings.json 配置

```json
{
  "PlaybackPerformance": {
    "MaxConcurrentTasks": 50,           // 最大并发轨迹数
    "ChannelCapacity": 10000,           // 队列容量
    "CleanupIntervalMinutes": 5,        // 清理间隔
    "MaxUdpLatencyMs": 100,             // UDP延迟阈值
    "MemoryThresholdMB": 1000,          // 内存阈值
    "EnablePerformanceMonitoring": true, // 启用性能监控
    "PerformanceReportIntervalSeconds": 30 // 性能报告间隔
  }
}
```

### 性能参数调优建议

| 服务器配置 | MaxConcurrentTasks | ChannelCapacity | 说明 |
|------------|-------------------|-----------------|------|
| 4核8GB     | 30                | 5000           | 小型部署 |
| 8核16GB    | 50                | 10000          | 中型部署 |
| 16核32GB   | 100               | 20000          | 大型部署 |

## 📊 集成验证

### 1. 功能验证

```bash
# 启动场景播放
curl -X POST "http://localhost:5000/api/trajectoryplayback/play-scene/1"

# 检查性能统计
curl -X GET "http://localhost:5000/api/playbackperformance/stats"

# 停止场景播放
curl -X POST "http://localhost:5000/api/trajectoryplayback/stop-scene/1"
```

### 2. 性能验证

监控以下指标：
- 内存使用量应该稳定，不会无限增长
- CPU使用率应该可控，不会超过80%
- UDP发送延迟应该低于配置的阈值
- 活跃线程数应该在合理范围内

### 3. 并发验证

```bash
# 同时启动多个场景
for i in {1..5}; do
  curl -X POST "http://localhost:5000/api/trajectoryplayback/play-scene/$i" &
done
wait

# 检查所有场景状态
for i in {1..5}; do
  curl -X GET "http://localhost:5000/api/trajectoryplayback/status/scene/$i"
done
```

## ⚠️ 注意事项

### 1. 向后兼容性

- 所有现有API端点保持不变
- 响应格式保持一致
- 错误处理机制不变

### 2. 渐进式迁移

建议按以下顺序进行迁移：

1. **第一阶段**: 部署高性能播放管理器，但不启用
2. **第二阶段**: 在测试环境启用并验证功能
3. **第三阶段**: 在生产环境小规模测试
4. **第四阶段**: 全面启用并监控性能

### 3. 回滚方案

如果遇到问题，可以通过以下方式快速回滚：

```csharp
// 在控制器中添加开关
private readonly bool _usePerformantManager = 
    _configuration.GetValue("PlaybackPerformance:EnablePerformantManager", false);

// 在播放方法中使用条件逻辑
if (_usePerformantManager)
{
    await _performantPlaybackManager.StartScenePlaybackAsync(sceneId, channels.ToList());
}
else
{
    // 使用原有逻辑
    foreach (var channel in channels)
    {
        _ = Task.Run(async () => await PlayChannelTrajectories(channel.Id));
    }
}
```

## 🚀 部署检查清单

- [ ] 确认所有依赖注入已正确配置
- [ ] 验证appsettings.json配置参数
- [ ] 测试单场景播放功能
- [ ] 测试多场景并发播放
- [ ] 验证停止功能正常工作
- [ ] 检查性能监控API响应
- [ ] 确认内存和CPU使用正常
- [ ] 验证UDP发送延迟在预期范围内
- [ ] 测试异常情况下的资源清理
- [ ] 确认日志记录完整且有用

通过这个集成指南，可以安全、稳定地将高性能播放管理器集成到现有系统中，显著提升多场景并发播放的性能和稳定性。
