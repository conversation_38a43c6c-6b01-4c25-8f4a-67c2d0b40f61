/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 9999; /* 提高z-index确保在最上层 */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
    overflow: auto;
}

/* 显示模态框时的样式 */
.modal.show {
    display: flex !important;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 80%;
    max-width: 600px;
    animation: modalFadeIn 0.3s ease;
    position: relative;
}

.scene-modal {
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    color: #2f3542;
    display: flex;
    align-items: center;
}

.modal-header h3 i {
    color: var(--tech-primary);
    margin-right: 8px;
}


.modal-footer { 
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-radius: 0 0 8px 8px;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close:hover {
    color: var(--tech-primary);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表单样式 - 确保与style.css一致 */
.scene-form {
    --input-height: 2rem;
    --input-padding: 0.3rem 0.6rem;
    --input-radius: 4px;
    --input-border: 1px solid #e2e8f0;
    --input-focus-border: #4a6cf7;
    --input-focus-shadow: 0 0 0 2px rgba(74, 108, 247, 0.15);
    --label-color: #4a5568;
    --label-margin: 0 0.5rem 0 0;
    --form-group-margin: 0 0 0.6rem 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 0.9rem;
}

.scene-form .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    padding: 0;
    align-items: flex-start;
    flex-wrap: wrap;
}

.scene-form .form-group {
    margin: var(--form-group-margin);
    position: relative;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    flex: 1;
    min-width: 160px;
}

.scene-form label {
    margin: var(--label-margin);
    font-weight: 500;
    font-size: 0.85rem;
    color: var(--label-color);
    transition: all 0.15s ease;
    position: relative;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 60px;
    text-align: right;
}

.scene-form input[type="text"],
.scene-form input[type="number"],
.scene-form input[type="file"],
.scene-form .form-control {
    width: 100%;
    min-width: 100px;
    height: var(--input-height);
    padding: var(--input-padding);
    border: var(--input-border);
    border-radius: var(--input-radius);
    font-size: 0.85rem;
    color: #2d3748;
    background-color: #fff;
    transition: all 0.15s ease;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    background-clip: padding-box;
    appearance: none;
    -webkit-appearance: none;
    line-height: 1.2;
    flex: 1;
    box-sizing: border-box;
}

.scene-form .form-control:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    outline: none;
}

/* 复选框样式 */
.scene-form .form-check {
    display: flex;
    align-items: center;
    height: var(--input-height);
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    width: auto;
}

.scene-form .form-check-input {
    margin: 0 0.75rem 0 0;
    width: 1.2rem;
    height: 1.2rem;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
    appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    background-color: #fff;
}

.scene-form .form-check-input:checked {
    background-color: var(--input-focus-border);
    border-color: var(--input-focus-border);
}

.scene-form .form-check-label {
    font-size: 0.85rem;
    cursor: pointer;
}

/* 背景图片预览区域样式 */
.background-image-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.background-image-preview {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    padding: 10px;
    margin-top: 5px;
    border: 1px dashed var(--border-color);
}

.preview-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

#backgroundImageName {
    font-size: 0.85rem;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

#viewBackgroundBtn {
    white-space: nowrap;
    font-size: 0.8rem;
    padding: 3px 8px;
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
    transition: all 0.2s ease;
}

#viewBackgroundBtn:hover {
    background-color: var(--accent-color-hover);
    border-color: var(--accent-color-hover);
}

                    /* 与场景表单样式保持一致 */
                    #channelForm {
                        --input-height: 2rem;
                        --input-padding: 0.3rem 0.6rem;
                        --input-radius: 4px;
                        --input-border: 1px solid #e2e8f0;
                        --input-focus-border: #4a6cf7;
                        --input-focus-shadow: 0 0 0 2px rgba(74, 108, 247, 0.15);
                        --label-color: #4a5568;
                        --label-margin: 0 0.5rem 0 0;
                        --form-group-margin: 0 0 0.6rem 0;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                        font-size: 0.9rem;
                    }
                    
                    .channel-form-row {
                        display: flex;
                        gap: 1rem;
                        margin-bottom: 0.5rem;
                        padding: 0;
                        align-items: flex-start;
                        flex-wrap: wrap;
                    }
                    
                    .channel-form-group {
                        margin: var(--form-group-margin);
                        position: relative;
                        transition: all 0.15s ease;
                        display: flex;
                        align-items: center;
                        gap: 0.3rem;
                        flex: 1;
                        min-width: 160px;
                    }
                    
                    .channel-form-group label {
                        margin: var(--label-margin);
                        font-weight: 500;
                        font-size: 0.85rem;
                        color: var(--label-color);
                        transition: all 0.15s ease;
                        position: relative;
                        white-space: nowrap;
                        flex-shrink: 0;
                        min-width: 60px;
                        text-align: right;
                    }
                    
                    .channel-form-group input[type="text"],
                    .channel-form-group input[type="number"],
                    .channel-form-group .form-control {
                        width: 100%;
                        min-width: 100px;
                        height: var(--input-height);
                        padding: var(--input-padding);
                        border: var(--input-border);
                        border-radius: var(--input-radius);
                        font-size: 0.85rem;
                        color: #2d3748;
                        background-color: #fff;
                        transition: all 0.15s ease;
                        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
                        background-clip: padding-box;
                        appearance: none;
                        -webkit-appearance: none;
                        line-height: 1.2;
                        flex: 1;
                        box-sizing: border-box;
                    }
                    
                    .channel-form-group input[type="color"] {
                        width: 60px;
                        height: var(--input-height);
                        padding: 2px;
                        border: var(--input-border);
                        border-radius: var(--input-radius);
                        background-color: #fff;
                        cursor: pointer;
                    }
                    
                    .channel-form-group .form-control:focus,
                    .channel-form-group input:focus {
                        border-color: var(--input-focus-border);
                        box-shadow: var(--input-focus-shadow);
                        outline: none;
                    }
                    
                    .channel-form-group .form-check {
                        display: flex;
                        align-items: center;
                        height: var(--input-height);
                        gap: 0.5rem;
                        margin: 0;
                        padding: 0;
                        width: auto;
                    }
                    
                    .channel-form-group .form-check-input {
                        margin: 0 0.75rem 0 0;
                        width: 1.2rem;
                        height: 1.2rem;
                        border: 1px solid #cbd5e0;
                        border-radius: 4px;
                        appearance: none;
                        -webkit-appearance: none;
                        cursor: pointer;
                        position: relative;
                        transition: all 0.2s ease;
                        background-color: #fff;
                    }
                    
                    .channel-form-group .form-check-input:checked {
                        background-color: var(--input-focus-border);
                        border-color: var(--input-focus-border);
                    }
                    
                    .channel-form-group .form-check-input:checked::after {
                        content: '\2713'; /* ✓ 符号 */
                        position: absolute;
                        color: white;
                        font-size: 0.8rem;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        line-height: 1;
                    }
                    
                    .z-axis-range {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                    }
                    
                    .z-axis-range input {
                        width: 80px;
                        height: var(--input-height);
                        padding: var(--input-padding);
                        border: var(--input-border);
                        border-radius: var(--input-radius);
                        font-size: 0.85rem;
                    } 