using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using TrajectoryAuto.Core.Models;
using TrajectoryAuto.Infrastructure.Services;

namespace TrajectoryAuto.Web.Controllers
{
    /// <summary>
    /// 简单UDP控制器
    /// 提供简单的UDP通信功能，不使用复杂的连接池和接口
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SimpleUdpController : ControllerBase
    {
        private readonly ILogger<SimpleUdpController> _logger;
        private readonly SimpleUdpManager _udpManager;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="udpManager">简单UDP管理器</param>
        public SimpleUdpController(
            ILogger<SimpleUdpController> logger,
            SimpleUdpManager udpManager)
        {
            _logger = logger;
            _udpManager = udpManager;
        }

        /// <summary>
        /// 发送坐标数据
        /// </summary>
        /// <param name="request">发送请求</param>
        /// <returns>发送结果</returns>
        [HttpPost("send")]
        public async Task<IActionResult> SendCoordinateData([FromBody] SendCoordinateRequest request)
        {
            if (request == null)
            {
                return BadRequest("请求参数不能为空");
            }

            try
            {
                _logger.LogInformation($"发送坐标数据: 场景={request.SceneId}, 通道={request.ChannelId}, 轨迹={request.TrajectoryId}, IP={request.ServerIp}, 端口={request.ServerPort}");

                // 创建坐标数据
                var coordinateData = new CoordinateData
                {
                    ChannelNumber = request.ChannelNumber,
                    X = request.X,
                    Y = request.Y,
                    Z = request.Z,
                    Timestamp = DateTime.Now,
                    ChannelAddress = request.ChannelAddress
                };

                // 发送坐标数据
                bool result = await _udpManager.SendCoordinateDataAsync(
                    request.ServerIp,
                    request.ServerPort,
                    coordinateData);

                if (result)
                {
                    _logger.LogInformation("发送坐标数据成功");
                    return Ok(new { success = true, message = "发送坐标数据成功" });
                }
                else
                {
                    _logger.LogWarning("发送坐标数据失败");
                    return StatusCode(500, new { success = false, message = "发送坐标数据失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送坐标数据时出错");
                return StatusCode(500, new { success = false, message = $"发送坐标数据时出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 停止场景UDP发送
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <returns>停止结果</returns>
        [HttpPost("stop-scene/{sceneId}")]
        public IActionResult StopSceneUdp(Guid sceneId)
        {
            try
            {
                _logger.LogInformation($"停止场景UDP发送: {sceneId}");
                _udpManager.StopSceneClient(sceneId);
                return Ok(new { success = true, message = $"已停止场景 {sceneId} 的UDP发送" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止场景UDP发送时出错: {sceneId}");
                return StatusCode(500, new { success = false, message = $"停止场景UDP发送时出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 停止通道UDP发送
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <returns>停止结果</returns>
        [HttpPost("stop-channel/{channelId}")]
        public IActionResult StopChannelUdp(Guid channelId)
        {
            try
            {
                _logger.LogInformation($"停止通道UDP发送: {channelId}");
                _udpManager.StopChannelClient(channelId);
                return Ok(new { success = true, message = $"已停止通道 {channelId} 的UDP发送" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止通道UDP发送时出错: {channelId}");
                return StatusCode(500, new { success = false, message = $"停止通道UDP发送时出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 停止轨迹UDP发送
        /// </summary>
        /// <param name="trajectoryId">轨迹ID</param>
        /// <returns>停止结果</returns>
        [HttpPost("stop-trajectory/{trajectoryId}")]
        public IActionResult StopTrajectoryUdp(Guid trajectoryId)
        {
            try
            {
                _logger.LogInformation($"停止轨迹UDP发送: {trajectoryId}");
                _udpManager.StopTrajectoryUdp(trajectoryId);
                return Ok(new { success = true, message = $"已停止轨迹 {trajectoryId} 的UDP发送" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止轨迹UDP发送时出错: {trajectoryId}");
                return StatusCode(500, new { success = false, message = $"停止轨迹UDP发送时出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 停止所有UDP发送
        /// </summary>
        /// <returns>停止结果</returns>
        [HttpPost("stop-all")]
        public IActionResult StopAllUdp()
        {
            try
            {
                _logger.LogInformation("停止所有UDP发送");
                _udpManager.StopAllClients();
                return Ok(new { success = true, message = "已停止所有UDP发送" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止所有UDP发送时出错");
                return StatusCode(500, new { success = false, message = $"停止所有UDP发送时出错: {ex.Message}" });
            }
        }

    }
    /// <summary>
    /// 发送坐标请求
    /// </summary>
    public class SendCoordinateRequest
    {
        /// <summary>
        /// 场景ID
        /// </summary>
        public Guid SceneId { get; set; }

        /// <summary>
        /// 通道ID
        /// </summary>
        public Guid ChannelId { get; set; }

        /// <summary>
        /// 轨迹ID（可选）
        /// </summary>
        public Guid? TrajectoryId { get; set; }

        /// <summary>
        /// 服务器IP
        /// </summary>
        public string ServerIp { get; set; }

        /// <summary>
        /// 服务器端口
        /// </summary>
        public int ServerPort { get; set; }

        /// <summary>
        /// 通道号
        /// </summary>
        public int ChannelNumber { get; set; }

        /// <summary>
        /// 通道地址
        /// </summary>
        public string ChannelAddress { get; set; }

        /// <summary>
        /// X坐标
        /// </summary>
        public double X { get; set; }

        /// <summary>
        /// Y坐标
        /// </summary>
        public double Y { get; set; }

        /// <summary>
        /// Z坐标
        /// </summary>
        public double Z { get; set; }
    }
}