# UDP连接池管理系统使用指南

## 🎯 系统概述

UDP连接池管理系统是为多场景轨迹播放系统设计的高性能网络连接管理解决方案。它按照**场景 → 通道 → 轨迹**的层级结构管理UDP连接，实现连接复用、自动清理和性能优化。

## 🏗️ 架构设计

### 分层连接管理

```
场景级别连接 (Scene Connection)
├── 通道级别连接 (Channel Connection)
│   ├── 轨迹级别连接 (Trajectory Connection)
│   ├── 轨迹级别连接 (Trajectory Connection)
│   └── ...
├── 通道级别连接 (Channel Connection)
└── ...
```

### 核心组件

1. **IUdpConnectionPoolManager**: 连接池管理器接口
2. **UdpConnectionPoolManager**: 连接池管理器实现
3. **IEnhancedCommunicationService**: 增强通信服务接口
4. **EnhancedCommunicationService**: 增强通信服务实现
5. **UdpConnectionPoolController**: 连接池管理API控制器

## 🚀 核心特性

### 1. 分层连接管理
- **场景级别**: 一个场景一个连接，所有轨迹共享
- **通道级别**: 一个通道一个连接，通道内轨迹共享
- **轨迹级别**: 每个轨迹独立连接，最高性能隔离

### 2. 智能连接复用
```csharp
// 自动复用现有连接
var connection = await _connectionPoolManager.GetChannelConnectionAsync(channelId, sceneId, targetEndpoint);
// 引用计数自动管理
connection.ReferenceCount++; // 获取时增加
connection.ReferenceCount--; // 释放时减少
```

### 3. 自动资源清理
- **定时清理**: 每5分钟自动清理空闲连接
- **手动清理**: 支持API手动触发清理
- **智能清理**: 基于引用计数和空闲时间

### 4. 性能监控
- **实时统计**: 连接数量、使用率、延迟等
- **健康评分**: 综合评估连接池状态
- **优化建议**: 自动生成性能优化建议

## 📋 使用方式

### 1. 基础配置

#### appsettings.json 配置
```json
{
  "UdpConnectionPool": {
    "IdleTimeoutMinutes": 10,           // 空闲超时时间
    "MaxConnectionsPerType": 100,       // 每种类型最大连接数
    "AutoCleanupIntervalMinutes": 5,    // 自动清理间隔
    "EnableConnectionReuse": true,      // 启用连接复用
    "ConnectionHealthCheckIntervalMinutes": 2, // 健康检查间隔
    "MaxConnectionAge": 60,             // 最大连接年龄（分钟）
    "PreferredConnectionType": "Channel" // 首选连接类型
  }
}
```

### 2. 服务注册（已在Program.cs中配置）

```csharp
// UDP连接池管理器（单例）
builder.Services.AddSingleton<IUdpConnectionPoolManager, UdpConnectionPoolManager>();

// 增强通信服务（作用域）
builder.Services.AddScoped<IEnhancedCommunicationService, EnhancedCommunicationService>();
```

### 3. 代码使用示例

#### A. 使用场景级别连接发送数据
```csharp
public class TrajectoryPlaybackService
{
    private readonly IEnhancedCommunicationService _communicationService;

    public async Task PlaySceneTrajectories(int sceneId, List<Trajectory> trajectories)
    {
        foreach (var trajectory in trajectories)
        {
            foreach (var point in trajectory.Points)
            {
                // 使用场景级别连接发送数据
                await _communicationService.SendCoordinateDataWithSceneConnectionAsync(
                    sceneId, "192.168.1.100", 8080, point.CoordinateData);
            }
        }
    }
}
```

#### B. 使用通道级别连接发送数据
```csharp
public async Task PlayChannelTrajectories(int channelId, int sceneId, List<Trajectory> trajectories)
{
    foreach (var trajectory in trajectories)
    {
        foreach (var point in trajectory.Points)
        {
            // 使用通道级别连接发送数据
            await _communicationService.SendCoordinateDataWithChannelConnectionAsync(
                channelId, sceneId, "192.168.1.100", 8080, point.CoordinateData);
        }
    }
}
```

#### C. 使用轨迹级别连接发送数据
```csharp
public async Task PlaySingleTrajectory(int trajectoryId, int channelId, int sceneId, Trajectory trajectory)
{
    foreach (var point in trajectory.Points)
    {
        // 使用轨迹级别连接发送数据
        await _communicationService.SendCoordinateDataWithTrajectoryConnectionAsync(
            trajectoryId, channelId, sceneId, "192.168.1.100", 8080, point.CoordinateData);
    }
}
```

#### D. 批量发送数据
```csharp
public async Task BatchSendData(int channelId, int sceneId, List<CoordinateData> dataList)
{
    // 使用通道级别连接批量发送
    var successCount = await _communicationService.SendBatchCoordinateDataWithChannelConnectionAsync(
        channelId, sceneId, "192.168.1.100", 8080, dataList);
    
    Console.WriteLine($"成功发送 {successCount}/{dataList.Count} 个数据包");
}
```

## 🔧 管理API

### 1. 获取连接池统计
```http
GET /api/udpconnectionpool/stats
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "sceneConnectionCount": 3,
    "channelConnectionCount": 8,
    "trajectoryConnectionCount": 25,
    "totalConnectionCount": 36,
    "activeConnectionCount": 20,
    "idleConnectionCount": 16,
    "poolEfficiency": 2.5,
    "averageConnectionAgeMinutes": 15.3
  }
}
```

### 2. 获取连接池健康状态
```http
GET /api/udpconnectionpool/status
```

### 3. 手动清理空闲连接
```http
POST /api/udpconnectionpool/cleanup?idleTimeoutMinutes=5
```

### 4. 释放特定资源的连接
```http
# 释放场景连接
POST /api/udpconnectionpool/release-scene/1

# 释放通道连接
POST /api/udpconnectionpool/release-channel/5

# 释放轨迹连接
POST /api/udpconnectionpool/release-trajectory/100
```

### 5. 获取连接使用建议
```http
GET /api/udpconnectionpool/recommendation?sceneId=1&channelCount=5&trajectoryCount=20
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "recommendedConnectionType": "Channel",
    "reason": "中等规模部署，推荐使用通道级别连接平衡性能和资源使用",
    "performanceImprovement": "通道内轨迹共享连接，减少连接数量，提高资源利用率",
    "resourceUsage": {
      "expectedConnectionCount": 5,
      "expectedMemoryUsageMB": 2.5,
      "expectedBandwidthMbps": 2.5,
      "connectionReuseRate": 4.0
    }
  }
}
```

## 📊 性能优化建议

### 1. 连接类型选择指南

| 场景规模 | 推荐连接类型 | 适用条件 | 优势 | 劣势 |
|----------|-------------|----------|------|------|
| 小规模 | Trajectory | ≤10轨迹, ≤3通道 | 最低延迟，完全隔离 | 资源消耗高 |
| 中等规模 | Channel | ≤50轨迹, ≤10通道 | 平衡性能和资源 | 通道内共享带宽 |
| 大规模 | Scene | >50轨迹, >10通道 | 最低资源消耗 | 可能有延迟竞争 |

### 2. 配置优化建议

#### 小型部署（1-3个场景）
```json
{
  "UdpConnectionPool": {
    "IdleTimeoutMinutes": 5,
    "MaxConnectionsPerType": 50,
    "AutoCleanupIntervalMinutes": 3,
    "PreferredConnectionType": "Trajectory"
  }
}
```

#### 中型部署（4-10个场景）
```json
{
  "UdpConnectionPool": {
    "IdleTimeoutMinutes": 10,
    "MaxConnectionsPerType": 100,
    "AutoCleanupIntervalMinutes": 5,
    "PreferredConnectionType": "Channel"
  }
}
```

#### 大型部署（>10个场景）
```json
{
  "UdpConnectionPool": {
    "IdleTimeoutMinutes": 15,
    "MaxConnectionsPerType": 200,
    "AutoCleanupIntervalMinutes": 8,
    "PreferredConnectionType": "Scene"
  }
}
```

### 3. 监控指标

#### 关键性能指标（KPI）
- **连接池效率**: >2.0 为良好，>5.0 为优秀
- **活跃连接比例**: >60% 为健康
- **平均连接年龄**: <30分钟为正常
- **清理频率**: 每小时1-2次为正常

#### 告警阈值
```json
{
  "alerts": {
    "maxTotalConnections": 500,
    "minPoolEfficiency": 1.0,
    "maxIdleConnections": 100,
    "maxConnectionAgeMinutes": 120
  }
}
```

## 🔍 故障排除

### 常见问题

#### 1. 连接数量过多
**症状**: 总连接数超过500
**原因**: 清理不及时或连接泄漏
**解决方案**:
```bash
# 立即清理空闲连接
curl -X POST "http://localhost:5000/api/udpconnectionpool/cleanup?idleTimeoutMinutes=1"

# 检查清理效果
curl -X GET "http://localhost:5000/api/udpconnectionpool/stats"
```

#### 2. 连接池效率低
**症状**: PoolEfficiency < 1.0
**原因**: 连接复用率低，可能使用了过细粒度的连接类型
**解决方案**:
- 考虑使用更高层级的连接类型（Channel或Scene）
- 检查连接使用模式，避免频繁创建新连接

#### 3. 内存使用持续增长
**症状**: 内存使用量不断上升
**原因**: 连接没有正确释放
**解决方案**:
```csharp
// 确保在播放结束后释放连接
try
{
    // 播放逻辑
}
finally
{
    await _communicationService.ReleaseChannelConnectionsAsync(channelId);
}
```

### 调试工具

#### 1. 连接池状态检查
```bash
# 获取详细状态
curl -X GET "http://localhost:5000/api/udpconnectionpool/status" | jq

# 获取性能报告
curl -X GET "http://localhost:5000/api/udpconnectionpool/performance-report" | jq
```

#### 2. 日志分析
```csharp
// 启用详细日志
"Logging": {
  "LogLevel": {
    "TrajectoryAuto.Infrastructure.Services.UdpConnectionPoolManager": "Debug",
    "TrajectoryAuto.Infrastructure.Services.EnhancedCommunicationService": "Debug"
  }
}
```

## 🚀 最佳实践

### 1. 连接生命周期管理
```csharp
public class TrajectoryPlaybackManager
{
    public async Task PlayTrajectoryAsync(int trajectoryId, int channelId, int sceneId)
    {
        try
        {
            // 获取连接（自动管理引用计数）
            var connection = await _connectionPool.GetTrajectoryConnectionAsync(
                trajectoryId, channelId, sceneId, targetEndpoint);
            
            // 使用连接发送数据
            await SendTrajectoryData(connection, trajectoryData);
        }
        finally
        {
            // 释放连接（减少引用计数）
            await _connectionPool.ReleaseTrajectoryConnectionAsync(trajectoryId);
        }
    }
}
```

### 2. 批量操作优化
```csharp
// 推荐：批量发送
var dataList = trajectories.SelectMany(t => t.Points.Select(p => p.CoordinateData)).ToList();
await _communicationService.SendBatchCoordinateDataWithChannelConnectionAsync(
    channelId, sceneId, serverIp, serverPort, dataList);

// 避免：逐个发送
foreach (var data in dataList)
{
    await _communicationService.SendCoordinateDataWithChannelConnectionAsync(
        channelId, sceneId, serverIp, serverPort, data); // 效率低
}
```

### 3. 错误处理
```csharp
public async Task<bool> SafeSendData(int channelId, int sceneId, CoordinateData data)
{
    try
    {
        return await _communicationService.SendCoordinateDataWithChannelConnectionAsync(
            channelId, sceneId, serverIp, serverPort, data);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "发送数据失败: Channel={ChannelId}, Scene={SceneId}", channelId, sceneId);
        
        // 可选：释放可能有问题的连接
        await _communicationService.ReleaseChannelConnectionsAsync(channelId);
        
        return false;
    }
}
```

## 📈 性能基准

### 预期性能指标

| 连接类型 | 并发轨迹 | 内存使用 | 延迟 | 吞吐量 |
|----------|----------|----------|------|--------|
| Trajectory | 1-10 | 5-50MB | <10ms | 高 |
| Channel | 10-50 | 10-100MB | <20ms | 中高 |
| Scene | 50-200 | 20-200MB | <50ms | 中 |

### 压力测试建议

```bash
# 测试脚本示例
for i in {1..10}; do
  curl -X POST "http://localhost:5000/api/trajectoryplayback/play-scene/$i" &
done
wait

# 监控性能
curl -X GET "http://localhost:5000/api/udpconnectionpool/performance-report"
```

通过这个UDP连接池管理系统，您可以显著提升多场景轨迹播放的网络性能，减少资源消耗，并获得更好的系统稳定性和可维护性。
