using System;
using System.Collections.Generic;

namespace TrajectoryAuto.Core.Interfaces;

/// <summary>
/// 播放状态服务接口 - 管理多场景、多通道的播放状态
/// </summary>
public interface IPlaybackStateService
{
    #region 全局播放状态管理
    
    /// <summary>
    /// 设置全局停止标志
    /// </summary>
    void SetGlobalStop();
    
    /// <summary>
    /// 重置全局停止标志，允许新的播放开始
    /// </summary>
    void ResetGlobalStop();
    
    /// <summary>
    /// 检查是否已请求全局停止
    /// </summary>
    /// <returns>如果已请求停止返回true</returns>
    bool IsGlobalStopRequested();
    
    #endregion
    
    #region 场景级别播放状态管理
    
    /// <summary>
    /// 开始场景播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    void StartScenePlayback(Guid sceneId);
    
    /// <summary>
    /// 停止场景播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    void StopScenePlayback(Guid sceneId);
    
    /// <summary>
    /// 检查场景是否正在播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>如果场景正在播放返回true</returns>
    bool IsScenePlaybackActive(Guid sceneId);
    
    /// <summary>
    /// 检查场景是否已请求停止
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>如果场景已请求停止返回true</returns>
    bool IsSceneStopRequested(Guid sceneId);
    
    /// <summary>
    /// 获取所有正在播放的场景ID
    /// </summary>
    /// <returns>正在播放的场景ID列表</returns>
    IReadOnlyList<Guid> GetActiveScenes();
    
    #endregion
    
    #region 通道级别播放状态管理
    
    /// <summary>
    /// 开始通道播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">所属场景ID</param>
    void StartChannelPlayback(Guid channelId, Guid sceneId);
    
    /// <summary>
    /// 停止通道播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    void StopChannelPlayback(Guid channelId);
    
    /// <summary>
    /// 检查通道是否正在播放
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <returns>如果通道正在播放返回true</returns>
    bool IsChannelPlaybackActive(Guid channelId);
    
    /// <summary>
    /// 检查通道是否已请求停止
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <returns>如果通道已请求停止返回true</returns>
    bool IsChannelStopRequested(Guid channelId);
    
    /// <summary>
    /// 获取场景下所有正在播放的通道ID
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    /// <returns>正在播放的通道ID列表</returns>
    IReadOnlyList<Guid> GetActiveChannelsInScene(Guid sceneId);
    
    /// <summary>
    /// 停止场景下所有通道的播放
    /// </summary>
    /// <param name="sceneId">场景ID</param>
    void StopAllChannelsInScene(Guid sceneId);
    
    #endregion
    
    #region 轨迹级别播放状态管理
    
    /// <summary>
    /// 检查轨迹是否应该停止（综合检查全局、场景、通道停止状态）
    /// </summary>
    /// <param name="trajectoryId">轨迹ID</param>
    /// <param name="channelId">通道ID</param>
    /// <param name="sceneId">场景ID</param>
    /// <returns>如果轨迹应该停止返回true</returns>
    bool ShouldTrajectoryStop(Guid trajectoryId, Guid channelId, Guid sceneId);
    
    #endregion
}
