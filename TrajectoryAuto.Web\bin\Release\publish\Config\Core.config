<?xml version="1.0" encoding="utf-8"?>
<Core>
  <!--全局调试。XTrace.Debug-->
  <Debug>true</Debug>
  <!--日志等级。只输出大于等于该级别的日志，All/Debug/Info/Warn/Error/Fatal，默认Info-->
  <LogLevel>Info</LogLevel>
  <!--文件日志目录。默认Log子目录-->
  <LogPath>Log</LogPath>
  <!--日志文件上限。超过上限后拆分新日志文件，默认10MB，0表示不限制大小-->
  <LogFileMaxBytes>10</LogFileMaxBytes>
  <!--日志文件备份。超过备份数后，最旧的文件将被删除，网络安全法要求至少保存6个月日志，默认200，0表示不限制个数-->
  <LogFileBackups>200</LogFileBackups>
  <!--日志文件格式。默认{0:yyyy_MM_dd}.log，支持日志等级如 {1}_{0:yyyy_MM_dd}.log-->
  <LogFileFormat>{0:yyyy_MM_dd}.log</LogFileFormat>
  <!--网络日志。本地子网日志广播udp://255.255.255.255:514，或者http://xxx:80/log-->
  <NetworkLog></NetworkLog>
  <!--日志记录时间UTC校正，小时-->
  <UtcIntervalHours>0</UtcIntervalHours>
  <!--数据目录。本地数据库目录，默认Data子目录-->
  <DataPath>Data</DataPath>
  <!--备份目录。备份数据库时存放的目录，默认Backup子目录-->
  <BackupPath>Backup</BackupPath>
  <!--插件目录-->
  <PluginPath>Plugins</PluginPath>
  <!--插件服务器。将从该网页上根据关键字分析链接并下载插件-->
  <PluginServer>http://x.newlifex.com/</PluginServer>
  <!--辅助解析程序集。程序集加载过程中，被依赖程序集未能解析时，是否协助解析，默认false-->
  <AssemblyResolve>false</AssemblyResolve>
  <!--服务地址。用户访问的外网地址，反向代理之外，用于内部构造其它Url（如SSO），或者向注册中心登记，多地址逗号隔开-->
  <ServiceAddress></ServiceAddress>
</Core>