using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Core.Interfaces;

/// <summary>
/// 通信服务接口
/// </summary>
public interface ICommunicationService
{
    /// <summary>
    /// 发送坐标数据到指定服务器
    /// </summary>
    /// <param name="serverIp">服务器IP地址</param>
    /// <param name="serverPort">服务器端口</param>
    /// <param name="coordinateData">坐标数据</param>
    /// <returns></returns>
    Task<bool> SendCoordinateDataAsync(string serverIp, int serverPort, CoordinateData coordinateData);
    
    /// <summary>
    /// 批量发送坐标数据
    /// </summary>
    /// <param name="serverIp">服务器IP地址</param>
    /// <param name="serverPort">服务器端口</param>
    /// <param name="coordinateDataList">坐标数据列表</param>
    /// <returns></returns>
    Task<bool> SendBatchCoordinateDataAsync(string serverIp, int serverPort, List<CoordinateData> coordinateDataList);
    
    /// <summary>
    /// 测试服务器连接
    /// </summary>
    /// <param name="serverIp">服务器IP地址</param>
    /// <param name="serverPort">服务器端口</param>
    /// <returns></returns>
    Task<bool> TestConnectionAsync(string serverIp, int serverPort);
    
    /// <summary>
    /// 启动实时通信
    /// </summary>
    /// <param name="serverIp">服务器IP地址</param>
    /// <param name="serverPort">服务器端口</param>
    /// <param name="sceneId">场景ID（可选）</param>
    /// <param name="channelId">通道ID（可选）</param>
    /// <returns></returns>
    Task<bool> StartRealTimeCommunicationAsync(string serverIp, int serverPort, int sceneId = 0, int channelId = 0);
    
    /// <summary>
    /// 停止实时通信
    /// </summary>
    /// <returns></returns>
    Task<bool> StopRealTimeCommunicationAsync();
    
    /// <summary>
    /// 检查实时通信状态
    /// </summary>
    /// <returns>如果实时通信已启动返回true，否则返回false</returns>
    bool IsRealTimeCommunicationStarted();
    
    /// <summary>
    /// 检查是否允许发送数据
    /// </summary>
    /// <returns>如果允许发送数据返回true，否则返回false</returns>
    bool IsAllowedToSend();
}