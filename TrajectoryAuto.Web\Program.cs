using Microsoft.Extensions.Logging;
using NewLife.Log;
using XCode.DataAccessLayer;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Infrastructure.Services;
using TrajectoryAuto.Web.Hubs;
using TrajectoryAuto.Web.Middleware;
using Microsoft.AspNetCore.RateLimiting;

var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllersWithViews();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 添加防伪令牌和安全配置
builder.Services.AddAntiforgery(options =>
{
    options.HeaderName = "X-CSRF-TOKEN";
    options.SuppressXFrameOptionsHeader = false;
});

// 添加会话支持（为后期登录功能准备）
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
});

// 添加数据保护（为后期登录功能准备）
builder.Services.AddDataProtection();

// 配置NewLife.XCode数据库连接 - 针对音频灯光联动优化
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") ?? "Data Source=TrajectoryAuto.db";
DAL.AddConnStr("TrajectoryAuto", connectionString, null, "sqlite");

// 配置XCode性能优化参数
XTrace.UseConsole();

// 注册优化后的服务 - 使用NewLife.XCode高性能实现
builder.Services.AddScoped<ISceneService, XCodeSceneService>();
builder.Services.AddScoped<IChannelService, XCodeChannelService>();
builder.Services.AddScoped<ITrajectoryService, XCodeTrajectoryService>();
builder.Services.AddScoped<ICommunicationService, CommunicationService>();

// 注册UDP连接池管理器为单例，支持连接复用和自动清理
builder.Services.AddSingleton<IUdpConnectionPoolManager>(provider =>
{
    var logger = provider.GetRequiredService<ILogger<UdpConnectionPoolManager>>();
    var configuration = provider.GetRequiredService<IConfiguration>();
    
    var idleTimeoutMinutes = configuration.GetValue("UdpConnectionPool:IdleTimeoutMinutes", 10);
    var maxConnectionsPerType = configuration.GetValue("UdpConnectionPool:MaxConnectionsPerType", 100);
    
    return new UdpConnectionPoolManager(logger, idleTimeoutMinutes, maxConnectionsPerType);
});

// 注册直接UDP服务
builder.Services.AddScoped<DirectUdpService>();

// 注册增强通信服务
builder.Services.AddScoped<IEnhancedCommunicationService, EnhancedCommunicationService>();

// 注册简单UDP管理器
builder.Services.AddSingleton<SimpleUdpManager>();


// 注册全局播放状态服务为单例，确保所有请求共享同一状态
builder.Services.AddSingleton<IPlaybackStateService, PlaybackStateService>();

// 注册高性能播放管理器为单例，支持多场景并发播放优化
builder.Services.AddSingleton<IPerformantPlaybackManager>(provider =>
{
    var logger = provider.GetRequiredService<ILogger<PerformantPlaybackManager>>();
    var communicationService = provider.GetRequiredService<ICommunicationService>();
    var playbackStateService = provider.GetRequiredService<IPlaybackStateService>();
    
    // 从配置读取性能参数，如果没有配置则使用默认值
    var configuration = provider.GetRequiredService<IConfiguration>();
    var maxConcurrentTasks = configuration.GetValue("PlaybackPerformance:MaxConcurrentTasks", 50);
    var channelCapacity = configuration.GetValue("PlaybackPerformance:ChannelCapacity", 10000);
    
    return new PerformantPlaybackManager(
        logger,
        communicationService,
        playbackStateService,
        maxConcurrentTasks,
        channelCapacity
    );
});

// 添加SignalR - 音频灯光联动实时通信
builder.Services.AddSignalR(options =>
{
    options.MaximumReceiveMessageSize = 102400; // 增加最大消息大小为100KB
    options.StreamBufferCapacity = 20; // 增加流缓冲区容量
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(60); // 增加客户端超时时间为60秒
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);     // 增加心跳间隔为30秒
    options.HandshakeTimeout = TimeSpan.FromSeconds(30);      // 增加握手超时时间为30秒
    options.EnableDetailedErrors = true;                     // 启用详细错误信息
})
.AddJsonProtocol(options => {
    // 使用小写开头属性名，与前端保持一致
    options.PayloadSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    // 属性名大小写不敏感
    options.PayloadSerializerOptions.PropertyNameCaseInsensitive = true;
    options.PayloadSerializerOptions.WriteIndented = false;
});

// 添加限流配置
builder.Services.AddRateLimiter(options =>
{
    options.AddFixedWindowLimiter("ApiPolicy", opts =>
    {
        opts.PermitLimit = 100;
        opts.Window = TimeSpan.FromMinutes(1);
        opts.QueueLimit = 2;
    });
});

// 添加CORS - 安全配置，仅允许特定域名
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins", policy =>
    {
        policy.WithOrigins("https://*:5001", "http://*:85")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

var app = builder.Build();

// 配置HTTP请求管道
app.UseMiddleware<ErrorHandlingMiddleware>();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 添加安全头
app.Use(async (context, next) =>
{
    context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Append("X-Frame-Options", "DENY");
    context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");
    await next();
});

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRateLimiter();
app.UseCors("AllowSpecificOrigins");

// 添加会话中间件
app.UseSession();

app.UseRouting();

// 添加防伪令牌中间件
app.UseAntiforgery();

app.UseAuthorization();

// 配置MVC路由
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// 配置API路由
app.MapControllers();

// 配置SignalR Hub
app.MapHub<TrajectoryHub>("/trajectoryHub");

// 初始化XCode数据库表结构
//try
//{
//    // 触发实体类的静态构造函数，自动创建表结构
//    _ = TrajectoryAuto.Infrastructure.Data.XCodeModels.SceneEntity.Meta.Count;
//    _ = TrajectoryAuto.Infrastructure.Data.XCodeModels.ChannelEntity.Meta.Count;
//    _ = TrajectoryAuto.Infrastructure.Data.XCodeModels.TrajectoryEntity.Meta.Count;
//    _ = TrajectoryAuto.Infrastructure.Data.XCodeModels.TrajectoryPointEntity.Meta.Count;
    
//    XTrace.WriteLine("数据库表结构初始化完成 - 音频灯光联动系统就绪");
//}
//catch (Exception ex)
//{
//    XTrace.WriteException(ex);
//}

app.Run();