{"Version": 1, "ManifestType": "Publish", "Endpoints": [{"Route": "css/all.min.css", "AssetFile": "css/all.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "102025"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:46:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8="}]}, {"Route": "css/all.min.ywkkukpd6z.css", "AssetFile": "css/all.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "102025"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:46:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ywkkukpd6z"}, {"Name": "integrity", "Value": "sha256-HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8="}, {"Name": "label", "Value": "css/all.min.css"}]}, {"Route": "css/bootstrap.min.css", "AssetFile": "css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12783"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 09:04:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM="}]}, {"Route": "css/bootstrap.min.u5b947y67y.css", "AssetFile": "css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12783"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 09:04:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u5b947y67y"}, {"Name": "integrity", "Value": "sha256-Pr+zMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM="}, {"Name": "label", "Value": "css/bootstrap.min.css"}]}, {"Route": "css/custom.atdhu7b3gl.css", "AssetFile": "css/custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7051"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 15:25:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "atdhu7b3gl"}, {"Name": "integrity", "Value": "sha256-0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY="}, {"Name": "label", "Value": "css/custom.css"}]}, {"Route": "css/custom.css", "AssetFile": "css/custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7051"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 15:25:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY="}]}, {"Route": "css/modal.css", "AssetFile": "css/modal.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11422"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 09:12:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M="}]}, {"Route": "css/modal.qedwpqxz3l.css", "AssetFile": "css/modal.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11422"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 09:12:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qedwpqxz3l"}, {"Name": "integrity", "Value": "sha256-4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M="}, {"Name": "label", "Value": "css/modal.css"}]}, {"Route": "css/playback.css", "AssetFile": "css/playback.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "983"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 08:21:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0="}]}, {"Route": "css/playback.flizyfk2xy.css", "AssetFile": "css/playback.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "983"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 08:21:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "flizyfk2xy"}, {"Name": "integrity", "Value": "sha256-4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0="}, {"Name": "label", "Value": "css/playback.css"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "559"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:37:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E="}]}, {"Route": "css/site.gekhf0kgg3.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "559"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:37:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gekhf0kgg3"}, {"Name": "integrity", "Value": "sha256-iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/style.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22448"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 08:12:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ="}]}, {"Route": "css/style.xubv7qtpf4.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22448"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 08:12:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xubv7qtpf4"}, {"Name": "integrity", "Value": "sha256-yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ="}, {"Name": "label", "Value": "css/style.css"}]}, {"Route": "css/trajectory-actions.css", "AssetFile": "css/trajectory-actions.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1070"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:27:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA="}]}, {"Route": "css/trajectory-actions.zrzw13xroc.css", "AssetFile": "css/trajectory-actions.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1070"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:27:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zrzw13xroc"}, {"Name": "integrity", "Value": "sha256-4SUVaikLX+y7dscg8f/MmxuxzUckbKjUa+gENEybXfA="}, {"Name": "label", "Value": "css/trajectory-actions.css"}]}, {"Route": "css/trajectory.css", "AssetFile": "css/trajectory.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8155"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:34:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA="}]}, {"Route": "css/trajectory.etg9qljj4e.css", "AssetFile": "css/trajectory.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8155"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:34:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "etg9qljj4e"}, {"Name": "integrity", "Value": "sha256-YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA="}, {"Name": "label", "Value": "css/trajectory.css"}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8832"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:14:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ="}]}, {"Route": "index.vfc0agseey.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8832"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:14:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfc0agseey"}, {"Name": "integrity", "Value": "sha256-BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr+tUXGvQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "js/app.gtlg8ms8g6.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "48746"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:07:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gtlg8ms8g6"}, {"Name": "integrity", "Value": "sha256-jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48746"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:07:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q="}]}, {"Route": "js/canvas.js", "AssetFile": "js/canvas.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "75498"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:17:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4="}]}, {"Route": "js/canvas.uqsvneiks2.js", "AssetFile": "js/canvas.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "75498"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:17:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uqsvneiks2"}, {"Name": "integrity", "Value": "sha256-RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4="}, {"Name": "label", "Value": "js/canvas.js"}]}, {"Route": "js/channelManager.js", "AssetFile": "js/channelManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26101"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 12:03:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus="}]}, {"Route": "js/channelManager.trl6al0oog.js", "AssetFile": "js/channelManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26101"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 12:03:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "trl6al0oog"}, {"Name": "integrity", "Value": "sha256-uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus="}, {"Name": "label", "Value": "js/channelManager.js"}]}, {"Route": "js/modalTest.js", "AssetFile": "js/modalTest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3888"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 09:10:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c="}]}, {"Route": "js/modalTest.qemmm9cnpe.js", "AssetFile": "js/modalTest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3888"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 09:10:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qemmm9cnpe"}, {"Name": "integrity", "Value": "sha256-Dssu5dT+9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c="}, {"Name": "label", "Value": "js/modalTest.js"}]}, {"Route": "js/playbackManager.js", "AssetFile": "js/playbackManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15500"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 16:43:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg="}]}, {"Route": "js/playbackManager.n541x1vj06.js", "AssetFile": "js/playbackManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15500"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 16:43:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n541x1vj06"}, {"Name": "integrity", "Value": "sha256-S+BiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg="}, {"Name": "label", "Value": "js/playbackManager.js"}]}, {"Route": "js/sceneManager.hmsqx3cwiz.js", "AssetFile": "js/sceneManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "36236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:12:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmsqx3cwiz"}, {"Name": "integrity", "Value": "sha256-aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw="}, {"Name": "label", "Value": "js/sceneManager.js"}]}, {"Route": "js/sceneManager.js", "AssetFile": "js/sceneManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:12:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw="}]}, {"Route": "js/site.50b1q86w11.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7623"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:51:25 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "50b1q86w11"}, {"Name": "integrity", "Value": "sha256-CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7623"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:51:25 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4="}]}, {"Route": "js/trajectory.js", "AssetFile": "js/trajectory.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 16:59:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c="}]}, {"Route": "js/trajectory.outbabz928.js", "AssetFile": "js/trajectory.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 16:59:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "outbabz928"}, {"Name": "integrity", "Value": "sha256-4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL+c="}, {"Name": "label", "Value": "js/trajectory.js"}]}, {"Route": "js/trajectoryManager.js", "AssetFile": "js/trajectoryManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "67293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 17:03:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w="}]}, {"Route": "js/trajectoryManager.js.32cvhp1tpd.backup", "AssetFile": "js/trajectoryManager.js.backup", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "67680"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 05:24:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "32cvhp1tpd"}, {"Name": "integrity", "Value": "sha256-NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So="}, {"Name": "label", "Value": "js/trajectoryManager.js.backup"}]}, {"Route": "js/trajectoryManager.js.backup", "AssetFile": "js/trajectoryManager.js.backup", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "67680"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 05:24:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NazynAdMPZDmjLqbPOUz26LpgmEat+jwBEPBggUP2So="}]}, {"Route": "js/trajectoryManager.sf1oj4kvz9.js", "AssetFile": "js/trajectoryManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "67293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 17:03:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sf1oj4kvz9"}, {"Name": "integrity", "Value": "sha256-uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w="}, {"Name": "label", "Value": "js/trajectoryManager.js"}]}, {"Route": "js/uiManager.js", "AssetFile": "js/uiManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5412"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 02:24:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA="}]}, {"Route": "js/uiManager.o8c5mi9y29.js", "AssetFile": "js/uiManager.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5412"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 02:24:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o8c5mi9y29"}, {"Name": "integrity", "Value": "sha256-yMSWVDSp8+iy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA="}, {"Name": "label", "Value": "js/uiManager.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.4r87du48le.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:51:04 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4r87du48le"}, {"Name": "integrity", "Value": "sha256-EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:51:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EM3893nKu9J5wzFCTX34XQM41Tsrr+E9ghiaeM72CDU="}]}, {"Route": "lib/jquery/dist/jquery.min.ikicaa7rus.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5675"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:50:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ikicaa7rus"}, {"Name": "integrity", "Value": "sha256-AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5675"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 08:50:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AsurigaMJIgR+ENgNAK6TFlu4Qp7vmO07EEwTnrovK8="}]}, {"Route": "lib/signalr/signalr.min.ai9bojo3f9.js", "AssetFile": "lib/signalr/signalr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "48701"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:59:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ai9bojo3f9"}, {"Name": "integrity", "Value": "sha256-HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU="}, {"Name": "label", "Value": "lib/signalr/signalr.min.js"}]}, {"Route": "lib/signalr/signalr.min.js", "AssetFile": "lib/signalr/signalr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48701"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:59:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.hgzbx4oip4.jpg", "AssetFile": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "409227"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:05:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hgzbx4oip4"}, {"Name": "integrity", "Value": "sha256-vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28="}, {"Name": "label", "Value": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg"}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg", "AssetFile": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "409227"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 08:05:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg", "AssetFile": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:27:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.v5rxafksj3.jpg", "AssetFile": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:27:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}, {"Name": "label", "Value": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg"}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg", "AssetFile": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:22:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.v5rxafksj3.jpg", "AssetFile": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:22:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}, {"Name": "label", "Value": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg"}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg", "AssetFile": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:27:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.v5rxafksj3.jpg", "AssetFile": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:27:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}, {"Name": "label", "Value": "uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg"}]}, {"Route": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg", "AssetFile": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:36:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.v5rxafksj3.jpg", "AssetFile": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:36:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}, {"Name": "label", "Value": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg"}]}, {"Route": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg", "AssetFile": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:43:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.v5rxafksj3.jpg", "AssetFile": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:43:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}, {"Name": "label", "Value": "uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg"}]}, {"Route": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg", "AssetFile": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:21:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}]}, {"Route": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.v5rxafksj3.jpg", "AssetFile": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28283"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 01:21:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5rxafksj3"}, {"Name": "integrity", "Value": "sha256-PdJ8qHlT86SP9wJ41HRNBDyw+bHZVmYoHmZYcbVX0Qk="}, {"Name": "label", "Value": "uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg"}]}, {"Route": "webfonts/fa-brands-400.gjto57buk3.woff2", "AssetFile": "webfonts/fa-brands-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "108020"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:52:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gjto57buk3"}, {"Name": "integrity", "Value": "sha256-dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc="}, {"Name": "label", "Value": "webfonts/fa-brands-400.woff2"}]}, {"Route": "webfonts/fa-brands-400.woff2", "AssetFile": "webfonts/fa-brands-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "108020"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:52:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc="}]}, {"Route": "webfonts/fa-regular-400.woff2", "AssetFile": "webfonts/fa-regular-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "24948"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:51:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU="}]}, {"Route": "webfonts/fa-regular-400.yrqo96mk2j.woff2", "AssetFile": "webfonts/fa-regular-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24948"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:51:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yrqo96mk2j"}, {"Name": "integrity", "Value": "sha256-jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ+zU="}, {"Name": "label", "Value": "webfonts/fa-regular-400.woff2"}]}, {"Route": "webfonts/fa-solid-900.bxa1shs6v7.woff2", "AssetFile": "webfonts/fa-solid-900.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "150124"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:51:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bxa1shs6v7"}, {"Name": "integrity", "Value": "sha256-cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA="}, {"Name": "label", "Value": "webfonts/fa-solid-900.woff2"}]}, {"Route": "webfonts/fa-solid-900.woff2", "AssetFile": "webfonts/fa-solid-900.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "150124"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 17:51:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA="}]}]}