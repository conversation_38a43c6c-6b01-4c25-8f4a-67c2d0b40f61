﻿#Software: TrajectoryAuto.Web
#ProcessID: 23548 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:34:15.9530000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
00:01:54.981  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
00:01:54.985  1 N - NewLife组件核心库 ©2002-2024 NewLife
00:01:54.985  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
00:01:54.985  1 N - TrajectoryAuto.Web 
00:01:54.985  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
00:01:54.985  1 N - NewLife数据中间件 ©2002-2024 NewLife
00:01:54.985  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
00:01:55.346  1 N - [TrajectoryAuto]待检查数据表：Scenes
00:01:55.351  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
00:01:55.369  1 N - [TrajectoryAuto] select * from sqlite_master
00:01:55.376  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
00:01:55.377  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
00:01:55.429  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
00:01:55.434  1 N - [TrajectoryAuto] Select Count(*) From Scenes
00:01:55.444  1 N - [TrajectoryAuto]待检查数据表：Channels
00:01:55.445  1 N - [TrajectoryAuto] select * from sqlite_master
00:01:55.452  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
00:01:55.452  1 N - [TrajectoryAuto] Select Count(*) From Channels
00:01:55.461  1 N - [TrajectoryAuto]待检查数据表：Trajectories
00:01:55.462  1 N - [TrajectoryAuto] select * from sqlite_master
00:01:55.469  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
00:01:55.470  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
00:01:55.476  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
00:01:55.477  1 N - [TrajectoryAuto] select * from sqlite_master
00:01:55.484  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
00:01:55.485  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
00:01:55.494  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 16096 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 04:39:01.7810000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
00:06:40.813  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
00:06:40.817  1 N - NewLife组件核心库 ©2002-2024 NewLife
00:06:40.817  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
00:06:40.817  1 N - TrajectoryAuto.Web 
00:06:40.817  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
00:06:40.817  1 N - NewLife数据中间件 ©2002-2024 NewLife
00:06:40.817  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
00:06:41.202  1 N - [TrajectoryAuto]待检查数据表：Scenes
00:06:41.207  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
00:06:41.224  1 N - [TrajectoryAuto] select * from sqlite_master
00:06:41.232  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
00:06:41.232  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
00:06:41.295  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
00:06:41.300  1 N - [TrajectoryAuto] Select Count(*) From Scenes
00:06:41.312  1 N - [TrajectoryAuto]待检查数据表：Channels
00:06:41.313  1 N - [TrajectoryAuto] select * from sqlite_master
00:06:41.320  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
00:06:41.321  1 N - [TrajectoryAuto] Select Count(*) From Channels
00:06:41.330  1 N - [TrajectoryAuto]待检查数据表：Trajectories
00:06:41.330  1 N - [TrajectoryAuto] select * from sqlite_master
00:06:41.337  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
00:06:41.338  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
00:06:41.346  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
00:06:41.347  1 N - [TrajectoryAuto] select * from sqlite_master
00:06:41.354  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
00:06:41.355  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
00:06:41.364  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 25408 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:07:40.4840000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
00:35:19.505  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
00:35:19.510  1 N - NewLife组件核心库 ©2002-2024 NewLife
00:35:19.511  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
00:35:19.511  1 N - TrajectoryAuto.Web 
00:35:19.511  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
00:35:19.511  1 N - NewLife数据中间件 ©2002-2024 NewLife
00:35:19.511  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
00:35:19.996  1 N - [TrajectoryAuto]待检查数据表：Scenes
00:35:20.003  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
00:35:20.032  1 N - [TrajectoryAuto] select * from sqlite_master
00:35:20.046  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
00:35:20.047  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
00:35:20.123  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
00:35:20.130  1 N - [TrajectoryAuto] Select Count(*) From Scenes
00:35:20.144  1 N - [TrajectoryAuto]待检查数据表：Channels
00:35:20.145  1 N - [TrajectoryAuto] select * from sqlite_master
00:35:20.153  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
00:35:20.154  1 N - [TrajectoryAuto] Select Count(*) From Channels
00:35:20.165  1 N - [TrajectoryAuto]待检查数据表：Trajectories
00:35:20.165  1 N - [TrajectoryAuto] select * from sqlite_master
00:35:20.175  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
00:35:20.176  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
00:35:20.185  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
00:35:20.185  1 N - [TrajectoryAuto] select * from sqlite_master
00:35:20.195  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
00:35:20.195  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
00:35:20.208  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 16704 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:28:36.0930000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadId Kind Name Message
00:56:15.107 01 N - NewLife.Core v11.6.2025.0801 Build 2025-08-01 00:00:00 .NET 8.0
00:56:15.116 01 N - NewLife组件核心库 ©2002-2025 NewLife
00:56:15.117 01 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 00:00:00 .NET 8.0
00:56:15.118 01 N - TrajectoryAuto.Web 
00:56:15.118 01 N - 更新配置：D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\Config\Core.config，原："eFormat>\r\n  <!--网络日志。本地子网日志广播udp://255.255.255.2"，新："eFormat>\r\n  <!--日志行格式。默认Time|ThreadId|Kind|Name|"
00:56:15.119 01 N - 更新配置：D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\Config\XCode.config，原："tchSize>\r\n  <!--命令超时。查询执行超时时间，默认0秒不限制-->\r\n  <Com"，新："tchSize>\r\n  <!--批操作间隙。用于批量删除数据时的暂停间隙，单位毫秒，默认100-"
00:56:15.126 01 N - XCode v11.20.2025.0801 Build 2025-08-01 00:00:00 .NET Standard 2.1
00:56:15.126 01 N - NewLife数据中间件 ©2002-2025 NewLife
00:56:15.126 01 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
00:56:15.721 01 N - [TrajectoryAuto]待检查数据表：Scenes
00:56:15.762 01 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
00:56:15.859 01 N - [TrajectoryAuto] select * from sqlite_master
00:56:15.871 01 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
00:56:15.872 01 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
00:56:15.957 01 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
00:56:15.963 01 N - [TrajectoryAuto] Select Count(*) From Scenes
00:56:15.979 01 N - [TrajectoryAuto]待检查数据表：Channels
00:56:15.980 01 N - [TrajectoryAuto] select * from sqlite_master
00:56:15.989 01 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
00:56:15.990 01 N - [TrajectoryAuto] Select Count(*) From Channels
00:56:16.003 01 N - [TrajectoryAuto]待检查数据表：Trajectories
00:56:16.004 01 N - [TrajectoryAuto] select * from sqlite_master
00:56:16.014 01 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
00:56:16.014 01 N - [TrajectoryAuto] Select Count(*) From Trajectories
00:56:16.026 01 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
00:56:16.027 01 N - [TrajectoryAuto] select * from sqlite_master
00:56:16.038 01 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
00:56:16.039 01 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
00:56:16.052 01 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
00:58:56.330 02 N - TimerScheduler.ClearAll [1]
00:58:56.331 02 N - DefaultTimer.ClearAll [7]

#Software: TrajectoryAuto.Web
#ProcessID: 23316 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:31:24.3900000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadId Kind Name Message
00:59:03.419 01 N - NewLife.Core v11.6.2025.0801 Build 2025-08-01 00:00:00 .NET 8.0
00:59:03.423 01 N - NewLife组件核心库 ©2002-2025 NewLife
00:59:03.424 01 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 00:00:00 .NET 8.0
00:59:03.424 01 N - TrajectoryAuto.Web 
00:59:03.424 01 N - XCode v11.20.2025.0801 Build 2025-08-01 00:00:00 .NET Standard 2.1
00:59:03.424 01 N - NewLife数据中间件 ©2002-2025 NewLife
00:59:03.424 01 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
00:59:03.948 01 N - [TrajectoryAuto]待检查数据表：Scenes
00:59:03.957 01 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
00:59:03.980 01 N - [TrajectoryAuto] select * from sqlite_master
00:59:03.990 01 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
00:59:03.991 01 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
00:59:04.069 01 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
00:59:04.074 01 N - [TrajectoryAuto] Select Count(*) From Scenes
00:59:04.090 01 N - [TrajectoryAuto]待检查数据表：Channels
00:59:04.090 01 N - [TrajectoryAuto] select * from sqlite_master
00:59:04.099 01 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
00:59:04.100 01 N - [TrajectoryAuto] Select Count(*) From Channels
00:59:04.111 01 N - [TrajectoryAuto]待检查数据表：Trajectories
00:59:04.112 01 N - [TrajectoryAuto] select * from sqlite_master
00:59:04.121 01 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
00:59:04.122 01 N - [TrajectoryAuto] Select Count(*) From Trajectories
00:59:04.132 01 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
00:59:04.133 01 N - [TrajectoryAuto] select * from sqlite_master
00:59:04.143 01 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
00:59:04.143 01 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
00:59:04.154 01 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪
01:04:34.680 02 N - TimerScheduler.ClearAll [1]
01:04:34.681 02 N - DefaultTimer.ClearAll [7]

#Software: TrajectoryAuto.Web
#ProcessID: 26332 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 05:37:05.6710000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
01:04:44.693  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
01:04:44.698  1 N - NewLife组件核心库 ©2002-2024 NewLife
01:04:44.699  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
01:04:44.699  1 N - TrajectoryAuto.Web 
01:04:44.699  1 N - 保存配置 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\Config\Core.config
01:04:44.701  1 N - 保存配置 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\Config\XCode.config
01:04:44.705  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
01:04:44.705  1 N - NewLife数据中间件 ©2002-2024 NewLife
01:04:44.705  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
01:04:45.141  1 N - [TrajectoryAuto]待检查数据表：Scenes
01:04:45.147  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
01:04:45.164  1 N - [TrajectoryAuto] select * from sqlite_master
01:04:45.172  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
01:04:45.172  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
01:04:45.227  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 -2
01:04:45.232  1 N - [TrajectoryAuto] Select Count(*) From Scenes
01:04:45.246  1 N - [TrajectoryAuto]待检查数据表：Channels
01:04:45.246  1 N - [TrajectoryAuto] select * from sqlite_master
01:04:45.256  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 -2
01:04:45.257  1 N - [TrajectoryAuto] Select Count(*) From Channels
01:04:45.268  1 N - [TrajectoryAuto]待检查数据表：Trajectories
01:04:45.269  1 N - [TrajectoryAuto] select * from sqlite_master
01:04:45.278  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 -2
01:04:45.279  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
01:04:45.288  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
01:04:45.288  1 N - [TrajectoryAuto] select * from sqlite_master
01:04:45.296  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 -2
01:04:45.297  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
01:04:45.306  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: TrajectoryAuto.Web
#ProcessID: 15144 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:05:19.3590000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
13:06:49.107  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
13:06:49.112  1 N - NewLife组件核心库 ©2002-2024 NewLife
13:06:49.113  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
13:06:49.113  1 N - TrajectoryAuto.Web 
13:06:49.113  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
13:06:49.113  1 N - NewLife数据中间件 ©2002-2024 NewLife
13:06:49.113  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
13:06:50.326  1 N - [TrajectoryAuto]待检查数据表：Scenes
13:06:50.342  1 N - [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
13:06:50.522  1 N - [TrajectoryAuto] select * from sqlite_master
13:06:50.531  1 N - Data Source=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
13:06:50.531  1 N - [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
13:06:50.612  1 N - SceneEntity.Count 快速计算表记录数（非精确）[Scenes/TrajectoryAuto] 参考值 2
13:06:50.619  1 N - [TrajectoryAuto] Select Count(*) From Scenes
13:06:50.630  1 N - [TrajectoryAuto]待检查数据表：Channels
13:06:50.630  1 N - [TrajectoryAuto] select * from sqlite_master
13:06:50.635  1 N - ChannelEntity.Count 快速计算表记录数（非精确）[Channels/TrajectoryAuto] 参考值 5
13:06:50.636  1 N - [TrajectoryAuto] Select Count(*) From Channels
13:06:50.644  1 N - [TrajectoryAuto]待检查数据表：Trajectories
13:06:50.645  1 N - [TrajectoryAuto] select * from sqlite_master
13:06:50.651  1 N - TrajectoryEntity.Count 快速计算表记录数（非精确）[Trajectories/TrajectoryAuto] 参考值 82
13:06:50.651  1 N - [TrajectoryAuto] Select Count(*) From Trajectories
13:06:50.659  1 N - [TrajectoryAuto]待检查数据表：TrajectoryPoints
13:06:50.660  1 N - [TrajectoryAuto] select * from sqlite_master
13:06:50.675  1 N - TrajectoryPointEntity.Count 快速计算表记录数（非精确）[TrajectoryPoints/TrajectoryAuto] 参考值 16,107
13:06:50.676  1 N - [TrajectoryAuto] Select Count(*) From TrajectoryPoints
13:06:50.682  1 N - 数据库表结构初始化完成 - 音频灯光联动系统就绪

#Software: ef
#ProcessID: 9908 x64
#AppDomain: ef
#FileName: C:\Program Files\dotnet\dotnet.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: C:\Users\<USER>\.nuget\packages\dotnet-ef\9.0.8\tools\net8.0\any\tools\netcoreapp2.0\any\ef.dll dbcontext list --json --assembly D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll --project D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\TrajectoryAuto.Web.csproj --startup-assembly D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll --startup-project D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\TrajectoryAuto.Web.csproj --project-dir D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\ --root-namespace TrajectoryAuto.Web --language C# --framework net8.0 --nullable --working-dir D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:07:08.1250000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
13:08:37.861  4 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
13:08:37.869  4 N - NewLife组件核心库 ©2002-2024 NewLife
13:08:37.870  4 N - ef v9.0.8 Build 2000-01-09 .NET Core 2.0
13:08:37.870  4 N - ef © Microsoft Corporation. All rights reserved.
13:08:37.870  4 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
13:08:37.870  4 N - NewLife数据中间件 ©2002-2024 NewLife
13:08:37.870  4 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。

#Software: ef
#ProcessID: 6976 x64
#AppDomain: ef
#FileName: C:\Program Files\dotnet\dotnet.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: C:\Users\<USER>\.nuget\packages\dotnet-ef\9.0.8\tools\net8.0\any\tools\netcoreapp2.0\any\ef.dll dbcontext list --json --assembly D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll --project D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\TrajectoryAuto.Web.csproj --startup-assembly D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll --startup-project D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\TrajectoryAuto.Web.csproj --project-dir D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web\ --root-namespace TrajectoryAuto.Web --language C# --framework net8.0 --nullable --working-dir D:\Project\TrajectoryAuto\code\TrajectoryAuto.Web
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 00:07:14.4680000
#Date: 2025-08-08
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
13:08:44.225  4 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
13:08:44.231  4 N - NewLife组件核心库 ©2002-2024 NewLife
13:08:44.231  4 N - ef v9.0.8 Build 2000-01-09 .NET Core 2.0
13:08:44.232  4 N - ef © Microsoft Corporation. All rights reserved.
13:08:44.232  4 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
13:08:44.232  4 N - NewLife数据中间件 ©2002-2024 NewLife
13:08:44.232  4 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
