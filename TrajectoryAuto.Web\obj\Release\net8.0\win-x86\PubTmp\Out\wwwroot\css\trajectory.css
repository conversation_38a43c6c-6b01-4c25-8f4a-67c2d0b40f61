/* 轨迹自动化系统专用样式 */

/* 头部样式 */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-bottom: 3px solid rgba(255,255,255,0.1);
    height: 80px;
    padding: 0.5rem 2rem;
}

.app-header .navbar-brand {
    color: white !important;
    font-weight: 700;
    font-size: 1.8rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    text-decoration: none;
    display: block;
}

.app-header .navbar-brand:hover {
    transform: translateY(-2px);
    text-shadow: 2px 4px 8px rgba(0,0,0,0.4);
    color: white !important;
}

.text-gradient {
    background: linear-gradient(45deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.app-header .navbar-brand i {
    color: #ffd700;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.status-indicator {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    background: rgba(255,255,255,0.15);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.status-indicator.connected {
    background: rgba(76, 175, 80, 0.2);
    border-color: #4caf50;
    color: #4caf50;
}

.status-indicator.disconnected {
    background: rgba(244, 67, 54, 0.2);
    border-color: #f44336;
    color: #f44336;
}

.app-header .btn {
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.app-header .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.app-header .btn i {
    font-size: 0.9rem;
}

/* 主要内容区域 */
.main-content {
    padding: 20px;
    background: #f8f9fa;
    min-height: calc(100vh - 80px);
}

/* 管理面板样式 */
.management-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.management-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.panel-header {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    padding: 15px 20px;
    font-weight: 600;
    font-size: 1.1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.panel-header i {
    margin-right: 8px;
    color: #ffd700;
}

.panel-content {
    padding: 20px;
}

/* 场景管理样式 */
.scene-management {
    width: 25%;
    margin-right: 15px;
}

/* 通道管理样式 */
.channel-management {
    width: 25%;
    margin-right: 15px;
}

/* 轨迹绘制区域样式 */
.trajectory-drawing {
    width: 50%;
}

.drawing-canvas-container {
    position: relative;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

#trajectoryCanvas {
    display: block;
    cursor: crosshair;
    background: white;
}

/* 工具栏样式 */
.toolbar {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 0.5rem;
    align-items: center;
}

.toolbar .btn-group {
    margin-right: 0.5rem;
    display: flex;
    gap: 0.3rem;
}

.toolbar .btn {
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.toolbar .btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table thead th {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 15px;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(74, 144, 226, 0.05);
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

/* 状态指示器 */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-active {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-inactive {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .scene-management,
    .channel-management {
        width: 30%;
    }
    
    .trajectory-drawing {
        width: 40%;
    }
}

@media (max-width: 992px) {
    .scene-management,
    .channel-management,
    .trajectory-drawing {
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .app-header .navbar-brand {
        font-size: 1.4rem;
    }
}

@media (max-width: 768px) {
    .app-header .navbar-brand {
        font-size: 1.2rem;
    }
    
    .status-indicator {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
    
    .app-header .btn {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.8rem;
}

.tooltip-inner {
    background: rgba(0,0,0,0.9);
    border-radius: 6px;
    padding: 8px 12px;
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
}
 

.modal-footer {
    border-top: 1px solid #e9ecef; 
}