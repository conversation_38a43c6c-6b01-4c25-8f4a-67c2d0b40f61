using TrajectoryAuto.Core.Entities;

namespace TrajectoryAuto.Core.Interfaces;

/// <summary>
/// 通道服务接口
/// </summary>
public interface IChannelService
{
    /// <summary>
    /// 获取场景的所有通道
    /// </summary>
    /// <param name="sceneId"></param>
    /// <returns></returns>
    Task<List<Channel>> GetChannelsBySceneIdAsync(Guid sceneId);
    
    /// <summary>
    /// 根据ID获取通道
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<Channel?> GetChannelByIdAsync(Guid id);
    
    /// <summary>
    /// 创建通道
    /// </summary>
    /// <param name="channel"></param>
    /// <returns></returns>
    Task<Channel> CreateChannelAsync(Channel channel);
    
    /// <summary>
    /// 更新通道
    /// </summary>
    /// <param name="channel"></param>
    /// <returns></returns>
    Task<Channel> UpdateChannelAsync(Channel channel);
    
    /// <summary>
    /// 删除通道
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<bool> DeleteChannelAsync(Guid id);
    
    /// <summary>
    /// 设置活动通道
    /// </summary>
    /// <param name="channelId"></param>
    /// <param name="sceneId"></param>
    /// <returns></returns>
    Task<bool> SetActiveChannelAsync(Guid channelId, Guid sceneId);
}