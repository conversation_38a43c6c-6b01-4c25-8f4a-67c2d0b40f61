{"Version": 1, "WorkspaceRootPath": "D:\\Project\\TrajectoryAuto\\code_claude\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\controllers\\trajectoryplaybackcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\controllers\\trajectoryplaybackcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\controllers\\simpleudpcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\controllers\\simpleudpcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\views\\home\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\views\\home\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.infrastructure\\services\\simpleudpmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|solutionrelative:trajectoryauto.infrastructure\\services\\simpleudpmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\controllers\\playbackperformancecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\controllers\\playbackperformancecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{19F5E8C2-C8C8-483B-B92D-091704D00796}|TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.core\\interfaces\\ienhancedcommunicationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{19F5E8C2-C8C8-483B-B92D-091704D00796}|TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj|solutionrelative:trajectoryauto.core\\interfaces\\ienhancedcommunicationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.infrastructure\\services\\communicationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|solutionrelative:trajectoryauto.infrastructure\\services\\communicationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.infrastructure\\services\\performantplaybackmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|solutionrelative:trajectoryauto.infrastructure\\services\\performantplaybackmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\wwwroot\\js\\canvas.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\wwwroot\\js\\canvas.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\controllers\\udpcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\controllers\\udpcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\hubs\\trajectoryhub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\hubs\\trajectoryhub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\wwwroot\\js\\app.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\wwwroot\\js\\app.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.infrastructure\\services\\xcodetrajectoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|solutionrelative:trajectoryauto.infrastructure\\services\\xcodetrajectoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\views\\shared\\_layout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\views\\shared\\_layout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\wwwroot\\js\\trajectory.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\wwwroot\\js\\trajectory.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.web\\wwwroot\\js\\scenemanager.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|TrajectoryAuto.Web\\TrajectoryAuto.Web.csproj|solutionrelative:trajectoryauto.web\\wwwroot\\js\\scenemanager.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{19F5E8C2-C8C8-483B-B92D-091704D00796}|TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.core\\entities\\trajectory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{19F5E8C2-C8C8-483B-B92D-091704D00796}|TrajectoryAuto.Core\\TrajectoryAuto.Core.csproj|solutionrelative:trajectoryauto.core\\entities\\trajectory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|d:\\project\\trajectoryauto\\code_claude\\trajectoryauto.infrastructure\\data\\xcodemodels\\trajectoryentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73A1C70F-587D-40AA-BC37-0058858930DA}|TrajectoryAuto.Infrastructure\\TrajectoryAuto.Infrastructure.csproj|solutionrelative:trajectoryauto.infrastructure\\data\\xcodemodels\\trajectoryentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e506b91c-c606-466a-90a9-123d1d1e12b3}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\appsettings.json", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\appsettings.json", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\appsettings.json", "RelativeToolTip": "TrajectoryAuto.Web\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-08T01:10:39.85Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "SimpleUdpController.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Controllers\\SimpleUdpController.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\Controllers\\SimpleUdpController.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Controllers\\SimpleUdpController.cs", "RelativeToolTip": "TrajectoryAuto.Web\\Controllers\\SimpleUdpController.cs", "ViewState": "AgIAADMAAAAAAAAAAAAuwDwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T14:54:25.307Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "TrajectoryPlaybackController.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Controllers\\TrajectoryPlaybackController.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\Controllers\\TrajectoryPlaybackController.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Controllers\\TrajectoryPlaybackController.cs", "RelativeToolTip": "TrajectoryAuto.Web\\Controllers\\TrajectoryPlaybackController.cs", "ViewState": "AgIAAMcEAAAAAAAAAAAAAA0AAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T17:00:06.894Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Program.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Program.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\Program.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Program.cs", "RelativeToolTip": "TrajectoryAuto.Web\\Program.cs", "ViewState": "AgIAAHYAAAAAAAAAAAAAAIAAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T17:26:56.118Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "Index.cshtml", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Views\\Home\\Index.cshtml", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\Views\\Home\\Index.cshtml", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Views\\Home\\Index.cshtml", "RelativeToolTip": "TrajectoryAuto.Web\\Views\\Home\\Index.cshtml", "ViewState": "AgIAAC8AAAAAAAAAAIBPwDoAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-06T08:30:07.375Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "PlaybackPerformanceController.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Controllers\\PlaybackPerformanceController.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\Controllers\\PlaybackPerformanceController.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Controllers\\PlaybackPerformanceController.cs", "RelativeToolTip": "TrajectoryAuto.Web\\Controllers\\PlaybackPerformanceController.cs", "ViewState": "AgIAACcAAAAAAAAAAAAywAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T15:12:36.482Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "SimpleUdpManager.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Services\\SimpleUdpManager.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Infrastructure\\Services\\SimpleUdpManager.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Services\\SimpleUdpManager.cs", "RelativeToolTip": "TrajectoryAuto.Infrastructure\\Services\\SimpleUdpManager.cs", "ViewState": "AgIAALcBAAAAAAAAAIAzwMMBAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T15:35:24.396Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "IEnhancedCommunicationService.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\Interfaces\\IEnhancedCommunicationService.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Core\\Interfaces\\IEnhancedCommunicationService.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\Interfaces\\IEnhancedCommunicationService.cs", "RelativeToolTip": "TrajectoryAuto.Core\\Interfaces\\IEnhancedCommunicationService.cs", "ViewState": "AgIAAGcAAAAAAAAAAIA/wIIAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T13:18:22.058Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "CommunicationService.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Services\\CommunicationService.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Infrastructure\\Services\\CommunicationService.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Services\\CommunicationService.cs", "RelativeToolTip": "TrajectoryAuto.Infrastructure\\Services\\CommunicationService.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAiwFoAAABcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T12:02:17.732Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "PerformantPlaybackManager.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Services\\PerformantPlaybackManager.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Infrastructure\\Services\\PerformantPlaybackManager.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Services\\PerformantPlaybackManager.cs", "RelativeToolTip": "TrajectoryAuto.Infrastructure\\Services\\PerformantPlaybackManager.cs", "ViewState": "AgIAAH0BAAAAAAAAAAAuwBUAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T10:27:24.8Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "app.js", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\wwwroot\\js\\app.js", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\wwwroot\\js\\app.js", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\wwwroot\\js\\app.js", "RelativeToolTip": "TrajectoryAuto.Web\\wwwroot\\js\\app.js", "ViewState": "AgIAACwBAAAAAAAAAIAzwDYBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-07T03:01:52.298Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "TrajectoryHub.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Hubs\\TrajectoryHub.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\Hubs\\TrajectoryHub.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Hubs\\TrajectoryHub.cs", "RelativeToolTip": "TrajectoryAuto.Web\\Hubs\\TrajectoryHub.cs", "ViewState": "AgIAADEAAAAAAAAAAAArwFQAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T02:55:09.143Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "canvas.js", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\wwwroot\\js\\canvas.js", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\wwwroot\\js\\canvas.js", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\wwwroot\\js\\canvas.js", "RelativeToolTip": "TrajectoryAuto.Web\\wwwroot\\js\\canvas.js", "ViewState": "AgIAAEQGAAAAAAAAAIAzwEsGAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-06T05:35:12.182Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "UdpController.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Controllers\\UdpController.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\Controllers\\UdpController.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Controllers\\UdpController.cs", "RelativeToolTip": "TrajectoryAuto.Web\\Controllers\\UdpController.cs", "ViewState": "AgIAABkAAAAAAAAAAIAzwBsAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T17:08:12.774Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "XCodeTrajectoryService.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Services\\XCodeTrajectoryService.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Infrastructure\\Services\\XCodeTrajectoryService.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Services\\XCodeTrajectoryService.cs", "RelativeToolTip": "TrajectoryAuto.Infrastructure\\Services\\XCodeTrajectoryService.cs", "ViewState": "AgIAADwBAAAAAAAAAAAiwD0BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T06:43:11.891Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "_Layout.cshtml", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Views\\Shared\\_Layout.cshtml", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\Views\\Shared\\_Layout.cshtml", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\Views\\Shared\\_Layout.cshtml", "RelativeToolTip": "TrajectoryAuto.Web\\Views\\Shared\\_Layout.cshtml", "ViewState": "AgIAABUAAAAAAAAAAIBfwCUAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-06T08:30:17.282Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "trajectory.js", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\wwwroot\\js\\trajectory.js", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\wwwroot\\js\\trajectory.js", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\wwwroot\\js\\trajectory.js", "RelativeToolTip": "TrajectoryAuto.Web\\wwwroot\\js\\trajectory.js", "ViewState": "AgIAABsAAAAAAAAAAIAzwDsBAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-06T08:59:06.208Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "sceneManager.js", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\wwwroot\\js\\sceneManager.js", "RelativeDocumentMoniker": "TrajectoryAuto.Web\\wwwroot\\js\\sceneManager.js", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Web\\wwwroot\\js\\sceneManager.js", "RelativeToolTip": "TrajectoryAuto.Web\\wwwroot\\js\\sceneManager.js", "ViewState": "AgIAACUDAAAAAAAAAIBfwEoCAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-06T09:43:09.731Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "Trajectory.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\Entities\\Trajectory.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Core\\Entities\\Trajectory.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Core\\Entities\\Trajectory.cs", "RelativeToolTip": "TrajectoryAuto.Core\\Entities\\Trajectory.cs", "ViewState": "AgIAABoAAAAAAAAAAAAiwCAAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T08:47:07.452Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "TrajectoryEntity.cs", "DocumentMoniker": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Data\\XCodeModels\\TrajectoryEntity.cs", "RelativeDocumentMoniker": "TrajectoryAuto.Infrastructure\\Data\\XCodeModels\\TrajectoryEntity.cs", "ToolTip": "D:\\Project\\TrajectoryAuto\\code_claude\\TrajectoryAuto.Infrastructure\\Data\\XCodeModels\\TrajectoryEntity.cs", "RelativeToolTip": "TrajectoryAuto.Infrastructure\\Data\\XCodeModels\\TrajectoryEntity.cs", "ViewState": "AgIAAAMAAAAAAAAAAIBfwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T08:47:01.878Z"}]}]}]}