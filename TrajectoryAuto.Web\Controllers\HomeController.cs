using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using TrajectoryAuto.Web.Models;

namespace TrajectoryAuto.Web.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 主页 - 舞台轨迹自动化系统
        /// </summary>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 关于页面
        /// </summary>
        public IActionResult About()
        {
            ViewData["Message"] = "舞台轨迹自动化系统 - 专业的音频灯光联动解决方案";
            return View();
        }

        /// <summary>
        /// 隐私政策
        /// </summary>
        public IActionResult Privacy()
        {
            return View();
        }

        /// <summary>
        /// 错误页面
        /// </summary>
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}