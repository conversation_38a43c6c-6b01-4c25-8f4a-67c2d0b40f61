Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrajectoryAuto.Web", "TrajectoryAuto.Web\TrajectoryAuto.Web.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrajectoryAuto.Core", "TrajectoryAuto.Core\TrajectoryAuto.Core.csproj", "{19F5E8C2-C8C8-483B-B92D-091704D00796}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrajectoryAuto.Infrastructure", "TrajectoryAuto.Infrastructure\TrajectoryAuto.Infrastructure.csproj", "{73A1C70F-587D-40AA-BC37-0058858930DA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{19F5E8C2-C8C8-483B-B92D-091704D00796}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{19F5E8C2-C8C8-483B-B92D-091704D00796}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{19F5E8C2-C8C8-483B-B92D-091704D00796}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{19F5E8C2-C8C8-483B-B92D-091704D00796}.Release|Any CPU.Build.0 = Release|Any CPU
		{73A1C70F-587D-40AA-BC37-0058858930DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{73A1C70F-587D-40AA-BC37-0058858930DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{73A1C70F-587D-40AA-BC37-0058858930DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{73A1C70F-587D-40AA-BC37-0058858930DA}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
