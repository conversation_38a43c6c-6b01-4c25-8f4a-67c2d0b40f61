/* 轨迹操作按钮样式 */
.trajectory-actions {
    display: flex;
    align-items: center;
    margin-left: 15px;
    padding: 5px 10px;
    background-color: rgba(240, 240, 240, 0.9);
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trajectory-actions button {
    margin: 0 5px;
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.trajectory-actions .btn-edit {
    background-color: #4a90e2;
    color: white;
}

.trajectory-actions .btn-edit:hover {
    background-color: #3a80d2;
}

.trajectory-actions .btn-delete {
    background-color: #e74c3c;
    color: white;
}

.trajectory-actions .btn-delete:hover {
    background-color: #d73c2c;
}

/* 触摸屏优化 */
@media (max-width: 768px) {
    .trajectory-actions {
        padding: 8px 12px;
    }
    
    .trajectory-actions button {
        padding: 8px 12px;
        font-size: 16px;
    }
}