﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/all.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\all.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"102025"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:46:00 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/all.min.ywkkukpd6z.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\all.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ywkkukpd6z"},{"Name":"integrity","Value":"sha256-HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/all.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"102025"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022HtsXJanqjKTc8vVQjO4YMhiqFoXkfBsjBWcX91T1jr8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:46:00 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/bootstrap.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Pr\u002BzMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"12783"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Pr\u002BzMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 09:04:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/bootstrap.min.u5b947y67y.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u5b947y67y"},{"Name":"integrity","Value":"sha256-Pr\u002BzMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/bootstrap.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12783"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Pr\u002BzMadLUkeLIaSY1WcqF3WCAmjCtROoLKKATjG3zhM=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 09:04:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/custom.atdhu7b3gl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\custom.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"atdhu7b3gl"},{"Name":"integrity","Value":"sha256-0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/custom.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7051"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00220tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 15:25:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/custom.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\custom.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7051"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00220tUjVCKcrzeUchgUBvyQLrxzVOWE3PPlMN4yJK6GbHY=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 15:25:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/modal.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\modal.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11422"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00224jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 09:12:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/modal.qedwpqxz3l.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\modal.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qedwpqxz3l"},{"Name":"integrity","Value":"sha256-4jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/modal.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11422"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00224jyH4Cvxk9wvGRHTsHcyaSja69sYPqJe3pYhPNYZI3M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 09:12:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/playback.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\playback.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"983"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00224wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 08:21:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/playback.flizyfk2xy.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\playback.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"flizyfk2xy"},{"Name":"integrity","Value":"sha256-4wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/playback.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"983"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00224wbzEMQlpgwMerqUp41d3A/NmykYDU2bg7OHerAvxW0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 08:21:03 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/site.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"559"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:37:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/site.gekhf0kgg3.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gekhf0kgg3"},{"Name":"integrity","Value":"sha256-iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/site.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"559"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022iDA6InRJ2hBS6SG0cHiqKvhXq4zLUQFZoZUB1UPIC7E=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:37:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/style.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22448"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 08:12:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/style.xubv7qtpf4.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xubv7qtpf4"},{"Name":"integrity","Value":"sha256-yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/style.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22448"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022yWy2BSIGzUsn5wMhkZr4EixcufZLIPeGk7w5TvA1GIQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 08:12:07 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/trajectory-actions.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\trajectory-actions.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4SUVaikLX\u002By7dscg8f/MmxuxzUckbKjUa\u002BgENEybXfA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1070"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00224SUVaikLX\u002By7dscg8f/MmxuxzUckbKjUa\u002BgENEybXfA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 08:27:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/trajectory-actions.zrzw13xroc.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\trajectory-actions.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zrzw13xroc"},{"Name":"integrity","Value":"sha256-4SUVaikLX\u002By7dscg8f/MmxuxzUckbKjUa\u002BgENEybXfA="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/trajectory-actions.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1070"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00224SUVaikLX\u002By7dscg8f/MmxuxzUckbKjUa\u002BgENEybXfA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 08:27:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/trajectory.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\trajectory.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8155"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:34:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/css/trajectory.etg9qljj4e.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\trajectory.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"etg9qljj4e"},{"Name":"integrity","Value":"sha256-YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/css/trajectory.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8155"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YnvZB0OvPoh0UrrvekoiwrlLPAN9KByhaKCnIce1KhA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:34:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/index.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr\u002BtUXGvQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8832"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr\u002BtUXGvQ=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:14:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/index.vfc0agseey.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vfc0agseey"},{"Name":"integrity","Value":"sha256-BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr\u002BtUXGvQ="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/index.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8832"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022BfeBusTIWg/qxFEPtanB53LLjxthXER3LvBr\u002BtUXGvQ=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:14:05 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/app.gtlg8ms8g6.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\app.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gtlg8ms8g6"},{"Name":"integrity","Value":"sha256-jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/app.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"48746"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 07:07:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/app.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\app.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"48746"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jE0tqhSRCRr6PEed9ZYOvOwxdG0PpNH045vKcsFsV2Q=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 07:07:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/canvas.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\canvas.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"75498"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 07:17:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/canvas.uqsvneiks2.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\canvas.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uqsvneiks2"},{"Name":"integrity","Value":"sha256-RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/canvas.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"75498"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022RjKpY/RvKY9R5lo79Ikhr9SfEnZKLQv9qmzjv7g50g4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 07:17:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/channelManager.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\channelManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"26101"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 12:03:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/channelManager.trl6al0oog.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\channelManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"trl6al0oog"},{"Name":"integrity","Value":"sha256-uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/channelManager.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"26101"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022uXHCE3qeCpVa41JEmPvj6mTTPScCGn4Q41zPbdoPTus=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 12:03:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/modalTest.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\modalTest.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Dssu5dT\u002B9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3888"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Dssu5dT\u002B9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 09:10:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/modalTest.qemmm9cnpe.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\modalTest.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qemmm9cnpe"},{"Name":"integrity","Value":"sha256-Dssu5dT\u002B9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/modalTest.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3888"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Dssu5dT\u002B9TuHzr1H27SD0am7bqs0jThYH2gNPj1kc0c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 09:10:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/playbackManager.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\playbackManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-S\u002BBiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"15500"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022S\u002BBiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 16:43:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/playbackManager.n541x1vj06.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\playbackManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n541x1vj06"},{"Name":"integrity","Value":"sha256-S\u002BBiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/playbackManager.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"15500"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022S\u002BBiwnZ0s/heoPTL5gT1635hvSQFf2eax9c/lCaJVVg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 16:43:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/sceneManager.hmsqx3cwiz.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\sceneManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hmsqx3cwiz"},{"Name":"integrity","Value":"sha256-aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/sceneManager.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"36236"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 08:12:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/sceneManager.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\sceneManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"36236"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022aYCrF8zfubRnPFyRaGJiu9jM6S7Dj4eyXtlfZdk/Biw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 08:12:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/site.50b1q86w11.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"50b1q86w11"},{"Name":"integrity","Value":"sha256-CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/site.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7623"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:51:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/site.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7623"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022CzjIgbrSgtyKsF6q7vo4lXoHZzTzIUlRir6fXRijyf4=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:51:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/trajectory.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\trajectory.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL\u002Bc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"18651"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00224DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL\u002Bc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 16:59:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/trajectory.outbabz928.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\trajectory.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"outbabz928"},{"Name":"integrity","Value":"sha256-4DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL\u002Bc="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/trajectory.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"18651"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00224DGPisghPlAr0iFPxjlGPzQ6J/aXGV709oaZudGrL\u002Bc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 16:59:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/trajectoryManager.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\trajectoryManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"67293"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 17:03:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/trajectoryManager.js.32cvhp1tpd.backup">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\trajectoryManager.js.backup'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"32cvhp1tpd"},{"Name":"integrity","Value":"sha256-NazynAdMPZDmjLqbPOUz26LpgmEat\u002BjwBEPBggUP2So="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/trajectoryManager.js.backup"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"67680"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022NazynAdMPZDmjLqbPOUz26LpgmEat\u002BjwBEPBggUP2So=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 05:24:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/trajectoryManager.js.backup">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\trajectoryManager.js.backup'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NazynAdMPZDmjLqbPOUz26LpgmEat\u002BjwBEPBggUP2So="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"67680"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022NazynAdMPZDmjLqbPOUz26LpgmEat\u002BjwBEPBggUP2So=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 05:24:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/trajectoryManager.sf1oj4kvz9.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\trajectoryManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sf1oj4kvz9"},{"Name":"integrity","Value":"sha256-uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/trajectoryManager.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"67293"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022uF//uocuwy2e0PFpS8XIy9oT5SFjAeftbuA9TM48G1w=\u0022"},{"Name":"Last-Modified","Value":"Thu, 07 Aug 2025 17:03:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/uiManager.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\uiManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-yMSWVDSp8\u002Biy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5412"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022yMSWVDSp8\u002Biy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 02:24:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/js/uiManager.o8c5mi9y29.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\uiManager.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o8c5mi9y29"},{"Name":"integrity","Value":"sha256-yMSWVDSp8\u002Biy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/js/uiManager.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5412"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022yMSWVDSp8\u002Biy7UlUEXC9wk7QOD2HHBN77EMB0ZMVRuA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 02:24:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/lib/bootstrap/dist/js/bootstrap.bundle.min.4r87du48le.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4r87du48le"},{"Name":"integrity","Value":"sha256-EM3893nKu9J5wzFCTX34XQM41Tsrr\u002BE9ghiaeM72CDU="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3852"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022EM3893nKu9J5wzFCTX34XQM41Tsrr\u002BE9ghiaeM72CDU=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:51:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/lib/bootstrap/dist/js/bootstrap.bundle.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EM3893nKu9J5wzFCTX34XQM41Tsrr\u002BE9ghiaeM72CDU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3852"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022EM3893nKu9J5wzFCTX34XQM41Tsrr\u002BE9ghiaeM72CDU=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:51:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/lib/jquery/dist/jquery.min.ikicaa7rus.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ikicaa7rus"},{"Name":"integrity","Value":"sha256-AsurigaMJIgR\u002BENgNAK6TFlu4Qp7vmO07EEwTnrovK8="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/lib/jquery/dist/jquery.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5675"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022AsurigaMJIgR\u002BENgNAK6TFlu4Qp7vmO07EEwTnrovK8=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:50:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/lib/jquery/dist/jquery.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AsurigaMJIgR\u002BENgNAK6TFlu4Qp7vmO07EEwTnrovK8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5675"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022AsurigaMJIgR\u002BENgNAK6TFlu4Qp7vmO07EEwTnrovK8=\u0022"},{"Name":"Last-Modified","Value":"Tue, 05 Aug 2025 08:50:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/lib/signalr/signalr.min.ai9bojo3f9.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\signalr\signalr.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ai9bojo3f9"},{"Name":"integrity","Value":"sha256-HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/lib/signalr/signalr.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"48701"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:59:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/lib/signalr/signalr.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\signalr\signalr.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"48701"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HncfsytYB7TWLspsnLalxLIccqLOuSVLGXOqZjZwukU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:59:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.hgzbx4oip4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hgzbx4oip4"},{"Name":"integrity","Value":"sha256-vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"409227"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 08:05:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\5ed512fa-4119-4867-a1c7-33110a87fca5_1fbe0b7d-f528-45f9-8d55-5f94f4ed9b89.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"409227"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022vyYsbcNNmfqmSogsFxZQHBlUbzpbG8NhTyHBJVbZV28=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 08:05:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:27:02 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.v5rxafksj3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v5rxafksj3"},{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_42802d0d-b462-4fca-876b-db2f41d2b9cb.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:27:02 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:22:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.v5rxafksj3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v5rxafksj3"},{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_85798f5c-2b58-4f7c-b20f-62459cf2c730.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:22:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:27:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.v5rxafksj3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v5rxafksj3"},{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/uploads/5ed512fa-4119-4867-a1c7-33110a87fca5_f8ab7bda-c13c-45dc-89a5-1d0b4b28ff77.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:27:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:36:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.v5rxafksj3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v5rxafksj3"},{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_9fc6b8e5-d4cf-4412-b22d-5a0b0f2e3fb3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:36:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:43:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.v5rxafksj3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v5rxafksj3"},{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/uploads/9f1a8e08-a55a-4b95-8d3d-0d033f81d336_cd914497-1515-4a83-8fcc-9a1c9a09c124.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:43:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:21:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.v5rxafksj3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v5rxafksj3"},{"Name":"integrity","Value":"sha256-PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/uploads/becf6abe-c309-4e47-a056-755e7c19eab5_dbd476df-bea9-4073-8905-da20fe70fedb.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"28283"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022PdJ8qHlT86SP9wJ41HRNBDyw\u002BbHZVmYoHmZYcbVX0Qk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 01:21:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/webfonts/fa-brands-400.gjto57buk3.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\webfonts\fa-brands-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gjto57buk3"},{"Name":"integrity","Value":"sha256-dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/webfonts/fa-brands-400.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"108020"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:52:00 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/webfonts/fa-brands-400.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\webfonts\fa-brands-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"108020"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022dIMyCQxLjiD5XQ/1nwviD6nIiTWdOzbUuIbXM3YFQgc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:52:00 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/webfonts/fa-regular-400.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\webfonts\fa-regular-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ\u002BzU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"24948"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ\u002BzU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:51:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/webfonts/fa-regular-400.yrqo96mk2j.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\webfonts\fa-regular-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yrqo96mk2j"},{"Name":"integrity","Value":"sha256-jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ\u002BzU="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/webfonts/fa-regular-400.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24948"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022jn5eobFfYqsU29QXaOj7zSHMhZpOpdqBJFfucUKZ\u002BzU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:51:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/webfonts/fa-solid-900.bxa1shs6v7.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\webfonts\fa-solid-900.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bxa1shs6v7"},{"Name":"integrity","Value":"sha256-cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA="},{"Name":"label","Value":"_content/TrajectoryAuto.Web/webfonts/fa-solid-900.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"150124"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:51:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TrajectoryAuto.Web/webfonts/fa-solid-900.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\webfonts\fa-solid-900.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"150124"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022cVKmkz7j1pDsKvPQnanXAXI9Fqo0EKbYDyj/iGbzuIA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 06 Aug 2025 17:51:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>