using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Core.Models;
using TrajectoryAuto.Infrastructure.Data.XCodeModels;

namespace TrajectoryAuto.Infrastructure.Services;

/// <summary>
/// 轨迹服务实现 - 使用NewLife.XCode优化音频灯光联动性能
/// </summary>
public class XCodeTrajectoryService : ITrajectoryService
{
    private readonly ICommunicationService _communicationService;
    private readonly Dictionary<Guid, PlaybackStatus> _playbackStatuses = new();
    private readonly Dictionary<Guid, CancellationTokenSource> _playbackTokens = new();

    public XCodeTrajectoryService(ICommunicationService communicationService)
    {
        _communicationService = communicationService;
    }

    public Task<List<Trajectory>> GetTrajectoriesByChannelIdAsync(Guid channelId)
    {
        var entities = TrajectoryEntity.FindByChannelId(channelId.ToString());
        var trajectories = new List<Trajectory>();
        
        foreach (var entity in entities)
        {
            var trajectory = entity.ToBusinessEntity();
            // 延迟加载轨迹点，提升查询性能
            var points = entity.GetPoints();
            trajectory.Points = points.Select(p => p.ToBusinessEntity()).ToList();
            trajectories.Add(trajectory);
        }
        
        return Task.FromResult(trajectories);
    }

    public async Task<Trajectory?> GetTrajectoryByIdAsync(Guid id)
    {
        return await Task.Run(() =>
        {
            var entity = TrajectoryEntity.FindById(id.ToString());
            if (entity == null) return null;

            var trajectory = entity.ToBusinessEntity();
            var points = entity.GetPoints();
            trajectory.Points = points.Select(p => p.ToBusinessEntity()).ToList();
            
            return trajectory;
        });
    }

    public async Task<Trajectory> CreateTrajectoryAsync(Trajectory trajectory)
    {
        return await Task.Run(() =>
        {
            // 使用事务确保数据一致性
            using var trans = TrajectoryEntity.Meta.CreateTrans();
            
            var entity = new TrajectoryEntity();
            entity.FromBusinessEntity(trajectory);
            entity.Insert();

            // 批量插入轨迹点，提升性能
            if (trajectory.Points?.Count > 0)
            {
                var pointEntities = new List<TrajectoryPointEntity>();
                for (int i = 0; i < trajectory.Points.Count; i++)
                {
                    var pointEntity = new TrajectoryPointEntity();
                    pointEntity.FromBusinessEntity(trajectory.Points[i]);
                    pointEntity.TrajectoryId = entity.Id;
                    pointEntity.Order = i;
                    pointEntities.Add(pointEntity);
                }
                
                // 使用XCode的批量插入功能
                TrajectoryPointEntity.BatchInsert(pointEntities);
            }
            
            trans.Commit();
            
            return entity.ToBusinessEntity();
        });
    }

    public async Task<Trajectory> UpdateTrajectoryAsync(Trajectory trajectory)
    {
        return await Task.Run(() =>
        {
            using var trans = TrajectoryEntity.Meta.CreateTrans();
            
            var entity = TrajectoryEntity.FindById(trajectory.Id.ToString());
            if (entity == null)
                throw new ArgumentException("轨迹不存在");

            entity.FromBusinessEntity(trajectory);
            entity.Update();

            // 删除旧的轨迹点
            TrajectoryPointEntity.Delete(TrajectoryPointEntity._.TrajectoryId == trajectory.Id.ToString());

            // 插入新的轨迹点
            if (trajectory.Points?.Count > 0)
            {
                var pointEntities = new List<TrajectoryPointEntity>();
                for (int i = 0; i < trajectory.Points.Count; i++)
                {
                    var pointEntity = new TrajectoryPointEntity();
                    pointEntity.FromBusinessEntity(trajectory.Points[i]);
                    pointEntity.TrajectoryId = entity.Id;
                    pointEntity.Order = i;
                    pointEntities.Add(pointEntity);
                }
                
                TrajectoryPointEntity.BatchInsert(pointEntities);
            }
            
            trans.Commit();
            
            return entity.ToBusinessEntity();
        });
    }

    public async Task<bool> DeleteTrajectoryAsync(Guid id)
    {
        return await Task.Run(() =>
        {
            // 停止播放（如果正在播放）
            StopTrajectoryAsync(id).Wait();

            var entity = TrajectoryEntity.FindById(id.ToString());
            if (entity == null) return false;

            // XCode会自动处理级联删除
            return entity.Delete() > 0;
        });
    }

    public async Task<bool> ClearChannelTrajectoriesAsync(Guid channelId)
    {
        return await Task.Run(() =>
        {
            // 停止所有相关轨迹的播放
            var trajectories = TrajectoryEntity.FindByChannelId(channelId.ToString());
            foreach (var trajectory in trajectories)
            {
                StopTrajectoryAsync(Guid.Parse(trajectory.Id)).Wait();
            }

            // 批量删除，提升性能
            var count = TrajectoryEntity.DeleteByChannelId(channelId.ToString());
            return count >= 0;
        });
    }

    public async Task<bool> PlayTrajectoryAsync(Guid trajectoryId, bool enableZAxis = false, double minZ = 0, double maxZ = 100)
    {
        var trajectory = await GetTrajectoryByIdAsync(trajectoryId);
        if (trajectory == null || trajectory.Points?.Count == 0)
            return false;

        // 停止之前的播放
        await StopTrajectoryAsync(trajectoryId);

        var cancellationTokenSource = new CancellationTokenSource();
        _playbackTokens[trajectoryId] = cancellationTokenSource;
        _playbackStatuses[trajectoryId] = PlaybackStatus.Playing;

        // 在后台任务中播放轨迹 - 音频灯光联动优化
        _ = Task.Run(async () =>
        {
            try
            {
                await PlayTrajectoryInternalAsync(trajectory, cancellationTokenSource.Token, enableZAxis, minZ, maxZ);
            }
            catch (OperationCanceledException)
            {
                // 播放被取消
            }
            catch (Exception ex)
            {
                Console.WriteLine($"轨迹播放异常: {ex.Message}");
            }
            finally
            {
                _playbackStatuses[trajectoryId] = PlaybackStatus.Stopped;
                _playbackTokens.Remove(trajectoryId);
            }
        });

        return true;
    }

    public async Task<bool> StopTrajectoryAsync(Guid trajectoryId)
    {
        if (_playbackTokens.TryGetValue(trajectoryId, out var tokenSource))
        {
            tokenSource.Cancel();
            _playbackTokens.Remove(trajectoryId);
        }

        _playbackStatuses[trajectoryId] = PlaybackStatus.Stopped;
        return await Task.FromResult(true);
    }

    public async Task<bool> StopAllPlaybackAsync()
    {
        var trajectoryIds = _playbackTokens.Keys.ToList();
        
        foreach (var trajectoryId in trajectoryIds)
        {
            if (_playbackTokens.TryGetValue(trajectoryId, out var tokenSource))
            {
                tokenSource.Cancel();
                _playbackTokens.Remove(trajectoryId);
            }
            _playbackStatuses[trajectoryId] = PlaybackStatus.Stopped;
        }
        
        Console.WriteLine($"已停止所有轨迹播放，共停止 {trajectoryIds.Count} 个轨迹");
        return await Task.FromResult(true);
    }

    public async Task<PlaybackStatus> GetPlaybackStatusAsync(Guid trajectoryId)
    {
        return await Task.FromResult(_playbackStatuses.TryGetValue(trajectoryId, out var status) ? status : PlaybackStatus.Stopped);
    }

    /// <summary>
    /// 内部轨迹播放方法 - 针对音频灯光联动优化
    /// </summary>
    /// <param name="trajectory">轨迹对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="enableZAxis">是否启用Z轴</param>
    /// <param name="minZ">Z轴最小值</param>
    /// <param name="maxZ">Z轴最大值</param>
    /// <returns></returns>
    private async Task PlayTrajectoryInternalAsync(Trajectory trajectory, CancellationToken cancellationToken, 
        bool enableZAxis = false, double minZ = 0, double maxZ = 100)
    {
        // 获取场景和通道信息
        var channelEntity = ChannelEntity.FindById(trajectory.ChannelId.ToString());
        if (channelEntity == null) return;

        var sceneEntity = SceneEntity.FindById(channelEntity.SceneId);
        if (sceneEntity == null) return;

        // 创建随机数生成器（用于Z轴随机值）
        Random random = new Random();
        
        // 使用通道的Z轴设置覆盖传入的参数
        bool useZAxis = channelEntity.UseZAxis;
        double zAxisMin = channelEntity.ZAxisMin;
        double zAxisMax = channelEntity.ZAxisMax;
        
        // 如果外部明确指定了启用Z轴，则使用外部参数
        if (enableZAxis)
        {
            useZAxis = true;
            // 只有当外部提供了有效的Z轴范围时才覆盖通道的Z轴范围
            if (minZ != 0 || maxZ != 100)
            {
                zAxisMin = minZ;
                zAxisMax = maxZ;
            }
        }
        
        Console.WriteLine($"轨迹播放Z轴设置 - 使用Z轴: {useZAxis}, Z轴范围: {zAxisMin}-{zAxisMax}");

        do
        {
            var startTime = DateTime.Now;

            // 按时间戳顺序播放轨迹点
            var sortedPoints = trajectory.Points.OrderBy(p => p.Timestamp).ToList();

            foreach (var point in sortedPoints)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                // 检查通信服务是否允许发送数据
                if (!_communicationService.IsAllowedToSend())
                {
                    Console.WriteLine("通信服务已停止，终止轨迹播放");
                    return;
                }

                // 转换为物理坐标
                var physicalX = (point.X - sceneEntity.OriginX) * sceneEntity.PixelToMeterRatio;
                var physicalY = (sceneEntity.OriginY - point.Y) * sceneEntity.PixelToMeterRatio;
                physicalX = Math.Round(physicalX, 3);
                physicalY = Math.Round(physicalY, 3);

                // 创建坐标数据
                var coordinateData = new CoordinateData
                {
                    ChannelNumber = channelEntity.ChannelNumber,
                    ChannelAddress = channelEntity.Address, // 使用通道的地址
                    X = physicalX,
                    Y = physicalY,
                    Timestamp = DateTime.Now
                };
                
                // 如果启用Z轴，在通道的Z轴范围内生成随机Z值
                if (useZAxis)
                {
                    coordinateData.Z = Math.Round(zAxisMin + random.NextDouble() * (zAxisMax - zAxisMin), 2);
                }
                else
                {
                    coordinateData.Z = 0; // 使用轨迹点中的Z值
                }
                
                // 处理 channelAddress 为 null 的情况
                if (string.IsNullOrEmpty(coordinateData.ChannelAddress))
                {
                    coordinateData.ChannelAddress = coordinateData.ChannelNumber.ToString();
                }

                // 发送坐标数据 - 音频灯光联动
                await _communicationService.SendCoordinateDataAsync(
                    sceneEntity.ServerIp, sceneEntity.ServerPort, coordinateData);

                // 记录日志
                Console.WriteLine($"发送轨迹点: {coordinateData.GetFormattedCoordinateString()}, 启用Z轴: {useZAxis}, Z范围: {zAxisMin}-{zAxisMax}");

                // 精确时间控制 - 确保音频灯光同步
                var elapsedTime = (DateTime.Now - startTime).TotalSeconds;
                var waitTime = point.Timestamp - elapsedTime;
                if (waitTime > 0)
                {
                    await Task.Delay(TimeSpan.FromSeconds(waitTime), cancellationToken);
                }
            }

            // 如果不是循环播放，退出循环
            if (!trajectory.IsLoop)
                break;

        } while (!cancellationToken.IsCancellationRequested);
    }
}