using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TrajectoryAuto.Core.Interfaces;

namespace TrajectoryAuto.Web.Controllers;

/// <summary>
/// 播放性能监控控制器
/// 提供性能统计、监控和优化建议
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PlaybackPerformanceController : ControllerBase
{
    private readonly ILogger<PlaybackPerformanceController> _logger;
    private readonly IPerformantPlaybackManager _performantPlaybackManager;
    
    public PlaybackPerformanceController(
        ILogger<PlaybackPerformanceController> logger,
        IPerformantPlaybackManager performantPlaybackManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _performantPlaybackManager = performantPlaybackManager ?? throw new ArgumentNullException(nameof(performantPlaybackManager));
    }
    
    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计数据</returns>
    [HttpGet("stats")]
    public IActionResult GetPerformanceStats()
    {
        try
        {
            var stats = _performantPlaybackManager.GetPerformanceStats();
            var taskStats = _performantPlaybackManager.GetTaskStats();
            
            // 计算性能评级
            var performanceGrade = CalculatePerformanceGrade(stats, taskStats);
            
            return Ok(new
            {
                success = true,
                performanceStats = stats,
                taskStats = taskStats,
                performanceGrade = performanceGrade,
                recommendations = GetPerformanceRecommendations(stats, taskStats)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取性能统计失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"获取性能统计失败: {ex.Message}" });
        }
    }
    
    /// <summary>
    /// 手动清理过期状态
    /// </summary>
    /// <returns>清理结果</returns>
    [HttpPost("cleanup")]
    public async Task<IActionResult> ManualCleanup()
    {
        try
        {
            var cleanedCount = await _performantPlaybackManager.CleanupExpiredStatesAsync();
            
            return Ok(new
            {
                success = true,
                message = $"清理完成，清理了 {cleanedCount} 个过期状态",
                cleanedCount = cleanedCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"手动清理失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"手动清理失败: {ex.Message}" });
        }
    }
    
    /// <summary>
    /// 获取系统资源使用情况
    /// </summary>
    /// <returns>系统资源信息</returns>
    [HttpGet("system-resources")]
    public IActionResult GetSystemResources()
    {
        try
        {
            var process = System.Diagnostics.Process.GetCurrentProcess();
            
            return Ok(new
            {
                success = true,
                systemResources = new
                {
                    memoryUsageMB = process.WorkingSet64 / 1024.0 / 1024.0,
                    privateMemoryMB = process.PrivateMemorySize64 / 1024.0 / 1024.0,
                    virtualMemoryMB = process.VirtualMemorySize64 / 1024.0 / 1024.0,
                    threadCount = process.Threads.Count,
                    handleCount = process.HandleCount,
                    cpuTimeMs = process.TotalProcessorTime.TotalMilliseconds,
                    startTime = process.StartTime,
                    uptime = DateTime.Now - process.StartTime,
                    threadPoolInfo = new
                    {
                        workerThreads = GetWorkerThreadCount(),
                        completionPortThreads = GetCompletionPortThreadCount(),
                        pendingWorkItems = ThreadPool.PendingWorkItemCount,
                        threadPoolThreadCount = ThreadPool.ThreadCount
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取系统资源信息失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"获取系统资源信息失败: {ex.Message}" });
        }
    }
    
    /// <summary>
    /// 获取性能建议
    /// </summary>
    /// <returns>性能优化建议</returns>
    [HttpGet("recommendations")]
    public IActionResult GetPerformanceRecommendations()
    {
        try
        {
            var stats = _performantPlaybackManager.GetPerformanceStats();
            var taskStats = _performantPlaybackManager.GetTaskStats();
            var recommendations = GetPerformanceRecommendations(stats, taskStats);
            
            return Ok(new
            {
                success = true,
                recommendations = recommendations,
                performanceGrade = CalculatePerformanceGrade(stats, taskStats)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取性能建议失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"获取性能建议失败: {ex.Message}" });
        }
    }
    
    #region 私有方法
    
    private string CalculatePerformanceGrade(PlaybackPerformanceStats stats, PlaybackTaskStats taskStats)
    {
        int score = 100;
        
        // 内存使用评分
        if (stats.MemoryUsageMB > 1000) score -= 20;
        else if (stats.MemoryUsageMB > 500) score -= 10;
        
        // 线程数量评分
        if (stats.ActiveThreadCount > 100) score -= 15;
        else if (stats.ActiveThreadCount > 50) score -= 8;
        
        // UDP延迟评分
        if (stats.AverageUdpLatencyMs > 100) score -= 15;
        else if (stats.AverageUdpLatencyMs > 50) score -= 8;
        
        // 任务数量评分
        if (taskStats.TrajectoryTaskCount > 200) score -= 10;
        else if (taskStats.TrajectoryTaskCount > 100) score -= 5;
        
        // 线程池队列评分
        if (taskStats.ThreadPoolQueueLength > 1000) score -= 10;
        else if (taskStats.ThreadPoolQueueLength > 500) score -= 5;
        
        return score switch
        {
            >= 90 => "优秀",
            >= 80 => "良好",
            >= 70 => "一般",
            >= 60 => "较差",
            _ => "危险"
        };
    }
    
    private object[] GetPerformanceRecommendations(PlaybackPerformanceStats stats, PlaybackTaskStats taskStats)
    {
        var recommendations = new List<object>();
        
        // 内存使用建议
        if (stats.MemoryUsageMB > 1000)
        {
            recommendations.Add(new
            {
                type = "memory",
                level = "warning",
                message = "内存使用过高，建议减少并发播放的场景数量或增加清理频率",
                currentValue = $"{stats.MemoryUsageMB:F1} MB",
                threshold = "1000 MB"
            });
        }
        
        // 线程数量建议
        if (stats.ActiveThreadCount > 100)
        {
            recommendations.Add(new
            {
                type = "threads",
                level = "warning",
                message = "活跃线程数过多，建议优化并发策略或使用线程池限制",
                currentValue = stats.ActiveThreadCount,
                threshold = 100
            });
        }
        
        // UDP延迟建议
        if (stats.AverageUdpLatencyMs > 100)
        {
            recommendations.Add(new
            {
                type = "udp_latency",
                level = "error",
                message = "UDP发送延迟过高，检查网络连接或减少发送频率",
                currentValue = $"{stats.AverageUdpLatencyMs:F1} ms",
                threshold = "100 ms"
            });
        }
        
        // 任务数量建议
        if (taskStats.TrajectoryTaskCount > 200)
        {
            recommendations.Add(new
            {
                type = "task_count",
                level = "warning",
                message = "并发轨迹任务过多，建议分批播放或增加播放间隔",
                currentValue = taskStats.TrajectoryTaskCount,
                threshold = 200
            });
        }
        
        // 线程池队列建议
        if (taskStats.ThreadPoolQueueLength > 1000)
        {
            recommendations.Add(new
            {
                type = "thread_pool",
                level = "error",
                message = "线程池队列积压严重，系统可能出现性能瓶颈",
                currentValue = taskStats.ThreadPoolQueueLength,
                threshold = 1000
            });
        }
        
        // 清理建议
        if (taskStats.PendingCleanupCount > 50)
        {
            recommendations.Add(new
            {
                type = "cleanup",
                level = "info",
                message = "建议执行手动清理以释放资源",
                currentValue = taskStats.PendingCleanupCount,
                action = "POST /api/playbackperformance/cleanup"
            });
        }
        
        // 如果没有问题，给出积极建议
        if (recommendations.Count == 0)
        {
            recommendations.Add(new
            {
                type = "general",
                level = "success",
                message = "系统性能良好，建议继续监控关键指标",
                suggestion = "定期检查性能统计以确保持续稳定运行"
            });
        }
        
        return recommendations.ToArray();
    }
    
    private int GetWorkerThreadCount()
    {
        ThreadPool.GetAvailableThreads(out int workerThreads, out _);
        ThreadPool.GetMaxThreads(out int maxWorkerThreads, out _);
        return maxWorkerThreads - workerThreads;
    }
    
    private int GetCompletionPortThreadCount()
    {
        ThreadPool.GetAvailableThreads(out _, out int completionPortThreads);
        ThreadPool.GetMaxThreads(out _, out int maxCompletionPortThreads);
        return maxCompletionPortThreads - completionPortThreads;
    }
    
    #endregion
}
