namespace TrajectoryAuto.Core.Entities;

/// <summary>
/// 场景实体
/// </summary>
public class Scene
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    /// <summary>
    /// 场景名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 场景宽度（像素）
    /// </summary>
    public int Width { get; set; }
    
    /// <summary>
    /// 场景高度（像素）
    /// </summary>
    public int Height { get; set; }
    
    /// <summary>
    /// 服务器IP地址
    /// </summary>
    public string ServerIp { get; set; } = string.Empty;
    
    /// <summary>
    /// 服务器端口
    /// </summary>
    public int ServerPort { get; set; }
    
    /// <summary>
    /// 背景图片路径
    /// </summary>
    public string? BackgroundImagePath { get; set; }
    
    /// <summary>
    /// 坐标原点X坐标（像素）
    /// </summary>
    public double OriginX { get; set; }
    
    /// <summary>
    /// 坐标原点Y坐标（像素）
    /// </summary>
    public double OriginY { get; set; }
    
    /// <summary>
    /// 像素到米的转换比例
    /// </summary>
    public double PixelToMeterRatio { get; set; } = 0.01;
    
    /// <summary>
    /// X轴缩放比例
    /// </summary>
    public double ScaleX { get; set; } = 1.0;
    
    /// <summary>
    /// Y轴缩放比例
    /// </summary>
    public double ScaleY { get; set; } = 1.0;
    
    /// <summary>
    /// Y轴是否反转
    /// </summary>
    public bool InvertY { get; set; } = false;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 场景中的通道列表
    /// </summary>
    public List<Channel> Channels { get; set; } = new();
}