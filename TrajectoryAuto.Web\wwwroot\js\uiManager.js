class UIManager {
    constructor(app) {
        this.app = app;
        this.initializeEventListeners();
    }

    // 初始化所有事件监听器
    initializeEventListeners() {
        // 场景相关事件
        document.getElementById('createSceneBtn')?.addEventListener('click', () => {
            this.app.sceneManager?.showSceneModal();
        });

        // 场景列表点击事件
        document.getElementById('sceneList')?.addEventListener('click', (e) => {
            const sceneItem = e.target.closest('.scene-item');
            if (sceneItem) {
                this.handleSceneItemClick(sceneItem);
            }
        });

        // 场景操作按钮
        document.getElementById('editSceneBtn')?.addEventListener('click', () => {
            if (this.app.sceneManager?.selectedSceneId) {
                this.app.sceneManager.editScene(this.app.sceneManager.selectedSceneId);
            }
        });

        document.getElementById('deleteSceneBtn')?.addEventListener('click', () => {
            if (this.app.sceneManager?.selectedSceneId) {
                if (confirm('确定要删除这个场景吗？此操作不可恢复。')) {
                    this.app.sceneManager.deleteScene(this.app.sceneManager.selectedSceneId);
                }
            }
        });

        // 通道相关事件
        document.getElementById('createChannelBtn')?.addEventListener('click', () => {
            this.app.channelManager?.showChannelModal();
        });

        // 通道列表点击事件
        document.getElementById('channelList')?.addEventListener('click', (e) => {
            const channelItem = e.target.closest('.channel-item');
            if (channelItem) {
                this.handleChannelItemClick(channelItem);
            }
        });

        // 通道操作按钮
        document.getElementById('editChannelBtn')?.addEventListener('click', () => {
            if (this.app.channelManager?.selectedChannelId) {
                this.app.channelManager.editChannel(this.app.channelManager.selectedChannelId);
            }
        });

        document.getElementById('deleteChannelBtn')?.addEventListener('click', () => {
            if (this.app.channelManager?.selectedChannelId) {
                this.app.channelManager.deleteChannel(this.app.channelManager.selectedChannelId);
            }
        });

        // 模态框关闭按钮
        document.querySelectorAll('.modal .close, .modal .btn-cancel').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // 保存按钮
        document.getElementById('sceneSaveBtn')?.addEventListener('click', async (e) => {
            e.preventDefault();
            if (this.app.sceneManager) {
                await this.app.sceneManager.saveScene();
            }
        });

        document.getElementById('channelSaveBtn')?.addEventListener('click', async (e) => {
            e.preventDefault();
            if (this.app.channelManager) {
                await this.app.channelManager.saveChannel();
            }
        });

        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    // 处理场景项点击
    async handleSceneItemClick(sceneItem) {
        // 移除其他项的选中状态
        document.querySelectorAll('.scene-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // 添加当前项的选中状态
        sceneItem.classList.add('selected');
        
        // 显示操作按钮
        document.getElementById('sceneActions').style.display = 'flex';
        
        // 存储当前选中的场景ID
        const sceneId = sceneItem.dataset.id;
        if (this.app.sceneManager) {
            this.app.sceneManager.selectedSceneId = sceneId;
            // 查找并选择对应的场景
            const scene = this.app.sceneManager.scenes.find(s => s.id == sceneId);
            if (scene) {
                // 确保设置currentScene
                this.app.sceneManager.currentScene = scene;
                await this.app.sceneManager.selectScene(scene);
            }
        }
    }

    // 处理通道项点击
    async handleChannelItemClick(channelItem) {
        // 移除其他项的选中状态
        document.querySelectorAll('.channel-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // 添加当前项的选中状态
        channelItem.classList.add('selected');
        
        // 显示操作按钮
        document.getElementById('channelActions').style.display = 'flex';
        
        // 存储当前选中的通道ID
        const channelId = channelItem.dataset.id;
        if (this.app.channelManager) {
            this.app.channelManager.selectedChannelId = channelId;
            // 查找并选择对应的通道
            const channel = this.app.channelManager.channels.find(c => c.id == channelId);
            if (channel) {
                await this.app.channelManager.selectChannel(channel);
            }
        }
    }
}
