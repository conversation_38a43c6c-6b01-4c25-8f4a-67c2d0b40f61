// 轨迹自动化系统 - 站点通用JavaScript
(function() {
    'use strict';

    // 页面加载完成后初始化
    $(document).ready(function() {
        console.log('轨迹自动化系统已加载');
        
        // 初始化工具提示
        initializeTooltips();
        
        // 初始化表单验证
        initializeFormValidation();
        
        // 初始化通用事件处理
        initializeCommonEvents();
    });

    // 初始化工具提示
    function initializeTooltips() {
        $('[data-bs-toggle="tooltip"]').each(function() {
            // 简单的工具提示实现
            const element = this;
            const title = element.getAttribute('title') || element.getAttribute('data-bs-title');
            
            if (title) {
                element.addEventListener('mouseenter', function() {
                    showTooltip(element, title);
                });
                
                element.addEventListener('mouseleave', function() {
                    hideTooltip(element);
                });
            }
        });
    }

    // 显示工具提示
    function showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip bs-tooltip-top show';
        tooltip.innerHTML = `
            <div class="tooltip-arrow"></div>
            <div class="tooltip-inner">${text}</div>
        `;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.position = 'absolute';
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        tooltip.style.zIndex = '1070';
        
        element._tooltip = tooltip;
    }

    // 隐藏工具提示
    function hideTooltip(element) {
        if (element._tooltip) {
            element._tooltip.remove();
            element._tooltip = null;
        }
    }

    // 初始化表单验证
    function initializeFormValidation() {
        $('form').on('submit', function(e) {
            const form = this;
            let isValid = true;
            
            // 检查必填字段
            $(form).find('[required]').each(function() {
                const field = this;
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            });
            
            // 检查数字字段
            $(form).find('input[type="number"]').each(function() {
                const field = this;
                const value = parseFloat(field.value);
                const min = parseFloat(field.getAttribute('min'));
                const max = parseFloat(field.getAttribute('max'));
                
                if (field.value && (isNaN(value) || 
                    (min !== null && value < min) || 
                    (max !== null && value > max))) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else if (field.value) {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showAlert('请检查表单中的错误信息', 'danger');
            }
        });
    }

    // 初始化通用事件处理
    function initializeCommonEvents() {
        // 确认删除对话框
        $('[data-confirm]').on('click', function(e) {
            const message = this.getAttribute('data-confirm') || '确定要执行此操作吗？';
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
        
        // 自动隐藏警告消息
        $('.alert[data-auto-dismiss]').each(function() {
            const alert = this;
            const delay = parseInt(alert.getAttribute('data-auto-dismiss')) || 5000;
            
            setTimeout(function() {
                $(alert).fadeOut(function() {
                    alert.remove();
                });
            }, delay);
        });
    }

    // 显示警告消息
    function showAlert(message, type = 'info', container = '.alert-container') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const containerElement = $(container);
        if (containerElement.length) {
            containerElement.append(alertHtml);
        } else {
            // 如果没有容器，在页面顶部创建一个
            $('body').prepend(`<div class="alert-container">${alertHtml}</div>`);
        }
        
        // 自动隐藏
        setTimeout(function() {
            $('.alert').last().fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // 格式化数字
    function formatNumber(num, decimals = 2) {
        return parseFloat(num).toFixed(decimals);
    }

    // 格式化日期
    function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    // 防抖函数
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // 节流函数
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 导出到全局作用域
    window.TrajectoryUtils = {
        showAlert: showAlert,
        formatNumber: formatNumber,
        formatDate: formatDate,
        debounce: debounce,
        throttle: throttle
    };

})();