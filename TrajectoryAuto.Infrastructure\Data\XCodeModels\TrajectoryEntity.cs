using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using NewLife.Data;
using XCode;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace TrajectoryAuto.Infrastructure.Data.XCodeModels;

/// <summary>轨迹实体 - 音频灯光联动实时性能优化</summary>
[BindTable("Trajectories", Description = "轨迹表", ConnName = "TrajectoryAuto", DbType = DatabaseType.SQLite)]
public partial class TrajectoryEntity : Entity<TrajectoryEntity>
{
    #region 属性
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 50)]
    [BindColumn("Id", "编号", "")]
    public String Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }
    private String _Id = "";

    /// <summary>轨迹名称</summary>
    [DisplayName("轨迹名称")]
    [Description("轨迹名称")]
    [DataObjectField(false, false, false, 100)]
    [BindColumn("Name", "轨迹名称", "", Master = true)]
    public String Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }
    private String _Name = "";

    /// <summary>轨迹类型</summary>
    [DisplayName("轨迹类型")]
    [Description("轨迹类型")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Type", "轨迹类型", "")]
    public Int32 Type { get => _Type; set { if (OnPropertyChanging("Type", value)) { _Type = value; OnPropertyChanged("Type"); } } }
    private Int32 _Type;

    /// <summary>持续时间</summary>
    [DisplayName("持续时间")]
    [Description("持续时间")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Duration", "持续时间", "")]
    public Double Duration { get => _Duration; set { if (OnPropertyChanging("Duration", value)) { _Duration = value; OnPropertyChanged("Duration"); } } }
    private Double _Duration;

    /// <summary>是否循环</summary>
    [DisplayName("是否循环")]
    [Description("是否循环")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsLoop", "是否循环", "")]
    public Boolean IsLoop { get => _IsLoop; set { if (OnPropertyChanging("IsLoop", value)) { _IsLoop = value; OnPropertyChanged("IsLoop"); } } }
    private Boolean _IsLoop;
    
    /// <summary>循环播放次数</summary>
    [DisplayName("循环播放次数")]
    [Description("循环播放次数（0表示无限循环）")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LoopCount", "循环播放次数", "")]
    public Int32 LoopCount { get => _LoopCount; set { if (OnPropertyChanging("LoopCount", value)) { _LoopCount = value; OnPropertyChanged("LoopCount"); } } }
    private Int32 _LoopCount = 0;
    
    /// <summary>是否逆向播放</summary>
    [DisplayName("是否逆向播放")]
    [Description("是否逆向播放")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsReverse", "是否逆向播放", "")]
    public Boolean IsReverse { get => _IsReverse; set { if (OnPropertyChanging("IsReverse", value)) { _IsReverse = value; OnPropertyChanged("IsReverse"); } } }
    private Boolean _IsReverse = false;
    
    /// <summary>是否使用Z轴</summary>
    [DisplayName("是否使用Z轴")]
    [Description("是否使用Z轴")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UseZAxis", "是否使用Z轴", "")]
    public Boolean UseZAxis { get => _UseZAxis; set { if (OnPropertyChanging("UseZAxis", value)) { _UseZAxis = value; OnPropertyChanged("UseZAxis"); } } }
    private Boolean _UseZAxis = false;
    
    /// <summary>Z轴数据范围最小值</summary>
    [DisplayName("Z轴数据范围最小值")]
    [Description("Z轴数据范围最小值")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ZAxisMin", "Z轴数据范围最小值", "")]
    public Double ZAxisMin { get => _ZAxisMin; set { if (OnPropertyChanging("ZAxisMin", value)) { _ZAxisMin = value; OnPropertyChanged("ZAxisMin"); } } }
    private Double _ZAxisMin = 0.0;
    
    /// <summary>Z轴数据范围最大值</summary>
    [DisplayName("Z轴数据范围最大值")]
    [Description("Z轴数据范围最大值")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ZAxisMax", "Z轴数据范围最大值", "")]
    public Double ZAxisMax { get => _ZAxisMax; set { if (OnPropertyChanging("ZAxisMax", value)) { _ZAxisMax = value; OnPropertyChanged("ZAxisMax"); } } }
    private Double _ZAxisMax = 1.0;

    /// <summary>通道编号</summary>
    [DisplayName("通道编号")]
    [Description("通道编号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("ChannelId", "通道编号", "")]
    public String ChannelId { get => _ChannelId; set { if (OnPropertyChanging("ChannelId", value)) { _ChannelId = value; OnPropertyChanged("ChannelId"); } } }
    private String _ChannelId = "";

    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }
    private DateTime _CreateTime;

    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }
    private DateTime _UpdateTime;
    
    /// <summary>线条颜色</summary>
    [DisplayName("线条颜色")]
    [Description("线条颜色（十六进制）")]
    [DataObjectField(false, false, false, 20)]
    [BindColumn("LineColor", "线条颜色", "")]
    public String LineColor { get => _LineColor; set { if (OnPropertyChanging("LineColor", value)) { _LineColor = value; OnPropertyChanged("LineColor"); } } }
    private String _LineColor = "#000000";
    
    /// <summary>线条宽度</summary>
    [DisplayName("线条宽度")]
    [Description("线条宽度（像素）")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LineWidth", "线条宽度", "")]
    public Int32 LineWidth { get => _LineWidth; set { if (OnPropertyChanging("LineWidth", value)) { _LineWidth = value; OnPropertyChanged("LineWidth"); } } }
    private Int32 _LineWidth = 2;
    
    /// <summary>线条样式</summary>
    [DisplayName("线条样式")]
    [Description("线条样式（实线、虚线、点线等）")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LineStyle", "线条样式", "")]
    public Int32 LineStyle { get => _LineStyle; set { if (OnPropertyChanging("LineStyle", value)) { _LineStyle = value; OnPropertyChanged("LineStyle"); } } }
    private Int32 _LineStyle = 1;
    
    /// <summary>线条透明度</summary>
    [DisplayName("线条透明度")]
    [Description("线条透明度（0-1）")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("LineOpacity", "线条透明度", "")]
    public Double LineOpacity { get => _LineOpacity; set { if (OnPropertyChanging("LineOpacity", value)) { _LineOpacity = value; OnPropertyChanged("LineOpacity"); } } }
    private Double _LineOpacity = 1.0;
    
    /// <summary>是否显示轨迹点</summary>
    [DisplayName("是否显示轨迹点")]
    [Description("是否显示轨迹点")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ShowPoints", "是否显示轨迹点", "")]
    public Boolean ShowPoints { get => _ShowPoints; set { if (OnPropertyChanging("ShowPoints", value)) { _ShowPoints = value; OnPropertyChanged("ShowPoints"); } } }
    private Boolean _ShowPoints = true;
    
    /// <summary>轨迹点大小</summary>
    [DisplayName("轨迹点大小")]
    [Description("轨迹点大小（像素）")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PointSize", "轨迹点大小", "")]
    public Int32 PointSize { get => _PointSize; set { if (OnPropertyChanging("PointSize", value)) { _PointSize = value; OnPropertyChanged("PointSize"); } } }
    private Int32 _PointSize = 4;
    
    /// <summary>轨迹点颜色</summary>
    [DisplayName("轨迹点颜色")]
    [Description("轨迹点颜色（十六进制）")]
    [DataObjectField(false, false, false, 20)]
    [BindColumn("PointColor", "轨迹点颜色", "")]
    public String PointColor { get => _PointColor; set { if (OnPropertyChanging("PointColor", value)) { _PointColor = value; OnPropertyChanged("PointColor"); } } }
    private String _PointColor = "#FF0000";
    
    /// <summary>是否显示起点和终点标记</summary>
    [DisplayName("是否显示起点和终点标记")]
    [Description("是否显示起点和终点标记")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ShowEndpoints", "是否显示起点和终点标记", "")]
    public Boolean ShowEndpoints { get => _ShowEndpoints; set { if (OnPropertyChanging("ShowEndpoints", value)) { _ShowEndpoints = value; OnPropertyChanged("ShowEndpoints"); } } }
    private Boolean _ShowEndpoints = true;
    
    /// <summary>起点标记颜色</summary>
    [DisplayName("起点标记颜色")]
    [Description("起点标记颜色（十六进制）")]
    [DataObjectField(false, false, false, 20)]
    [BindColumn("StartPointColor", "起点标记颜色", "")]
    public String StartPointColor { get => _StartPointColor; set { if (OnPropertyChanging("StartPointColor", value)) { _StartPointColor = value; OnPropertyChanged("StartPointColor"); } } }
    private String _StartPointColor = "#00FF00";
    
    /// <summary>终点标记颜色</summary>
    [DisplayName("终点标记颜色")]
    [Description("终点标记颜色（十六进制）")]
    [DataObjectField(false, false, false, 20)]
    [BindColumn("EndPointColor", "终点标记颜色", "")]
    public String EndPointColor { get => _EndPointColor; set { if (OnPropertyChanging("EndPointColor", value)) { _EndPointColor = value; OnPropertyChanged("EndPointColor"); } } }
    private String _EndPointColor = "#FF0000";
    
    /// <summary>是否启用阴影效果</summary>
    [DisplayName("是否启用阴影效果")]
    [Description("是否启用阴影效果")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("EnableShadow", "是否启用阴影效果", "")]
    public Boolean EnableShadow { get => _EnableShadow; set { if (OnPropertyChanging("EnableShadow", value)) { _EnableShadow = value; OnPropertyChanged("EnableShadow"); } } }
    private Boolean _EnableShadow = false;
    
    /// <summary>阴影颜色</summary>
    [DisplayName("阴影颜色")]
    [Description("阴影颜色（十六进制）")]
    [DataObjectField(false, false, false, 30)]
    [BindColumn("ShadowColor", "阴影颜色", "")]
    public String ShadowColor { get => _ShadowColor; set { if (OnPropertyChanging("ShadowColor", value)) { _ShadowColor = value; OnPropertyChanged("ShadowColor"); } } }
    private String _ShadowColor = "rgba(0,0,0,0.5)";
    
    /// <summary>阴影模糊度</summary>
    [DisplayName("阴影模糊度")]
    [Description("阴影模糊度（像素）")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ShadowBlur", "阴影模糊度", "")]
    public Int32 ShadowBlur { get => _ShadowBlur; set { if (OnPropertyChanging("ShadowBlur", value)) { _ShadowBlur = value; OnPropertyChanged("ShadowBlur"); } } }
    private Int32 _ShadowBlur = 5;
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get
        {
            switch (name)
            {
                case "Id": return _Id;
                case "Name": return _Name;
                case "Type": return _Type;
                case "Duration": return _Duration;
                case "IsLoop": return _IsLoop;
                case "LoopCount": return _LoopCount;
                case "IsReverse": return _IsReverse;
                case "UseZAxis": return _UseZAxis;
                case "ZAxisMin": return _ZAxisMin;
                case "ZAxisMax": return _ZAxisMax;
                case "ChannelId": return _ChannelId;
                case "CreateTime": return _CreateTime;
                case "UpdateTime": return _UpdateTime;
                case "LineColor": return _LineColor;
                case "LineWidth": return _LineWidth;
                case "LineStyle": return _LineStyle;
                case "LineOpacity": return _LineOpacity;
                case "ShowPoints": return _ShowPoints;
                case "PointSize": return _PointSize;
                case "PointColor": return _PointColor;
                case "ShowEndpoints": return _ShowEndpoints;
                case "StartPointColor": return _StartPointColor;
                case "EndPointColor": return _EndPointColor;
                case "EnableShadow": return _EnableShadow;
                case "ShadowColor": return _ShadowColor;
                case "ShadowBlur": return _ShadowBlur;
                default: return base[name];
            }
        }
        set
        {
            switch (name)
            {
                case "Id": _Id = Convert.ToString(value) ?? ""; break;
                case "Name": _Name = Convert.ToString(value) ?? ""; break;
                case "Type": _Type = Convert.ToInt32(value); break;
                case "Duration": _Duration = Convert.ToDouble(value); break;
                case "IsLoop": _IsLoop = Convert.ToBoolean(value); break;
                case "LoopCount": _LoopCount = Convert.ToInt32(value); break;
                case "IsReverse": _IsReverse = Convert.ToBoolean(value); break;
                case "UseZAxis": _UseZAxis = Convert.ToBoolean(value); break;
                case "ZAxisMin": _ZAxisMin = Convert.ToDouble(value); break;
                case "ZAxisMax": _ZAxisMax = Convert.ToDouble(value); break;
                case "ChannelId": _ChannelId = Convert.ToString(value) ?? ""; break;
                case "CreateTime": _CreateTime = Convert.ToDateTime(value); break;
                case "UpdateTime": _UpdateTime = Convert.ToDateTime(value); break;
                case "LineColor": _LineColor = Convert.ToString(value) ?? "#000000"; break;
                case "LineWidth": _LineWidth = Convert.ToInt32(value); break;
                case "LineStyle": _LineStyle = Convert.ToInt32(value); break;
                case "LineOpacity": _LineOpacity = Convert.ToDouble(value); break;
                case "ShowPoints": _ShowPoints = Convert.ToBoolean(value); break;
                case "PointSize": _PointSize = Convert.ToInt32(value); break;
                case "PointColor": _PointColor = Convert.ToString(value) ?? "#FF0000"; break;
                case "ShowEndpoints": _ShowEndpoints = Convert.ToBoolean(value); break;
                case "StartPointColor": _StartPointColor = Convert.ToString(value) ?? "#00FF00"; break;
                case "EndPointColor": _EndPointColor = Convert.ToString(value) ?? "#FF0000"; break;
                case "EnableShadow": _EnableShadow = Convert.ToBoolean(value); break;
                case "ShadowColor": _ShadowColor = Convert.ToString(value) ?? "rgba(0,0,0,0.5)"; break;
                case "ShadowBlur": _ShadowBlur = Convert.ToInt32(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 字段名
    /// <summary>取得轨迹实体字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>轨迹名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>轨迹类型</summary>
        public static readonly Field Type = FindByName("Type");

        /// <summary>持续时间</summary>
        public static readonly Field Duration = FindByName("Duration");

        /// <summary>是否循环</summary>
        public static readonly Field IsLoop = FindByName("IsLoop");
        
        /// <summary>循环播放次数</summary>
        public static readonly Field LoopCount = FindByName("LoopCount");
        
        /// <summary>是否逆向播放</summary>
        public static readonly Field IsReverse = FindByName("IsReverse");
        
        /// <summary>是否使用Z轴</summary>
        public static readonly Field UseZAxis = FindByName("UseZAxis");
        
        /// <summary>Z轴数据范围最小值</summary>
        public static readonly Field ZAxisMin = FindByName("ZAxisMin");
        
        /// <summary>Z轴数据范围最大值</summary>
        public static readonly Field ZAxisMax = FindByName("ZAxisMax");

        /// <summary>通道编号</summary>
        public static readonly Field ChannelId = FindByName("ChannelId");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");
        
        /// <summary>线条颜色</summary>
        public static readonly Field LineColor = FindByName("LineColor");
        
        /// <summary>线条宽度</summary>
        public static readonly Field LineWidth = FindByName("LineWidth");
        
        /// <summary>线条样式</summary>
        public static readonly Field LineStyle = FindByName("LineStyle");
        
        /// <summary>线条透明度</summary>
        public static readonly Field LineOpacity = FindByName("LineOpacity");
        
        /// <summary>是否显示轨迹点</summary>
        public static readonly Field ShowPoints = FindByName("ShowPoints");
        
        /// <summary>轨迹点大小</summary>
        public static readonly Field PointSize = FindByName("PointSize");
        
        /// <summary>轨迹点颜色</summary>
        public static readonly Field PointColor = FindByName("PointColor");
        
        /// <summary>是否显示起点和终点标记</summary>
        public static readonly Field ShowEndpoints = FindByName("ShowEndpoints");
        
        /// <summary>起点标记颜色</summary>
        public static readonly Field StartPointColor = FindByName("StartPointColor");
        
        /// <summary>终点标记颜色</summary>
        public static readonly Field EndPointColor = FindByName("EndPointColor");
        
        /// <summary>是否启用阴影效果</summary>
        public static readonly Field EnableShadow = FindByName("EnableShadow");
        
        /// <summary>阴影颜色</summary>
        public static readonly Field ShadowColor = FindByName("ShadowColor");
        
        /// <summary>阴影模糊度</summary>
        public static readonly Field ShadowBlur = FindByName("ShadowBlur");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }
    #endregion

    #region 对象操作
    static TrajectoryEntity()
    {
        Meta.Modules.Add<TimeModule>();
    }

    public override void Valid(Boolean isNew)
    {
        if (!HasDirty) return;
        base.Valid(isNew);

        if (String.IsNullOrEmpty(Name)) throw new ArgumentNullException(nameof(Name), "轨迹名称不能为空！");
        if (String.IsNullOrEmpty(ChannelId)) throw new ArgumentNullException(nameof(ChannelId), "通道ID不能为空！");
        if (Duration <= 0) throw new ArgumentException("轨迹时长必须大于0！");
    }
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static TrajectoryEntity? FindById(String id)
    {
        if (String.IsNullOrEmpty(id)) return null;
        return Find(_.Id == id);
    }

    /// <summary>根据通道ID查找轨迹</summary>
    public static IList<TrajectoryEntity> FindByChannelId(String channelId)
    {
        if (String.IsNullOrEmpty(channelId)) return new List<TrajectoryEntity>();
        return FindAll(_.ChannelId == channelId).OrderByDescending(e => e.CreateTime).ToList();
    }

    /// <summary>批量删除通道轨迹</summary>
    public static Int32 DeleteByChannelId(String channelId)
    {
        if (String.IsNullOrEmpty(channelId)) return 0;

        // 先删除轨迹点
        TrajectoryPointEntity.DeleteByTrajectoryChannelId(channelId);
        
        // 再删除轨迹
        return Delete(_.ChannelId == channelId);
    }
    #endregion

    #region 业务操作
    public TrajectoryAuto.Core.Entities.Trajectory ToBusinessEntity()
    {
        return new TrajectoryAuto.Core.Entities.Trajectory
        {
            Id = Guid.Parse(Id),
            Name = Name,
            Type = (TrajectoryAuto.Core.Entities.TrajectoryType)Type,
            Duration = Duration,
            IsLoop = IsLoop,
            LoopCount = LoopCount,
            IsReverse = IsReverse,
            UseZAxis = UseZAxis,
            ZAxisMin = ZAxisMin,
            ZAxisMax = ZAxisMax,
            ChannelId = Guid.Parse(ChannelId),
            CreatedAt = CreateTime,
            LineColor = LineColor,
            LineWidth = LineWidth,
            LineStyle = (TrajectoryAuto.Core.Entities.LineStyle)LineStyle,
            LineOpacity = LineOpacity,
            ShowPoints = ShowPoints,
            PointSize = PointSize,
            PointColor = PointColor,
            ShowEndpoints = ShowEndpoints,
            StartPointColor = StartPointColor,
            EndPointColor = EndPointColor,
            EnableShadow = EnableShadow,
            ShadowColor = ShadowColor,
            ShadowBlur = ShadowBlur
        };
    }

    public void FromBusinessEntity(TrajectoryAuto.Core.Entities.Trajectory trajectory)
    {
        Id = trajectory.Id.ToString();
        Name = trajectory.Name;
        Type = (Int32)trajectory.Type;
        Duration = trajectory.Duration;
        IsLoop = trajectory.IsLoop;
        LoopCount = trajectory.LoopCount;
        IsReverse = trajectory.IsReverse;
        UseZAxis = trajectory.UseZAxis;
        ZAxisMin = trajectory.ZAxisMin;
        ZAxisMax = trajectory.ZAxisMax;
        ChannelId = trajectory.ChannelId.ToString();
        LineColor = trajectory.LineColor;
        LineWidth = trajectory.LineWidth;
        LineStyle = (Int32)trajectory.LineStyle;
        LineOpacity = trajectory.LineOpacity;
        ShowPoints = trajectory.ShowPoints;
        PointSize = trajectory.PointSize;
        PointColor = trajectory.PointColor;
        ShowEndpoints = trajectory.ShowEndpoints;
        StartPointColor = trajectory.StartPointColor;
        EndPointColor = trajectory.EndPointColor;
        EnableShadow = trajectory.EnableShadow;
        ShadowColor = trajectory.ShadowColor;
        ShadowBlur = trajectory.ShadowBlur;
    }

    /// <summary>获取轨迹点位 - 延迟加载优化</summary>
    public IList<TrajectoryPointEntity> GetPoints()
    {
        return TrajectoryPointEntity.FindByTrajectoryId(Id);
    }
    #endregion
}