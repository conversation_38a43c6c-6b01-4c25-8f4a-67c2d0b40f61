namespace TrajectoryAuto.Core.Entities;

/// <summary>
/// 通道实体
/// </summary>
public class Channel
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; }
    
    /// <summary>
    /// 是否使用Z轴
    /// </summary>
    public bool UseZAxis { get; set; } = false;
    
    /// <summary>
    /// Z轴数据范围最小值
    /// </summary>
    public double ZAxisMin { get; set; } = 0.0;
    
    /// <summary>
    /// Z轴数据范围最大值
    /// </summary>
    public double ZAxisMax { get; set; } = 1.0;
    
    /// <summary>
    /// 所属场景ID
    /// </summary>
    public int ChannelNumber { get; set; }
    
    /// <summary>
    /// 通道地址
    /// </summary>
    public string Address { get; set; } = string.Empty;
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;
    
    /// <summary>
    /// 端口号
    /// </summary>
    public int Port { get; set; }
    
    /// <summary>
    /// 通道颜色（十六进制）
    /// </summary>
    public string Color { get; set; } = "#FF0000";
     
    
    /// <summary>
    /// 所属场景ID
    /// </summary>
    public Guid SceneId { get; set; }
    
    /// <summary>
    /// 所属场景
    /// </summary>
    public Scene? Scene { get; set; }
    
    /// <summary>
    /// 通道的轨迹列表
    /// </summary>
    public List<Trajectory> Trajectories { get; set; } = new();
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}