using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Infrastructure.Services;

/// <summary>
/// 高性能轨迹播放管理器实现
/// 使用Channel和有限并发来优化性能
/// </summary>
public class PerformantPlaybackManager : IPerformantPlaybackManager
{
    #region 私有字段
    
    private readonly ILogger<PerformantPlaybackManager> _logger;
    private readonly ICommunicationService _communicationService;
    private readonly IPlaybackStateService _playbackStateService;
    
    // 使用Channel进行高效的生产者-消费者模式
    private readonly Channel<TrajectoryPlaybackItem> _playbackChannel;
    private readonly ChannelWriter<TrajectoryPlaybackItem> _channelWriter;
    private readonly ChannelReader<TrajectoryPlaybackItem> _channelReader;
    
    // 限制并发数量，避免线程池耗尽
    private readonly SemaphoreSlim _concurrencyLimiter;
    private readonly int _maxConcurrentTasks;
    
    // 播放任务管理
    private readonly ConcurrentDictionary<Guid, PlaybackTaskInfo> _scenePlaybackTasks = new();
    private readonly ConcurrentDictionary<Guid, PlaybackTaskInfo> _channelPlaybackTasks = new();
    private readonly ConcurrentDictionary<Guid, CancellationTokenSource> _cancellationTokens = new();
    
    // 性能统计
    private long _totalUdpSentCount = 0;
    private readonly List<double> _udpLatencyHistory = new();
    private readonly object _statsLock = new object();
    
    // 清理任务
    private readonly Timer _cleanupTimer;
    private volatile bool _disposed = false;
    
    // 消费者任务
    private readonly Task[] _consumerTasks;
    
    #endregion
    
    #region 构造函数
    
    public PerformantPlaybackManager(
        ILogger<PerformantPlaybackManager> logger,
        ICommunicationService communicationService,
        IPlaybackStateService playbackStateService,
        int maxConcurrentTasks = 50, // 限制最大并发任务数
        int channelCapacity = 10000)  // Channel容量
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
        _playbackStateService = playbackStateService ?? throw new ArgumentNullException(nameof(playbackStateService));
        
        _maxConcurrentTasks = maxConcurrentTasks;
        _concurrencyLimiter = new SemaphoreSlim(maxConcurrentTasks, maxConcurrentTasks);
        
        // 创建有界Channel，避免内存无限增长
        var options = new BoundedChannelOptions(channelCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait, // 当满时等待
            SingleReader = false,
            SingleWriter = false
        };
        
        _playbackChannel = System.Threading.Channels.Channel.CreateBounded<TrajectoryPlaybackItem>(options);
        _channelWriter = _playbackChannel.Writer;
        _channelReader = _playbackChannel.Reader;
        
        // 创建多个消费者任务来处理播放
        int consumerCount = Math.Min(Environment.ProcessorCount, 8);
        _consumerTasks = new Task[consumerCount];
        for (int i = 0; i < consumerCount; i++)
        {
            _consumerTasks[i] = Task.Run(ConsumePlaybackItemsAsync);
        }
        
        // 定期清理过期状态（每5分钟）
        _cleanupTimer = new Timer(async _ => await CleanupExpiredStatesAsync(), null, 
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation($"高性能播放管理器已启动，最大并发: {maxConcurrentTasks}, 消费者数量: {consumerCount}");
    }
    
    #endregion
    
    #region 公共方法
    
    public async Task<bool> StartScenePlaybackAsync(Guid sceneId, List<Trajectory> trajectories, CancellationToken cancellationToken = default)
    {
        if (_disposed) return false;
        
        try
        {
            // 检查场景是否已在播放
            if (_playbackStateService.IsScenePlaybackActive(sceneId))
            {
                _logger.LogInformation($"场景 {sceneId} 已在播放中，忽略重复请求");
                return true;
            }
            
            // 创建场景级取消令牌
            var sceneCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            _cancellationTokens[sceneId] = sceneCts;
            
            // 启动场景播放状态
            _playbackStateService.StartScenePlayback(sceneId);
            
            // 创建场景播放任务
            var sceneTask = ProcessScenePlaybackAsync(sceneId, trajectories, sceneCts.Token);
            _scenePlaybackTasks[sceneId] = new PlaybackTaskInfo
            {
                Id = sceneId,
                Task = sceneTask,
                StartTime = DateTime.Now,
                TrajectoryCount = trajectories.Count
            };
            
            _logger.LogInformation($"场景 {sceneId} 播放已启动，轨迹数量: {trajectories.Count}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"启动场景播放失败: {sceneId}, 错误: {ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> StopScenePlaybackAsync(Guid sceneId)
    {
        if (_disposed) return false;
        
        try
        {
            // 停止场景播放状态
            _playbackStateService.StopScenePlayback(sceneId);
            
            // 取消场景播放任务
            if (_cancellationTokens.TryRemove(sceneId, out var cts))
            {
                cts.Cancel();
                cts.Dispose();
            }
            
            // 等待任务完成并清理
            if (_scenePlaybackTasks.TryRemove(sceneId, out var taskInfo))
            {
                try
                {
                    await taskInfo.Task.WaitAsync(TimeSpan.FromSeconds(5));
                }
                catch (TimeoutException)
                {
                    _logger.LogWarning($"场景 {sceneId} 停止超时");
                }
                catch (OperationCanceledException)
                {
                    // 预期的取消异常
                }
            }
            
            _logger.LogInformation($"场景 {sceneId} 播放已停止");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"停止场景播放失败: {sceneId}, 错误: {ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> StartChannelPlaybackAsync(Guid channelId, List<Trajectory> trajectories, CancellationToken cancellationToken = default)
    {
        if (_disposed) return false;
        
        try
        {
            // 检查通道是否已在播放
            if (_playbackStateService.IsChannelPlaybackActive(channelId))
            {
                _logger.LogInformation($"通道 {channelId} 已在播放中，忽略重复请求");
                return true;
            }
            
            // 获取场景ID（假设轨迹都属于同一个场景）
            var firstTrajectory = trajectories.FirstOrDefault();
            if (firstTrajectory == null) return false;
            
            // 这里需要获取通道的场景ID，简化处理
            var sceneId = Guid.NewGuid(); // 实际应该从数据库获取
            
            // 创建通道级取消令牌
            var channelCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            _cancellationTokens[channelId] = channelCts;
            
            // 启动通道播放状态
            _playbackStateService.StartChannelPlayback(channelId, sceneId);
            
            // 创建通道播放任务
            var channelTask = ProcessChannelPlaybackAsync(channelId, trajectories, channelCts.Token);
            _channelPlaybackTasks[channelId] = new PlaybackTaskInfo
            {
                Id = channelId,
                Task = channelTask,
                StartTime = DateTime.Now,
                TrajectoryCount = trajectories.Count
            };
            
            _logger.LogInformation($"通道 {channelId} 播放已启动，轨迹数量: {trajectories.Count}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"启动通道播放失败: {channelId}, 错误: {ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> StopChannelPlaybackAsync(Guid channelId)
    {
        if (_disposed) return false;
        
        try
        {
            // 停止通道播放状态
            _playbackStateService.StopChannelPlayback(channelId);
            
            // 取消通道播放任务
            if (_cancellationTokens.TryRemove(channelId, out var cts))
            {
                cts.Cancel();
                cts.Dispose();
            }
            
            // 等待任务完成并清理
            if (_channelPlaybackTasks.TryRemove(channelId, out var taskInfo))
            {
                try
                {
                    await taskInfo.Task.WaitAsync(TimeSpan.FromSeconds(5));
                }
                catch (TimeoutException)
                {
                    _logger.LogWarning($"通道 {channelId} 停止超时");
                }
                catch (OperationCanceledException)
                {
                    // 预期的取消异常
                }
            }
            
            _logger.LogInformation($"通道 {channelId} 播放已停止");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"停止通道播放失败: {channelId}, 错误: {ex.Message}");
            return false;
        }
    }
    
    public PlaybackPerformanceStats GetPerformanceStats()
    {
        lock (_statsLock)
        {
            var process = Process.GetCurrentProcess();
            
            return new PlaybackPerformanceStats
            {
                ActiveSceneCount = _scenePlaybackTasks.Count,
                ActiveChannelCount = _channelPlaybackTasks.Count,
                ActiveTrajectoryCount = _scenePlaybackTasks.Values.Sum(t => t.TrajectoryCount) + 
                                      _channelPlaybackTasks.Values.Sum(t => t.TrajectoryCount),
                TotalUdpSentCount = _totalUdpSentCount,
                AverageUdpLatencyMs = _udpLatencyHistory.Count > 0 ? _udpLatencyHistory.Average() : 0,
                MemoryUsageMB = process.WorkingSet64 / 1024.0 / 1024.0,
                ActiveThreadCount = process.Threads.Count
            };
        }
    }
    
    public PlaybackTaskStats GetTaskStats()
    {
        return new PlaybackTaskStats
        {
            SceneTaskCount = _scenePlaybackTasks.Count,
            ChannelTaskCount = _channelPlaybackTasks.Count,
            TrajectoryTaskCount = _scenePlaybackTasks.Values.Sum(t => t.TrajectoryCount) + 
                                 _channelPlaybackTasks.Values.Sum(t => t.TrajectoryCount),
            PendingCleanupCount = _cancellationTokens.Count,
            ThreadPoolQueueLength = ThreadPool.PendingWorkItemCount
        };
    }
    
    public async Task<int> CleanupExpiredStatesAsync()
    {
        if (_disposed) return 0;
        
        int cleanedCount = 0;
        var cutoffTime = DateTime.Now.AddMinutes(-30); // 清理30分钟前的过期状态
        
        try
        {
            // 清理已完成的场景任务
            var expiredSceneTasks = _scenePlaybackTasks
                .Where(kvp => kvp.Value.Task.IsCompleted && kvp.Value.StartTime < cutoffTime)
                .ToList();
                
            foreach (var kvp in expiredSceneTasks)
            {
                if (_scenePlaybackTasks.TryRemove(kvp.Key, out _))
                {
                    cleanedCount++;
                }
            }
            
            // 清理已完成的通道任务
            var expiredChannelTasks = _channelPlaybackTasks
                .Where(kvp => kvp.Value.Task.IsCompleted && kvp.Value.StartTime < cutoffTime)
                .ToList();
                
            foreach (var kvp in expiredChannelTasks)
            {
                if (_channelPlaybackTasks.TryRemove(kvp.Key, out _))
                {
                    cleanedCount++;
                }
            }
            
            // 清理过期的取消令牌
            var expiredTokens = _cancellationTokens
                .Where(kvp => kvp.Value.IsCancellationRequested)
                .ToList();
                
            foreach (var kvp in expiredTokens)
            {
                if (_cancellationTokens.TryRemove(kvp.Key, out var cts))
                {
                    cts.Dispose();
                    cleanedCount++;
                }
            }
            
            // 清理UDP延迟历史记录，保持最近1000条
            lock (_statsLock)
            {
                if (_udpLatencyHistory.Count > 1000)
                {
                    _udpLatencyHistory.RemoveRange(0, _udpLatencyHistory.Count - 1000);
                }
            }
            
            if (cleanedCount > 0)
            {
                _logger.LogInformation($"清理了 {cleanedCount} 个过期状态");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"清理过期状态失败: {ex.Message}");
        }
        
        return cleanedCount;
    }
    
    #endregion
    
    #region 私有方法
    
    private async Task ProcessScenePlaybackAsync(Guid sceneId, List<Trajectory> trajectories, CancellationToken cancellationToken)
    {
        try
        {
            // 将轨迹添加到播放队列
            foreach (var trajectory in trajectories)
            {
                if (cancellationToken.IsCancellationRequested) break;
                
                var playbackItem = new TrajectoryPlaybackItem
                {
                    Trajectory = trajectory,
                    SceneId = sceneId,
                    ChannelId = trajectory.ChannelId,
                    CancellationToken = cancellationToken
                };
                
                await _channelWriter.WriteAsync(playbackItem, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation($"场景 {sceneId} 播放被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError($"场景 {sceneId} 播放处理失败: {ex.Message}");
        }
    }
    
    private async Task ProcessChannelPlaybackAsync(Guid channelId, List<Trajectory> trajectories, CancellationToken cancellationToken)
    {
        try
        {
            // 将轨迹添加到播放队列
            foreach (var trajectory in trajectories)
            {
                if (cancellationToken.IsCancellationRequested) break;
                
                var playbackItem = new TrajectoryPlaybackItem
                {
                    Trajectory = trajectory,
                    SceneId = Guid.NewGuid(), // 实际应该从数据库获取
                    ChannelId = channelId,
                    CancellationToken = cancellationToken
                };
                
                await _channelWriter.WriteAsync(playbackItem, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation($"通道 {channelId} 播放被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError($"通道 {channelId} 播放处理失败: {ex.Message}");
        }
    }
    
    private async Task ConsumePlaybackItemsAsync()
    {
        try
        {
            await foreach (var item in _channelReader.ReadAllAsync())
            {
                if (_disposed) break;
                
                // 使用信号量限制并发
                await _concurrencyLimiter.WaitAsync(item.CancellationToken);
                
                try
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await ProcessTrajectoryPlaybackAsync(item);
                        }
                        finally
                        {
                            _concurrencyLimiter.Release();
                        }
                    }, item.CancellationToken);
                }
                catch (OperationCanceledException)
                {
                    _concurrencyLimiter.Release();
                    break;
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 正常关闭
        }
        catch (Exception ex)
        {
            _logger.LogError($"消费播放项目失败: {ex.Message}");
        }
    }
    
    private async Task ProcessTrajectoryPlaybackAsync(TrajectoryPlaybackItem item)
    {
        try
        {
            var trajectory = item.Trajectory;
            var cancellationToken = item.CancellationToken;
            
            // 模拟获取场景和通道信息
            // 实际应该从服务中获取
            var scene = new Scene
            {
                Id = item.SceneId,
                ServerIp = "127.0.0.1",
                ServerPort = 8080,
                OriginX = 0,
                OriginY = 0,
                PixelToMeterRatio = 1.0
            };
            
            var channel = new Core.Entities.Channel
            {
                Id = item.ChannelId,
                ChannelNumber = 1,
                Address = "A",
                UseZAxis = false
            };
            
            // 启动实时通信
            await _communicationService.StartRealTimeCommunicationAsync(scene.ServerIp, scene.ServerPort);
            
            do
            {
                var startTime = DateTime.Now;
                var sortedPoints = trajectory.Points.OrderBy(p => p.Timestamp).ToList();
                
                foreach (var point in sortedPoints)
                {
                    if (cancellationToken.IsCancellationRequested) return;
                    
                    // 检查是否应该停止
                    if (_playbackStateService.ShouldTrajectoryStop(trajectory.Id, item.ChannelId, item.SceneId))
                    {
                        return;
                    }
                    
                    // 转换坐标
                    var physicalX = (point.X - scene.OriginX) * scene.PixelToMeterRatio;
                    var physicalY = (scene.OriginY - point.Y) * scene.PixelToMeterRatio;
                    
                    var coordinateData = new CoordinateData
                    {
                        ChannelNumber = channel.ChannelNumber,
                        X = Math.Round(physicalX, 3),
                        Y = Math.Round(physicalY, 3),
                        Z = 0,
                        Timestamp = DateTime.Now,
                        ChannelAddress = channel.Address
                    };
                    
                    // 发送UDP数据并记录性能
                    var sendStart = DateTime.Now;
                    await _communicationService.SendCoordinateDataAsync(scene.ServerIp, scene.ServerPort, coordinateData);
                    var sendLatency = (DateTime.Now - sendStart).TotalMilliseconds;
                    
                    // 更新统计信息
                    Interlocked.Increment(ref _totalUdpSentCount);
                    lock (_statsLock)
                    {
                        _udpLatencyHistory.Add(sendLatency);
                    }
                    
                    // 精确时间控制
                    var elapsedTime = (DateTime.Now - startTime).TotalSeconds;
                    var waitTime = point.Timestamp - elapsedTime;
                    if (waitTime > 0)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(waitTime), cancellationToken);
                    }
                }
                
                if (!trajectory.IsLoop) break;
                
            } while (!cancellationToken.IsCancellationRequested);
        }
        catch (OperationCanceledException)
        {
            // 正常取消
        }
        catch (Exception ex)
        {
            _logger.LogError($"轨迹播放失败: {item.Trajectory.Id}, 错误: {ex.Message}");
        }
    }
    
    #endregion
    
    #region IDisposable
    
    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;
        
        // 停止接收新的播放项目
        _channelWriter.Complete();
        
        // 取消所有播放任务
        foreach (var cts in _cancellationTokens.Values)
        {
            cts.Cancel();
            cts.Dispose();
        }
        _cancellationTokens.Clear();
        
        // 等待消费者任务完成
        Task.WaitAll(_consumerTasks, TimeSpan.FromSeconds(10));
        
        // 清理资源
        _cleanupTimer?.Dispose();
        _concurrencyLimiter?.Dispose();
        
        _logger.LogInformation("高性能播放管理器已释放");
    }
    
    #endregion
}

/// <summary>
/// 轨迹播放项目
/// </summary>
internal class TrajectoryPlaybackItem
{
    public required Trajectory Trajectory { get; set; }
    public required Guid SceneId { get; set; }
    public required Guid ChannelId { get; set; }
    public required CancellationToken CancellationToken { get; set; }
}

/// <summary>
/// 播放任务信息
/// </summary>
internal class PlaybackTaskInfo
{
    public required Guid Id { get; set; }
    public required Task Task { get; set; }
    public required DateTime StartTime { get; set; }
    public required int TrajectoryCount { get; set; }
}
