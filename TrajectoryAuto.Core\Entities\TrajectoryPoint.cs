namespace TrajectoryAuto.Core.Entities;

/// <summary>
/// 轨迹点位实体
/// </summary>
public class TrajectoryPoint
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    /// <summary>
    /// X坐标（像素）
    /// </summary>
    public double X { get; set; }
    
    /// <summary>
    /// Y坐标（像素）
    /// </summary>
    public double Y { get; set; }
    
    /// <summary>
    /// 时间戳（相对于轨迹开始时间的秒数）
    /// </summary>
    public double Timestamp { get; set; }
    
    /// <summary>
    /// 点位顺序
    /// </summary>
    public int Order { get; set; }
    
    /// <summary>
    /// 所属轨迹ID
    /// </summary>
    public Guid TrajectoryId { get; set; }
    
    /// <summary>
    /// 所属轨迹
    /// </summary>
    public Trajectory? Trajectory { get; set; }
    
    /// <summary>
    /// 转换为物理坐标（米）
    /// </summary>
    /// <param name="originX">原点X坐标</param>
    /// <param name="originY">原点Y坐标</param>
    /// <param name="pixelToMeterRatio">像素到米的转换比例</param>
    /// <returns>物理坐标</returns>
    public (double X, double Y) ToPhysicalCoordinate(double originX, double originY, double pixelToMeterRatio)
    {
        var physicalX = (X - originX) * pixelToMeterRatio;
        var physicalY = (originY - Y) * pixelToMeterRatio; // Y轴翻转
        return (Math.Round(physicalX, 3), Math.Round(physicalY, 3));
    }
}