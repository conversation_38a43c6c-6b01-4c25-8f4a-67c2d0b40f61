namespace TrajectoryAuto.Core.Entities;

/// <summary>
/// 轨迹实体
/// </summary>
public class Trajectory
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    /// <summary>
    /// 轨迹名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 轨迹类型
    /// </summary>
    public TrajectoryType Type { get; set; }
    
    /// <summary>
    /// 运行时间（秒）
    /// </summary>
    public double Duration { get; set; }
    
    /// <summary>
    /// 是否循环播放
    /// </summary>
    public bool IsLoop { get; set; }
    
    /// <summary>
    /// 循环播放次数（0表示无限循环）
    /// </summary>
    public int LoopCount { get; set; } = 0;
    
    /// <summary>
    /// 是否逆向播放
    /// </summary>
    public bool IsReverse { get; set; } = false;
    
    /// <summary>
    /// 是否使用Z轴
    /// </summary>
    public bool UseZAxis { get; set; } = false;
    
    /// <summary>
    /// Z轴数据范围最小值
    /// </summary>
    public double ZAxisMin { get; set; } = 0.0;
    
    /// <summary>
    /// Z轴数据范围最大值
    /// </summary>
    public double ZAxisMax { get; set; } = 1.0;
    
    /// <summary>
    /// 轨迹点位数据
    /// </summary>
    public List<TrajectoryPoint> Points { get; set; } = new();
    
    /// <summary>
    /// 所属通道ID
    /// </summary>
    public Guid ChannelId { get; set; }
    
    /// <summary>
    /// 所属通道
    /// </summary>
    public Channel? Channel { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 线条颜色（十六进制）
    /// </summary>
    public string LineColor { get; set; } = "#000000";
    
    /// <summary>
    /// 线条宽度（像素）
    /// </summary>
    public int LineWidth { get; set; } = 2;
    
    /// <summary>
    /// 线条样式（实线、虚线、点线等）
    /// </summary>
    public LineStyle LineStyle { get; set; } = LineStyle.Solid;
    
    /// <summary>
    /// 线条透明度（0-1）
    /// </summary>
    public double LineOpacity { get; set; } = 1.0;
    
    /// <summary>
    /// 是否显示轨迹点
    /// </summary>
    public bool ShowPoints { get; set; } = true;
    
    /// <summary>
    /// 轨迹点大小（像素）
    /// </summary>
    public int PointSize { get; set; } = 4;
    
    /// <summary>
    /// 轨迹点颜色（十六进制）
    /// </summary>
    public string PointColor { get; set; } = "#FF0000";
    
    /// <summary>
    /// 是否显示起点和终点标记
    /// </summary>
    public bool ShowEndpoints { get; set; } = true;
    
    /// <summary>
    /// 起点标记颜色（十六进制）
    /// </summary>
    public string StartPointColor { get; set; } = "#00FF00";
    
    /// <summary>
    /// 终点标记颜色（十六进制）
    /// </summary>
    public string EndPointColor { get; set; } = "#FF0000";
    
    /// <summary>
    /// 是否启用阴影效果
    /// </summary>
    public bool EnableShadow { get; set; } = false;
    
    /// <summary>
    /// 阴影颜色（十六进制）
    /// </summary>
    public string ShadowColor { get; set; } = "rgba(0,0,0,0.5)";
    
    /// <summary>
    /// 阴影模糊度（像素）
    /// </summary>
    public int ShadowBlur { get; set; } = 5;
}

/// <summary>
/// 轨迹类型枚举
/// </summary>
public enum TrajectoryType
{
    /// <summary>
    /// 圆形轨迹
    /// </summary>
    Circle = 1,
    
    /// <summary>
    /// 矩形轨迹
    /// </summary>
    Rectangle = 2,
    
    /// <summary>
    /// 多边形轨迹
    /// </summary>
    Polygon = 3,
    
    /// <summary>
    /// 自由绘制轨迹
    /// </summary>
    FreeDraw = 4,
    
    /// <summary>
    /// 实时轨迹
    /// </summary>
    RealTime = 5
}

/// <summary>
/// 线条样式枚举
/// </summary>
public enum LineStyle
{
    /// <summary>
    /// 实线
    /// </summary>
    Solid = 1,
    
    /// <summary>
    /// 虚线
    /// </summary>
    Dashed = 2,
    
    /// <summary>
    /// 点线
    /// </summary>
    Dotted = 3,
    
    /// <summary>
    /// 点划线
    /// </summary>
    DashDot = 4
}
