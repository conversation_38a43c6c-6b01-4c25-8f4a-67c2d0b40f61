using Microsoft.AspNetCore.SignalR;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Web.Hubs;

/// <summary>
/// 轨迹实时通信Hub
/// </summary>
public class TrajectoryHub : Hub
{
    private readonly ICommunicationService _communicationService;
    private readonly ITrajectoryService _trajectoryService;

    public TrajectoryHub(ICommunicationService communicationService, ITrajectoryService trajectoryService)
    {
        _communicationService = communicationService;
        _trajectoryService = trajectoryService;
    }

    /// <summary>
    /// 客户端连接时调用
    /// </summary>
    /// <returns></returns>
    public override async Task OnConnectedAsync()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "TrajectoryClients");
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时调用
    /// </summary>
    /// <param name="exception"></param>
    /// <returns></returns>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, "TrajectoryClients");
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 发送实时坐标数据
    /// </summary>
    /// <param name="serverIp">服务器IP地址</param>
    /// <param name="serverPort">服务器端口</param>
    /// <param name="coordinateData">坐标数据</param>
    /// <param name="enableZAxis">是否启用Z轴</param>
    /// <param name="minZ">Z轴最小值</param>
    /// <param name="maxZ">Z轴最大值</param>
    /// <returns></returns>
    public async Task SendRealTimeCoordinate(string serverIp, int serverPort, CoordinateData coordinateData, 
        bool enableZAxis = false, double minZ = 0, double maxZ = 100)
    {
        try
        {
            // 处理 channelAddress 为 null 的情况
            if (string.IsNullOrEmpty(coordinateData.ChannelAddress))
            {
                coordinateData.ChannelAddress = coordinateData.ChannelNumber.ToString();
            }
            
            // 如果启用Z轴，在Z轴范围内计算一个随机值
            if (enableZAxis)
            {
                Random random = new Random();
                // 生成指定范围内的随机Z值
                coordinateData.Z = Math.Round(minZ + random.NextDouble() * (maxZ - minZ), 2);
            }
            
            // 使用 GetFormattedCoordinateString 方法获取正确格式的坐标数据
            // 格式为 {channelAddress}{X},{Y},{Z}
            var success = await _communicationService.SendCoordinateDataAsync(serverIp, serverPort, coordinateData);
            
            // 通知所有客户端坐标数据发送结果
            await Clients.Group("TrajectoryClients").SendAsync("CoordinateDataSent", new
            {
                success = success,
                data = coordinateData,
                formattedData = coordinateData.GetFormattedCoordinateString(), // 添加格式化的数据
                timestamp = DateTime.Now.ToString("o")
            });
            
            // 记录日志
            Console.WriteLine($"发送坐标数据: {coordinateData.GetFormattedCoordinateString()}, 启用Z轴: {enableZAxis}, Z范围: {minZ}-{maxZ}");
        }
        catch (Exception ex)
        {
            // 记录错误日志
            Console.WriteLine($"发送坐标数据失败: {ex.Message}");
            
            // 发送失败时通知客户端
            await Clients.Group("TrajectoryClients").SendAsync("CoordinateDataSent", new
            {
                success = false,
                error = ex.Message,
                timestamp = DateTime.Now.ToString("o")
            });
        }
    }

    /// <summary>
    /// 播放轨迹
    /// </summary>
    /// <param name="trajectoryId">轨迹ID</param>
    /// <param name="enableZAxis">是否启用Z轴</param>
    /// <param name="minZ">Z轴最小值</param>
    /// <param name="maxZ">Z轴最大值</param>
    /// <returns></returns>
    public async Task PlayTrajectory(Guid trajectoryId, bool enableZAxis = false, double minZ = 0, double maxZ = 100)
    {
        try
        {
            // 调用轨迹服务播放轨迹，并传递Z轴参数
            var success = await _trajectoryService.PlayTrajectoryAsync(trajectoryId, enableZAxis, minZ, maxZ);
            
            // 通知所有客户端轨迹播放状态变更
            await Clients.Group("TrajectoryClients").SendAsync("TrajectoryPlaybackChanged", new
            {
                trajectoryId = trajectoryId,
                status = success ? PlaybackStatus.Playing : PlaybackStatus.Stopped,
                enableZAxis = enableZAxis,
                minZ = minZ,
                maxZ = maxZ,
                timestamp = DateTime.Now.ToString("o") // ISO 8601格式字符串
            });
            
            // 记录日志
            Console.WriteLine($"播放轨迹: {trajectoryId}, 启用Z轴: {enableZAxis}, Z范围: {minZ}-{maxZ}");
        }
        catch (Exception ex)
        {
            // 记录错误日志
            Console.WriteLine($"播放轨迹失败: {ex.Message}");
            
            // 通知客户端播放失败
            await Clients.Group("TrajectoryClients").SendAsync("TrajectoryPlaybackChanged", new
            {
                trajectoryId = trajectoryId,
                status = PlaybackStatus.Stopped,
                error = ex.Message,
                timestamp = DateTime.Now.ToString("o")
            });
        }
    }

    /// <summary>
    /// 停止轨迹播放
    /// </summary>
    /// <param name="trajectoryId">轨迹ID</param>
    /// <returns></returns>
    public async Task StopTrajectory(Guid trajectoryId)
    {
        try
        {
            var success = await _trajectoryService.StopTrajectoryAsync(trajectoryId);
            
            // 通知所有客户端轨迹播放状态变更
            await Clients.Group("TrajectoryClients").SendAsync("TrajectoryPlaybackChanged", new
            {
                trajectoryId = trajectoryId,
                status = PlaybackStatus.Stopped,
                timestamp = DateTime.Now.ToString("o") // ISO 8601格式字符串
            });
            
            // 记录日志
            Console.WriteLine($"停止轨迹播放: {trajectoryId}");
        }
        catch (Exception ex)
        {
            // 记录错误日志
            Console.WriteLine($"停止轨迹播放失败: {ex.Message}");
            
            // 通知客户端停止失败
            await Clients.Group("TrajectoryClients").SendAsync("TrajectoryPlaybackChanged", new
            {
                trajectoryId = trajectoryId,
                status = PlaybackStatus.Playing, // 保持播放状态
                error = ex.Message,
                timestamp = DateTime.Now.ToString("o")
            });
        }
    }

    /// <summary>
    /// 获取轨迹播放状态
    /// </summary>
    /// <param name="trajectoryId"></param>
    /// <returns></returns>
    public async Task GetPlaybackStatus(Guid trajectoryId)
    {
        var status = await _trajectoryService.GetPlaybackStatusAsync(trajectoryId);
        
        await Clients.Caller.SendAsync("PlaybackStatusResponse", new
        {
            trajectoryId = trajectoryId,
            status = status,
            timestamp = DateTime.Now.ToString("o") // ISO 8601格式字符串
        });
    }
}