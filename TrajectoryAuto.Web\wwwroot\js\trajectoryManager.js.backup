/**
 * 轨迹管理器 - 处理轨迹的选择、设置、拖动和删除
 */
class TrajectoryManager {
    constructor(canvas) {
        this.canvas = canvas;
        this.selectedTrajectory = null;
        
        // 延迟初始化DOM元素引用，确保DOM已完全加载
        setTimeout(() => {
            this.initDOMReferences();
            this.initEventListeners();
        }, 100);
        
        // 拖动相关属性
        this.isDragging = false;
        this.dragStartPos = null;
        this.dragOffset = { x: 0, y: 0 };
        
        // 播放相关属性
        this.isPlaying = false;
        this.playingTrajectories = []; // 存储所有正在播放的轨迹
        this.playStartTime = null;
        this.currentPlayTime = 0;
        this.animationFrameId = null;
        this.playMarkers = []; // 存储所有轨迹的播放标记
    }
    
    // 初始化DOM元素引用
    initDOMReferences() {
        // console.log('初始化轨迹管理器DOM元素引用');
        
        // 模态框元素
        this.trajectoryModal = document.getElementById('trajectoryModal');
        this.trajectoryModalClose = document.getElementById('trajectoryModalClose');
        this.modalTrajectoryName = document.getElementById('modalTrajectoryName');
        this.modalTrajectoryDuration = document.getElementById('modalTrajectoryDuration');
        this.modalTrajectoryPointCount = document.getElementById('modalTrajectoryPointCount');
        this.modalTrajectoryLoop = document.getElementById('modalTrajectoryLoop');
        this.modalTrajectoryReverse = document.getElementById('modalTrajectoryReverse');
        this.trajectorySaveBtn = document.getElementById('trajectorySaveBtn');
        this.trajectoryCancelBtn = document.getElementById('trajectoryCancelBtn');
        this.trajectoryDeleteBtn = document.getElementById('trajectoryDeleteBtn');
        
        // 工具栏中的轨迹操作按钮
        this.trajectoryActions = document.getElementById('trajectoryActions');
        this.editTrajectoryBtn = document.getElementById('editTrajectoryBtn');
        this.deleteTrajectoryBtn = document.getElementById('deleteTrajectoryBtn');
        this.playTrajectoryBtn = document.getElementById('playTrajectoryBtn');
        this.stopTrajectoryBtn = document.getElementById('stopTrajectoryBtn');
        
        // 全局播放和停止按钮
        this.playBtn = document.getElementById('playBtn');
        this.stopBtn = document.getElementById('stopBtn');
        
        // 创建全局播放控制按钮
        const toolbarContainer = document.querySelector('.toolbar-container');
        if (toolbarContainer) {
            // 创建播放控制器容器
            const playControlsContainer = document.createElement('div');
            playControlsContainer.id = 'playControlsContainer';
            playControlsContainer.className = 'play-controls';
            playControlsContainer.style.display = 'flex';
            playControlsContainer.style.marginLeft = '10px';
            
            // 创建全局播放按钮
            this.globalPlayBtn = document.createElement('button');
            this.globalPlayBtn.id = 'globalPlayBtn';
            this.globalPlayBtn.className = 'btn btn-success';
            this.globalPlayBtn.innerHTML = '<i class="fas fa-play"></i> 播放所有';
            
            // 创建全局停止按钮
            this.globalStopBtn = document.createElement('button');
            this.globalStopBtn.id = 'globalStopBtn';
            this.globalStopBtn.className = 'btn btn-danger';
            this.globalStopBtn.innerHTML = '<i class="fas fa-stop"></i> 停止';
            this.globalStopBtn.style.display = 'none'; // 初始隐藏
            this.globalStopBtn.style.marginLeft = '5px';
            
            // 添加按钮到容器
            playControlsContainer.appendChild(this.globalPlayBtn);
            playControlsContainer.appendChild(this.globalStopBtn);
            
            // 添加到工具栏
            toolbarContainer.appendChild(playControlsContainer);
            
            // 添加事件监听器
            this.globalPlayBtn.addEventListener('click', () => this.playAllTrajectories());
            this.globalStopBtn.addEventListener('click', () => this.stopPlayback());
        }
        
        // 如果播放按钮不存在，创建它
        if (!this.playTrajectoryBtn && this.trajectoryActions) {
            this.playTrajectoryBtn = document.createElement('button');
            this.playTrajectoryBtn.id = 'playTrajectoryBtn';
            this.playTrajectoryBtn.className = 'btn btn-success btn-sm';
            this.playTrajectoryBtn.innerHTML = '<i class="fas fa-play"></i> 播放';
            this.trajectoryActions.appendChild(this.playTrajectoryBtn);
        }
        
        // 如果停止按钮不存在，创建它
        if (!this.stopTrajectoryBtn && this.trajectoryActions) {
            this.stopTrajectoryBtn = document.createElement('button');
            this.stopTrajectoryBtn.id = 'stopTrajectoryBtn';
            this.stopTrajectoryBtn.className = 'btn btn-danger btn-sm';
            this.stopTrajectoryBtn.innerHTML = '<i class="fas fa-stop"></i> 停止';
            this.stopTrajectoryBtn.style.display = 'none'; // 初始隐藏
            this.trajectoryActions.appendChild(this.stopTrajectoryBtn);
        }
        
        // 检查元素是否存在
        if (!this.trajectoryModal) console.error('trajectoryModal元素不存在');
        if (!this.modalTrajectoryName) console.error('modalTrajectoryName元素不存在');
        if (!this.modalTrajectoryDuration) console.error('modalTrajectoryDuration元素不存在');
        if (!this.modalTrajectoryPointCount) console.error('modalTrajectoryPointCount元素不存在');
        if (!this.modalTrajectoryLoop) console.error('modalTrajectoryLoop元素不存在');
        if (!this.modalTrajectoryReverse) console.error('modalTrajectoryReverse元素不存在');
    }

    initEventListeners() {
        // 防止重复初始化
        if (this.isInitializing) {
            console.log('正在初始化中，跳过重复调用');
            return;
        }
        
        // console.log('初始化轨迹管理器事件监听器');
        
        // 确保所有元素引用都存在 
        // 关闭模态框
        if (this.trajectoryModalClose) {
            this.trajectoryModalClose.addEventListener('click', () => this.closeModal());
        }
        
        if (this.trajectoryCancelBtn) {
            this.trajectoryCancelBtn.addEventListener('click', () => this.closeModal());
        }
        
        // 保存轨迹设置
        if (this.trajectorySaveBtn) {
            this.trajectorySaveBtn.addEventListener('click', () => this.saveTrajectorySettings());
        }
        
        // 删除轨迹（模态框中的按钮）
        if (this.trajectoryDeleteBtn) {
            this.trajectoryDeleteBtn.addEventListener('click', () => this.deleteSelectedTrajectory());
        }
        
        // 点击模态框外部关闭
        if (this.trajectoryModal) {
            window.addEventListener('click', (event) => {
                if (event.target === this.trajectoryModal) {
                    this.closeModal();
                }
            });
        }
        
        // 工具栏中的编辑按钮
        if (this.editTrajectoryBtn) {
            this.editTrajectoryBtn.addEventListener('click', () => {
                if (this.selectedTrajectory) {
                    this.openTrajectorySettings(this.selectedTrajectory);
                }
            });
        }
        
        // 工具栏中的删除按钮
        if (this.deleteTrajectoryBtn) {
            this.deleteTrajectoryBtn.addEventListener('click', () => {
                if (this.selectedTrajectory) {
                    this.deleteSelectedTrajectory();
                }
            });
        }
        
        // 工具栏中的播放按钮
        if (this.playTrajectoryBtn) {
            this.playTrajectoryBtn.addEventListener('click', () => {
                if (this.selectedTrajectory) {
                    this.playTrajectory(this.selectedTrajectory);
                } else {
                    // 如果没有选中轨迹，播放所有轨迹
                    this.playAllTrajectories();
                }
            });
        }
        
        // 工具栏中的停止按钮
        if (this.stopTrajectoryBtn) {
            this.stopTrajectoryBtn.addEventListener('click', () => {
                this.stopPlayback();
            });
        }
        
        // 使用事件委托方式绑定播放停止按钮，避免DOM元素重新创建导致的事件监听器丢失
        
        console.log('initEventListeners 被调用', {
            timestamp: new Date().toISOString(),
            hasExistingHandlers: !!(this.handlePlayButtonClick || this.handleStopButtonClick)
        });
        
        // 移除之前可能存在的事件监听器
        if (this.handlePlayButtonClick) {
            console.log('移除旧的播放按钮监听器');
            document.removeEventListener('click', this.handlePlayButtonClick, true);
        }
        if (this.handleStopButtonClick) {
            console.log('移除旧的停止按钮监听器');
            document.removeEventListener('click', this.handleStopButtonClick, true);
        }
        
        // 创建绑定的事件处理函数
        this.handlePlayButtonClick = (e) => {
            if (e.target && (e.target.id === 'playBtn' || e.target.id === 'globalPlayBtn')) {
                console.log('播放按钮被点击 (事件委托)');
                e.preventDefault();
                e.stopPropagation();
                this.playAllTrajectories();
            }
        };
        
        this.handleStopButtonClick = (e) => {
            if (e.target && (e.target.id === 'stopBtn' || e.target.id === 'globalStopBtn')) {
                console.log('停止按钮被点击 (事件委托)');
                e.preventDefault();
                e.stopPropagation();
                try {
                    this.stopPlayback();
                } catch (error) {
                    console.error('停止播放时发生错误:', error);
                    // 即使发生错误，也要尝试强制停止
                    this.forceStop();
                }
            }
        };
        
        // 添加时间戳标记
        this.eventListenersTimestamp = Date.now();
        this.handlePlayButtonClick._timestamp = this.eventListenersTimestamp;
        this.handleStopButtonClick._timestamp = this.eventListenersTimestamp;
        
        // 使用事件委托绑定到document上
        document.addEventListener('click', this.handlePlayButtonClick, true);
        document.addEventListener('click', this.handleStopButtonClick, true);
        
        console.log('事件监听器绑定完成', {
            playHandler: !!this.handlePlayButtonClick,
        });
    }
            
    // 工具栏中的停止按钮
    if (this.stopTrajectoryBtn) {
        this.stopTrajectoryBtn.addEventListener('click', () => {
            this.stopPlayback();
        });
    }
            
    // 使用事件委托方式绑定播放停止按钮，避免DOM元素重新创建导致的事件监听器丢失
            
    console.log('initEventListeners 被调用', {
        timestamp: new Date().toISOString(),
        hasExistingHandlers: !!(this.handlePlayButtonClick || this.handleStopButtonClick)
    });
            
    // 移除之前可能存在的事件监听器
    if (this.handlePlayButtonClick) {
        console.log('移除旧的播放按钮监听器');
        document.removeEventListener('click', this.handlePlayButtonClick, true);
    }
    if (this.handleStopButtonClick) {
        console.log('移除旧的停止按钮监听器');
        document.removeEventListener('click', this.handleStopButtonClick, true);
    }
            
    // 创建绑定的事件处理函数
    this.handlePlayButtonClick = (e) => {
        if (e.target && (e.target.id === 'playBtn' || e.target.id === 'globalPlayBtn')) {
            console.log('播放按钮被点击 (事件委托)');
            e.preventDefault();
            e.stopPropagation();
            this.playAllTrajectories();
        }
    };
            
    this.handleStopButtonClick = (e) => {
        if (e.target && (e.target.id === 'stopBtn' || e.target.id === 'globalStopBtn')) {
            console.log('停止按钮被点击 (事件委托)');
            e.preventDefault();
            e.stopPropagation();
            try {
                this.stopPlayback();
            } catch (error) {
                console.error('停止播放时发生错误:', error);
                // 即使发生错误，也要尝试强制停止
                this.forceStop();
            }
        }
    };
            
    // 添加时间戳标记
    this.eventListenersTimestamp = Date.now();
    this.handlePlayButtonClick._timestamp = this.eventListenersTimestamp;
    this.handleStopButtonClick._timestamp = this.eventListenersTimestamp;
            
    // 使用事件委托绑定到document上
    document.addEventListener('click', this.handlePlayButtonClick, true);
    document.addEventListener('click', this.handleStopButtonClick, true);
            
    console.log('事件监听器绑定完成', {
        playHandler: !!this.handlePlayButtonClick,
        stopHandler: !!this.handleStopButtonClick
    });
            
    // 设置全局监控，检测是否有其他代码移除事件监听器
    if (!window.trajectoryEventMonitor) {
        window.trajectoryEventMonitor = true;
        const originalRemoveEventListener = document.removeEventListener;
        document.removeEventListener = function(type, listener, options) {
            if (type === 'click' && listener) {
                const listenerStr = listener.toString();
                const isOurHandler = listenerStr.includes('stopBtn') || listenerStr.includes('playBtn') || 
                                   listenerStr.includes('停止按钮') || listenerStr.includes('播放按钮');
                
                if (isOurHandler) {
                    console.error('检测到我们的 click 事件监听器被移除！', {
                        type,
                        listener: listenerStr.substring(0, 200),
                        stack: new Error().stack
                    });
                } else {
                    console.log('检测到其他 click 事件监听器被移除:', {
                        type,
                        listener: listenerStr.substring(0, 100)
                    });
                }
        }
        
        // 添加初始化标记，防止重复初始化
        this.isInitializing = false;
        
        this.eventCheckInterval = setInterval(() => {
            if (!this.handleStopButtonClick && !this.isInitializing) {
                console.error('事件监听器丢失！重新绑定...');
                this.isInitializing = true;
                
                // 延迟重新绑定，防止与其他操作冲突
                setTimeout(() => {
                    try {
                        this.initEventListeners();
                    } finally {
                        this.isInitializing = false;
                    }
                }, 100);
            }
        }, 5000);
        
        // 检查按钮元素是否存在
        console.log('按钮元素检查:', {
            playBtn: !!document.getElementById('playBtn'),
            stopBtn: !!document.getElementById('stopBtn'),
            playBtnElement: document.getElementById('playBtn'),
            stopBtnElement: document.getElementById('stopBtn')
        });
        
        // 拖动相关事件
        if (this.canvas && this.canvas.canvas) {
            // 鼠标按下事件 - 开始拖动
            this.canvas.canvas.addEventListener('mousedown', (e) => {
                if (this.canvas.currentTool === 'select' && this.selectedTrajectory) {
                    const pos = this.canvas.getMousePos(e);
                    // 检查是否点击在轨迹上
                    if (this.isPointOnTrajectory(pos, this.selectedTrajectory)) {
                        this.startDragging(pos);
                        e.preventDefault();
                    }
                }
            });
            
            // 鼠标移动事件 - 拖动中
            this.canvas.canvas.addEventListener('mousemove', (e) => {
                if (this.isDragging && this.selectedTrajectory) {
                    const pos = this.canvas.getMousePos(e);
                    this.dragTrajectory(pos);
                    e.preventDefault();
                }
            });
            
            // 鼠标释放事件 - 结束拖动
            this.canvas.canvas.addEventListener('mouseup', (e) => {
                if (this.isDragging) {
                    this.stopDragging();
                    e.preventDefault();
                }
            });
            
            // 触摸事件支持
            this.canvas.canvas.addEventListener('touchstart', (e) => {
                if (this.canvas.currentTool === 'select' && this.selectedTrajectory) {
                    const pos = this.canvas.getTouchPos(e);
                    if (this.isPointOnTrajectory(pos, this.selectedTrajectory)) {
                        this.startDragging(pos);
                        e.preventDefault();
                    }
                }
            });
            
            this.canvas.canvas.addEventListener('touchmove', (e) => {
                if (this.isDragging && this.selectedTrajectory) {
                    const pos = this.canvas.getTouchPos(e);
                    this.dragTrajectory(pos);
                    e.preventDefault();
                }
            });
            
            this.canvas.canvas.addEventListener('touchend', (e) => {
                if (this.isDragging) {
                    this.stopDragging();
                    e.preventDefault();
                }
            });
        }
    }

    // 选择轨迹
    selectTrajectory(trajectory) {
        this.selectedTrajectory = trajectory;
        
        // 显示工具栏中的轨迹操作按钮
        if (this.trajectoryActions) {
            this.trajectoryActions.style.display = 'flex';
        }
    }
    
    // 取消选择轨迹
    deselectTrajectory() {
        this.selectedTrajectory = null;
        
        // 隐藏工具栏中的轨迹操作按钮
        if (this.trajectoryActions) {
            this.trajectoryActions.style.display = 'none';
        }
    }

    // 打开轨迹设置模态框
    openTrajectorySettings(trajectory) {
        if (!trajectory) return;
        
        this.selectTrajectory(trajectory);
        
        // 在控制台输出所有轨迹属性，帮助调试
        // console.log('轨迹属性:', {
        //     id: trajectory.id,
        //     name: trajectory.name,
        //     duration: trajectory.duration,
        //     isLoop: trajectory.isLoop,
        //     isReverse: trajectory.isReverse,
        //     pointCount: trajectory.points ? trajectory.points.length : 0
        // });
        
        // console.log('显示轨迹设置模态框 - 完全重写版本');
        
        // 1. 先删除现有的模态框（如果存在）
        let existingModal = document.getElementById('trajectoryModal');
        if (existingModal && existingModal.parentNode) {
            // console.log('删除现有轨迹模态框');
            existingModal.parentNode.removeChild(existingModal);
        }
        
        // 2. 创建新的模态框元素
        const modal = document.createElement('div');
        modal.id = 'trajectoryModal';
        modal.className = 'modal';
        
        // 3. 设置模态框HTML内容
        modal.innerHTML = `
            <div class="modal-content trajectory-modal">
                <div class="modal-header">
                    <h3 id="trajectoryModalTitle"><i class="fas fa-route me-2"></i>轨迹设置 - ${trajectory.name || '未命名轨迹'}</h3>
                    <span class="close" id="trajectoryModalClose">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="trajectoryForm" class="trajectory-form">
                        <div class="form-row" style="display: flex; align-items: center; margin-bottom: 10px;">
                            <label for="modalTrajectoryName" style="width: 100px; margin-bottom: 0; flex-shrink: 0;">轨迹名称:</label>
                            <input type="text" id="modalTrajectoryName" name="modalTrajectoryName" class="form-control" value="${trajectory.name || `轨迹_${trajectory.id}`}" style="flex: 1;">
                        </div>
                        <div class="form-row" style="display: flex; align-items: center; margin-bottom: 10px;">
                            <label for="modalTrajectoryDuration" style="width: 100px; margin-bottom: 0; flex-shrink: 0;">持续时间(秒):</label>
                            <input type="number" id="modalTrajectoryDuration" name="modalTrajectoryDuration" class="form-control" min="1" max="6000" value="${trajectory.duration || 60}" style="flex: 1;">
                        </div>
                        <div class="form-row" style="display: flex; align-items: center; margin-bottom: 10px;">
                            <label for="modalTrajectoryPointCount" style="width: 100px; margin-bottom: 0; flex-shrink: 0;">点位数量:</label>
                            <input type="number" id="modalTrajectoryPointCount" name="modalTrajectoryPointCount" class="form-control" min="2" max="1000" value="${trajectory.points ? trajectory.points.length : 300}" style="flex: 1;">
                        </div>
                        <div class="form-row" style="display: flex; align-items: center; margin-bottom: 10px;">
                            <label for="modalTrajectoryLoop" style="width: 100px; margin-bottom: 0; flex-shrink: 0;">循环:</label>
                            <div class="form-check" style="flex: 1;">
                                <input type="checkbox" id="modalTrajectoryLoop" name="modalTrajectoryLoop" class="form-check-input" ${trajectory.isLoop ? 'checked' : ''}>
                            </div>
                        </div>
                        <div class="form-row" style="display: flex; align-items: center; margin-bottom: 10px;">
                            <label for="modalTrajectoryReverse" style="width: 100px; margin-bottom: 0; flex-shrink: 0;">逆向播放:</label>
                            <div class="form-check" style="flex: 1;">
                                <input type="checkbox" id="modalTrajectoryReverse" name="modalTrajectoryReverse" class="form-check-input" ${trajectory.isReverse ? 'checked' : ''}>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="trajectoryCancelBtn"><i class="fas fa-times me-1"></i>取消</button>
                    <button type="button" class="btn btn-primary" id="trajectorySaveBtn"><i class="fas fa-save me-1"></i>保存</button>
                </div>
            </div>
        `;
        
        // 4. 添加到body
        document.body.appendChild(modal);
        console.log('已创建并添加新的轨迹模态框到body');
        
        // 5. 强制设置样式确保模态框显示在最上层
        console.log('设置轨迹模态框样式');
        modal.style.display = 'block';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.style.zIndex = '9999';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        
        // 6. 确保模态框内容可见
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            console.log('设置轨迹模态框内容样式');
            modalContent.style.opacity = '1';
            modalContent.style.visibility = 'visible';
            modalContent.style.zIndex = '10000';
            modalContent.style.position = 'relative';
            modalContent.style.margin = '10% auto';
            modalContent.style.width = '80%';
            modalContent.style.maxWidth = '500px';
            modalContent.style.backgroundColor = '#fff';
            modalContent.style.borderRadius = '5px';
            modalContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
            modalContent.style.overflow = 'hidden'; // 防止内容溢出圆角
            
            // 处理模态框标题栏圆角问题
            const modalHeader = modalContent.querySelector('.modal-header');
            if (modalHeader) {
                modalHeader.style.borderTopLeftRadius = '5px';
                modalHeader.style.borderTopRightRadius = '5px';
                modalHeader.style.backgroundColor = '#f8f9fa';
                modalHeader.style.padding = '10px 15px';
                modalHeader.style.borderBottom = '1px solid #dee2e6';
            }
        }
        
        // 7. 确保模态框在body下
        const body = document.body;
        if (modal.parentNode !== body) {
            console.log('将轨迹模态框移动到body直接子元素位置');
            body.appendChild(modal);
        }
        
        // 8. 绑定事件
        const closeBtn = modal.querySelector('#trajectoryModalClose');
        const cancelBtn = modal.querySelector('#trajectoryCancelBtn');
        const saveBtn = modal.querySelector('#trajectorySaveBtn');
        const deleteBtn = modal.querySelector('#trajectoryDeleteBtn');
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeModal());
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.closeModal());
        }
        
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveTrajectorySettings());
        }
        
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => this.deleteSelectedTrajectory());
        }
        
        // 9. 添加点击事件测试
        modal.onclick = function(event) {
            console.log('轨迹模态框被点击', event.target);
            if (event.target === modal) {
                console.log('点击了轨迹模态框背景');
            }
        };
        
        console.log('轨迹模态框已显示', modal);
        
        // 10. 强制重绘
        setTimeout(() => {
            console.log('强制重绘轨迹模态框');
            const displayValue = modal.style.display;
            modal.style.display = 'none';
            modal.offsetHeight; // 强制浏览器重新计算布局
            setTimeout(() => {
                modal.style.display = displayValue;
                console.log('轨迹模态框重绘完成');
            }, 10);
        }, 50);
        
        // 存储模态框引用
        this.trajectoryModal = modal;
    }
    
    // 创建缺失的输入元素
    createMissingElement(id, label, value, type = 'text') {
        const form = document.getElementById('trajectoryForm');
        if (!form) {
            console.error('找不到trajectoryForm元素，无法创建缺失的输入元素');
            return;
        }
        
        // 创建表单行
        const formRow = document.createElement('div');
        formRow.className = 'form-row';
        
        // 创建表单组
        const formGroup = document.createElement('div');
        formGroup.className = 'form-group';
        
        // 创建标签
        const labelElement = document.createElement('label');
        labelElement.setAttribute('for', id);
        labelElement.textContent = label;
        
        // 创建输入框
        const input = document.createElement('input');
        input.type = type;
        input.id = id;
        input.name = id;
        input.className = 'form-control';
        input.value = value;
        
        if (type === 'number') {
            input.min = '1';
            if (id === 'modalTrajectoryDuration') {
                input.max = '60';
            } else if (id === 'modalTrajectoryPointCount') {
                input.min = '2';
                input.max = '1000';
            }
        }
        
        // 组装元素
        formGroup.appendChild(labelElement);
        formGroup.appendChild(input);
        formRow.appendChild(formGroup);
        form.appendChild(formRow);
        
        // console.log(`已创建缺失的元素: ${id}`);
        return input;
    }
    
    // 创建缺失的复选框元素
    createMissingCheckbox(id, label, checked) {
        const form = document.getElementById('trajectoryForm');
        if (!form) {
            console.error('找不到trajectoryForm元素，无法创建缺失的复选框元素');
            return;
        }
        
        // 创建表单行
        const formRow = document.createElement('div');
        formRow.className = 'form-row';
        
        // 创建表单组
        const formGroup = document.createElement('div');
        formGroup.className = 'form-group';
        
        // 创建标签
        const labelElement = document.createElement('label');
        labelElement.setAttribute('for', id);
        labelElement.textContent = label;
        
        // 创建复选框容器
        const checkContainer = document.createElement('div');
        checkContainer.className = 'form-check';
        
        // 创建复选框
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = id;
        checkbox.name = id;
        checkbox.className = 'form-check-input';
        checkbox.checked = checked;
        
        // 组装元素
        checkContainer.appendChild(checkbox);
        formGroup.appendChild(labelElement);
        formGroup.appendChild(checkContainer);
        formRow.appendChild(formGroup);
        form.appendChild(formRow);
        
        // console.log(`已创建缺失的复选框元素: ${id}`);
        return checkbox;
    }

    // 关闭模态框
    closeModal() {
        // console.log('关闭轨迹模态框');
        const modal = document.getElementById('trajectoryModal');
        if (modal && modal.parentNode) {
            // 先隐藏模态框
            modal.style.display = 'none';
            modal.style.visibility = 'hidden';
            modal.style.opacity = '0';
            
            // 然后从 DOM 中移除
            modal.parentNode.removeChild(modal);
            // console.log('轨迹模态框已移除');
        } else {
            console.warn('找不到轨迹模态框，无法关闭');
        }
        // 清除引用
        this.trajectoryModal = null;
    }

    // 保存轨迹设置
    async saveTrajectorySettings() {
        if (!this.selectedTrajectory) {
            console.warn('没有选中的轨迹，无法保存设置');
            return;
        }
        
        // 获取模态框中的所有输入元素
        const nameInput = document.getElementById('modalTrajectoryName');
        const durationInput = document.getElementById('modalTrajectoryDuration');
        const pointCountInput = document.getElementById('modalTrajectoryPointCount');
        const loopCheckbox = document.getElementById('modalTrajectoryLoop');
        const reverseCheckbox = document.getElementById('modalTrajectoryReverse');
        
        // console.log('保存轨迹设置 - 开始处理');
          
        // 获取原始值用于比较
        const originalDuration = this.selectedTrajectory.duration;
        const originalPointCount = this.selectedTrajectory.points ? this.selectedTrajectory.points.length : 300;
        const originalReverse = this.selectedTrajectory.isReverse || false;
        
        // 更新轨迹属性
        if (nameInput) {
            this.selectedTrajectory.name = nameInput.value;
        } else {
            console.warn('modalTrajectoryName元素不存在，无法更新轨迹名称');
        }
        
        if (durationInput) {
            this.selectedTrajectory.duration = parseInt(durationInput.value);
        } else {
            console.warn('modalTrajectoryDuration元素不存在，使用默认持续时间');
            this.selectedTrajectory.duration = this.selectedTrajectory.duration || 10;
        }
        
        if (loopCheckbox) {
            this.selectedTrajectory.isLoop = loopCheckbox.checked;
        } else {
            console.warn('modalTrajectoryLoop元素不存在，使用默认循环设置');
        }
        
        if (reverseCheckbox) {
            this.selectedTrajectory.isReverse = reverseCheckbox.checked;
        } else {
            console.warn('modalTrajectoryReverse元素不存在，使用默认逆向设置');
        }
        
        // 获取并验证点位数量
        let newPointCount = originalPointCount;
        if (pointCountInput) {
            newPointCount = parseInt(pointCountInput.value);
            if (isNaN(newPointCount) || newPointCount < 2) {
                console.warn('点位数量无效，使用默认值300');
                newPointCount = 300;
                pointCountInput.value = 300;
            }
        } else {
            console.warn('modalTrajectoryPointCount元素不存在，使用原始点数量');
        }
        
        // console.log('点位数量设置为:', newPointCount);
        
        // 始终根据设置重新生成点位
        try {
            // console.log('开始重新生成轨迹点位');
            
            // 检查是否有足够的原始点进行重新采样
            if (!this.selectedTrajectory.points || this.selectedTrajectory.points.length < 2) {
                console.warn('原始轨迹点不足，创建默认点');
                // 创建默认的直线轨迹点
                const defaultPoints = [];
                for (let i = 0; i < newPointCount; i++) {
                    defaultPoints.push({
                        x: 100 + i * 5,
                        y: 100,
                        timestamp: (i / (newPointCount - 1)) * this.selectedTrajectory.duration,
                        order: i
                    });
                }
                this.selectedTrajectory.points = defaultPoints;
            } else {
                // 重新生成点
                this.selectedTrajectory.points = this.regenerateTrajectoryPoints(
                    this.selectedTrajectory.points, 
                    newPointCount, 
                    this.selectedTrajectory.isReverse
                );
            }
            
            // 始终重新计算时间戳，确保与持续时间匹配
            this.recalculateTimestamps(this.selectedTrajectory.points, this.selectedTrajectory.duration);
            
            // console.log('轨迹点重新生成成功，点数量:', this.selectedTrajectory.points.length);
        } catch (error) {
            console.error('重新生成轨迹点时出错:', error);
        }
        
        // 关闭模态框
        this.closeModal();
        
        // 保存到服务器
        console.log('保存轨迹到服务器');
        await this.saveTrajectoryToServer(this.selectedTrajectory);
        
        // 通知画布更新
        if (this.canvas && typeof this.canvas.updateTrajectory === 'function') {
            console.log('通知画布更新轨迹');
            this.canvas.updateTrajectory(this.selectedTrajectory);
        }
    }
    
    // 重新生成轨迹点
    regenerateTrajectoryPoints(originalPoints, newPointCount, isReverse) {
        if (!originalPoints || originalPoints.length < 2) {
            return originalPoints;
        }
        
        // 创建原始点的副本
        let points = [...originalPoints];
        
        // 如果需要逆向，先反转点数组
        if (isReverse) {
            points.reverse();
        }
        
        // 如果点数量相同，直接返回
        if (points.length === newPointCount) {
            return this.normalizePointOrders(points);
        }
        
        // 重新采样点
        const result = [];
        
        // 计算每个点之间的间隔
        const step = (points.length - 1) / (newPointCount - 1);
        
        for (let i = 0; i < newPointCount; i++) {
            const index = i * step;
            const lowerIndex = Math.floor(index);
            const upperIndex = Math.min(Math.ceil(index), points.length - 1);
            
            if (lowerIndex === upperIndex) {
                result.push({...points[lowerIndex]});
            } else {
                // 线性插值
                const ratio = index - lowerIndex;
                const p1 = points[lowerIndex];
                const p2 = points[upperIndex];
                
                result.push({
                    x: p1.x + ratio * (p2.x - p1.x),
                    y: p1.y + ratio * (p2.y - p1.y),
                    timestamp: 0, // 稍后重新计算
                    order: i
                });
            }
        }
        
        return result;
    }
    
    // 重新计算时间戳
    recalculateTimestamps(points, duration) {
        if (!points || points.length < 2) {
            console.warn('点数组为空或点数不足，无法重新计算时间戳');
            return;
        }
        
        console.log(`重新计算时间戳，点数: ${points.length}, 持续时间: ${duration}秒`);
        
        try {
            // 为每个点分配新的时间戳
            points.forEach((point, index) => {
                if (point) {
                    point.timestamp = (index / (points.length - 1)) * duration;
                }
            });
            
            console.log('时间戳重新计算完成');
        } catch (error) {
            console.error('重新计算时间戳时出错:', error);
        }
    }
    
    // 规范化点的顺序
    normalizePointOrders(points) {
        return points.map((point, index) => ({
            ...point,
            order: index
        }));
    }
    
    // 保存轨迹到服务器
    async saveTrajectoryToServer(trajectory) {
        if (!trajectory) {
            console.error('轨迹对象为空，无法保存到服务器');
            return false;
        }
        
        if (!trajectory.id) {
            console.error('轨迹ID不存在，无法保存到服务器');
            return false;
        }
        
        console.log(`开始保存轨迹 ${trajectory.id} 到服务器`);
        console.log('轨迹数据:', {
            id: trajectory.id,
            name: trajectory.name,
            duration: trajectory.duration,
            isLoop: trajectory.isLoop,
            isReverse: trajectory.isReverse,
            pointCount: trajectory.points ? trajectory.points.length : 0
        });
        
        try {
            // 创建一个副本，避免修改原始对象
            const trajectoryToSave = JSON.parse(JSON.stringify(trajectory));
            
            // 确保点数组有效
            if (!trajectoryToSave.points || trajectoryToSave.points.length < 2) {
                console.error('轨迹点数组无效，无法保存到服务器');
                return false;
            }
            
            const response = await fetch(`/api/trajectories/${trajectory.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trajectoryToSave)
            });
            
            if (response.ok) {
                console.log(`轨迹 ${trajectory.id} 已成功保存到服务器`);
                return true;
            } else {
                const errorText = await response.text();
                console.error(`保存轨迹 ${trajectory.id} 失败:`, response.status, errorText);
                return false;
            }
        } catch (error) {
            console.error(`保存轨迹 ${trajectory.id} 时出错:`, error);
            return false;
        }
    }

    // 删除选中的轨迹
    deleteSelectedTrajectory() {
        if (!this.selectedTrajectory) return;
        
        // 通知画布删除轨迹
        if (this.canvas && typeof this.canvas.deleteTrajectory === 'function') {
            this.canvas.deleteTrajectory(this.selectedTrajectory);
        }
        
        // 关闭模态框
        this.closeModal();
        
        // 清除选中状态
        this.selectedTrajectory = null;
    }
    
    // 检查点是否在轨迹上
    isPointOnTrajectory(point, trajectory) {
        if (!trajectory || !trajectory.points || trajectory.points.length < 2) {
            return false;
        }
        
        // 设置点击容差（像素）
        const tolerance = 10;
        
        // 检查点是否在轨迹附近
        for (let i = 0; i < trajectory.points.length - 1; i++) {
            const p1 = trajectory.points[i];
            const p2 = trajectory.points[i + 1];
            
            // 计算点到线段的距离
            const distance = this.pointToLineDistance(point, p1, p2);
            
            if (distance <= tolerance) {
                return true;
            }
        }
        
        return false;
    }
    
    // 计算点到线段的距离
    pointToLineDistance(point, lineStart, lineEnd) {
        const A = point.x - lineStart.x;
        const B = point.y - lineStart.y;
        const C = lineEnd.x - lineStart.x;
        const D = lineEnd.y - lineStart.y;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        let param = -1;
        
        if (lenSq !== 0) {
            param = dot / lenSq;
        }
        
        let xx, yy;
        
        if (param < 0) {
            xx = lineStart.x;
            yy = lineStart.y;
        } else if (param > 1) {
            xx = lineEnd.x;
            yy = lineEnd.y;
        } else {
            xx = lineStart.x + param * C;
            yy = lineStart.y + param * D;
        }
        
        const dx = point.x - xx;
        const dy = point.y - yy;
        
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    // 开始拖动
    startDragging(pos) {
        this.isDragging = true;
        this.dragStartPos = pos;
        
        // 计算轨迹中心点
        const center = this.calculateTrajectoryCenter(this.selectedTrajectory);
        
        // 计算拖动偏移量
        this.dragOffset = {
            x: center.x - pos.x,
            y: center.y - pos.y
        };
        
        // 添加拖动中的样式
        if (this.canvas && this.canvas.canvas) {
            this.canvas.canvas.style.cursor = 'grabbing';
        }
    }
    
    // 拖动轨迹
    dragTrajectory(pos) {
        if (!this.isDragging || !this.selectedTrajectory || !this.selectedTrajectory.points) {
            return;
        }
        
        // 计算新的中心点
        const newCenter = {
            x: pos.x + this.dragOffset.x,
            y: pos.y + this.dragOffset.y
        };
        
        // 计算当前中心点
        const currentCenter = this.calculateTrajectoryCenter(this.selectedTrajectory);
        
        // 计算偏移量
        const deltaX = newCenter.x - currentCenter.x;
        const deltaY = newCenter.y - currentCenter.y;
        
        // 移动所有点
        this.selectedTrajectory.points.forEach(point => {
            point.x += deltaX;
            point.y += deltaY;
        });
        
        // 重绘画布
        if (this.canvas) {
            this.canvas.redrawCanvas();
        }
    }
    
    // 停止拖动
    stopDragging() {
        if (this.isDragging && this.selectedTrajectory) {
            // 保存轨迹到服务器
            this.saveTrajectoryToServer(this.selectedTrajectory);
        }
        
        this.isDragging = false;
        this.dragStartPos = null;
        
        // 恢复光标样式
        if (this.canvas && this.canvas.canvas) {
            this.canvas.canvas.style.cursor = 'default';
        }
    }
    
    // 计算轨迹中心点
    calculateTrajectoryCenter(trajectory) {
        if (!trajectory || !trajectory.points || trajectory.points.length === 0) {
            return { x: 0, y: 0 };
        }
        
        let sumX = 0;
        let sumY = 0;
        
        trajectory.points.forEach(point => {
            sumX += point.x;
            sumY += point.y;
        });
        
        return {
            x: sumX / trajectory.points.length,
            y: sumY / trajectory.points.length
        };
    }
    
    // 播放轨迹
    playTrajectory(trajectory) {
        if (!trajectory || !trajectory.points || trajectory.points.length < 2) {
            console.warn('轨迹无效或点数不足，无法播放');
            return;
        }
        
        // 如果已经在播放，先停止当前播放
        if (this.isPlaying) {
            this.stopPlayback();
        }
        
        console.log(`开始播放轨迹: ${trajectory.name || '未命名轨迹'}`);
        console.log('轨迹设置:', {
            duration: trajectory.duration || 60,
            isLoop: trajectory.isLoop || false,
            isReverse: trajectory.isReverse || false,
            pointCount: trajectory.points.length
        });
        
        // 设置播放状态
        this.isPlaying = true;
        this.playingTrajectories = [trajectory]; // 存储到播放轨迹数组中
        this.playStartTime = performance.now();
        this.currentPlayTime = 0;
        
        // 初始化播放标记
        this.playMarker = {
            x: 0,
            y: 0,
            radius: 8,
            color: '#00FF00'
        };
        
        // 更新UI状态 - 轨迹工具栏按钮
        if (this.playTrajectoryBtn) this.playTrajectoryBtn.style.display = 'none';
        if (this.stopTrajectoryBtn) this.stopTrajectoryBtn.style.display = 'inline-block';
        
        // 更新UI状态 - 全局按钮
        if (this.globalPlayBtn) this.globalPlayBtn.style.display = 'none';
        if (this.globalStopBtn) this.globalStopBtn.style.display = 'inline-block';
        if (this.playBtn) this.playBtn.style.display = 'none';
        if (this.stopBtn) this.stopBtn.style.display = 'inline-block';
        
        // 调用后台API开始播放轨迹
        this.callPlayTrajectoryApi(trajectory.id);
        
        // 开始动画循环
        this.animateTrajectory();
    }
    
    // 停止播放
    stopPlayback() {
        console.log('=== 停止播放被调用 ===');
        console.log('调用堆栈:', new Error().stack);
        console.log('当前状态:', {
            isPlaying: this.isPlaying,
            animationFrameId: this.animationFrameId,
            playingTrajectories: this.playingTrajectories.length,
            timestamp: new Date().toISOString()
        });
        
        try {
            // 强制停止，不管当前状态如何
        
        // 取消所有可能的动画帧
        if (this.animationFrameId !== null) {
            console.log('取消动画帧:', this.animationFrameId);
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        
        // 调用后台API停止所有轨迹播放
        this.callStopAllTrajectoryApi();
        
        // 强制重置所有播放状态
        this.isPlaying = false;
        this.playingTrajectories = [];
        this.playStartTime = null;
        this.currentPlayTime = 0;
        this.playMarkers = []; // 清除播放标记
        this.playMarker = null; // 清除单个播放标记
        
        console.log('重置播放状态完成');
        
        // 更新UI状态 - 轨迹工具栏按钮
        if (this.playTrajectoryBtn) this.playTrajectoryBtn.style.display = 'inline-block';
        if (this.stopTrajectoryBtn) this.stopTrajectoryBtn.style.display = 'none';
        
        // 更新UI状态 - 全局按钮
        if (this.globalPlayBtn) this.globalPlayBtn.style.display = 'inline-block';
        if (this.globalStopBtn) this.globalStopBtn.style.display = 'none';
        if (this.playBtn) this.playBtn.style.display = 'inline-block';
        if (this.stopBtn) this.stopBtn.style.display = 'inline-block';
        
        // 重绘画布，清除播放标记和动画效果
        if (this.canvas) {
            console.log('重绘画布清除播放效果');
            // 只操作当前实例，不访问其他可能的实例
            this.canvas.redrawCanvas();
        }
        
        console.log('停止播放完成');
        
        // 检查事件监听器是否仍然存在，如果丢失则立即恢复
        setTimeout(() => {
            const hasStopHandler = !!this.handleStopButtonClick;
            const hasPlayHandler = !!this.handlePlayButtonClick;
            
            console.log('停止后事件监听器状态检查:', {
                hasStopHandler,
                hasPlayHandler,
                eventListenersTimestamp: this.eventListenersTimestamp,
                stopHandlerTimestamp: this.handleStopButtonClick?._timestamp,
                playHandlerTimestamp: this.handlePlayButtonClick?._timestamp
            });
            
            if (!hasStopHandler || !hasPlayHandler) {
                console.warn('停止后事件监听器丢失！立即恢复...');
                
                // 立即恢复事件监听器
                if (!this.isInitializing) {
                    this.isInitializing = true;
                    try {
                        this.initEventListeners();
                        console.log('事件监听器已恢复');
                    } catch (error) {
                        console.error('恢复事件监听器失败:', error);
                    } finally {
                        this.isInitializing = false;
                    }
                }
            }
        }, 50);
        
        } catch (error) {
            console.error('停止播放过程中发生错误:', error);
            // 强制停止基本操作
            this.isPlaying = false;
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
                this.animationFrameId = null;
            }
        }
    }
    
    // 强制停止方法（备用）
    forceStop() {
        console.log('执行强制停止');
        try {
            this.isPlaying = false;
            this.playingTrajectories = [];
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
                this.animationFrameId = null;
            }
            // 调用后台API
            this.callStopAllTrajectoryApi();
        } catch (e) {
            console.error('强制停止也失败:', e);
        }
    }
    
    // 动画轨迹
    animateTrajectory() {
        // 更严格的停止检查
        if (!this.isPlaying || this.playingTrajectories.length === 0) {
            console.log('动画停止条件触发:', {
                isPlaying: this.isPlaying,
                playingTrajectories: this.playingTrajectories.length
            });
            return;
        }
        
        // 计算当前播放时间
        const now = performance.now();
        const elapsedTime = (now - this.playStartTime) / 1000; // 转换为秒
        this.currentPlayTime = elapsedTime;
        
        // 重绘画布
        if (this.canvas) {
            this.canvas.redrawCanvas();
        }
        
        // 检查是否所有非循环轨迹都已播放完成
        let allNonLoopingCompleted = true;
        let hasLoopingTrajectories = false;
        
        // 处理每个正在播放的轨迹
        for (let i = 0; i < this.playingTrajectories.length; i++) {
            const trajectory = this.playingTrajectories[i];
            const duration = trajectory.duration || 60; // 默认60秒
            const isLoop = trajectory.isLoop || false;
            const points = trajectory.points;
            
            if (!points || points.length < 2) continue;
            
            if (isLoop) {
                hasLoopingTrajectories = true;
            }
            
            // 检查是否需要循环或停止
            let currentTime = this.currentPlayTime;
            if (currentTime > duration) {
                if (isLoop) {
                    // 循环播放，计算当前循环内的时间
                    currentTime = currentTime % duration;
                } else {
                    // 非循环轨迹已播放完成
                    currentTime = duration;
                }
            }
            
            if (currentTime < duration || isLoop) {
                allNonLoopingCompleted = false;
            }
            
            // 根据当前时间计算播放位置
            const playPosition = this.calculatePlayPosition(points, currentTime, duration, trajectory.isReverse);
            
            // 绘制轨迹播放进度
            this.drawPlayingTrajectory(trajectory, currentTime, i);
        }
        
        // 如果所有非循环轨迹都播放完成且没有循环轨迹，停止播放
        if (allNonLoopingCompleted && !hasLoopingTrajectories) {
            this.stopPlayback();
            return;
        }
        
        // 再次检查是否仍在播放状态（防止在处理过程中被停止）
        if (this.isPlaying) {
            // 继续下一帧动画
            this.animationFrameId = requestAnimationFrame(() => this.animateTrajectory());
        } else {
            console.log('动画循环中检测到停止状态，终止动画');
        }
    }
    
    // 计算播放位置
    calculatePlayPosition(points, currentTime, duration, isReverse) {
        if (!points || points.length < 2) {
            return { x: 0, y: 0 };
        }
        
        // 计算播放进度比例 (0-1)
        let progress = currentTime / duration;
        
        // 如果是逆向播放，反转进度
        if (isReverse) {
            progress = 1 - progress;
        }
        
        // 计算当前应该在哪两个点之间
        const totalSegments = points.length - 1;
        const currentSegment = Math.min(Math.floor(progress * totalSegments), totalSegments - 1);
        const segmentProgress = (progress * totalSegments) - currentSegment;
        
        // 获取当前段的两个端点
        const startPoint = points[currentSegment];
        const endPoint = points[currentSegment + 1];
        
        // 线性插值计算当前位置
        return {
            x: startPoint.x + (endPoint.x - startPoint.x) * segmentProgress,
            y: startPoint.y + (endPoint.y - startPoint.y) * segmentProgress
        };
    }
    
    // 绘制播放中的轨迹
    drawPlayingTrajectory(trajectory, currentTime, index = 0) {
        if (!this.canvas || !this.canvas.ctx || !trajectory) return;
        
        const ctx = this.canvas.ctx;
        const points = trajectory.points;
        
        if (!points || points.length < 2) return;
        
        // 保存当前绘图状态
        ctx.save();
        
        // 绘制完整轨迹路径（灰色）
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i].x, points[i].y);
        }
        ctx.strokeStyle = '#888888';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // 计算已播放部分的点
        const playedPoints = [];
        const progress = currentTime / (trajectory.duration || 60);
        const totalPoints = points.length;
        const playedPointCount = Math.ceil(progress * totalPoints);
        
        // 如果是逆向播放，反转计算方式
        if (trajectory.isReverse) {
            for (let i = totalPoints - 1; i >= totalPoints - playedPointCount && i >= 0; i--) {
                playedPoints.push(points[i]);
            }
        } else {
            for (let i = 0; i < playedPointCount && i < totalPoints; i++) {
                playedPoints.push(points[i]);
            }
        }
        
        // 绘制已播放部分（绿色）
        if (playedPoints.length >= 2) {
            ctx.beginPath();
            ctx.moveTo(playedPoints[0].x, playedPoints[0].y);
            for (let i = 1; i < playedPoints.length; i++) {
                ctx.lineTo(playedPoints[i].x, playedPoints[i].y);
            }
            ctx.strokeStyle = '#00FF00';
            ctx.lineWidth = 3;
            ctx.stroke();
        }
        
        // 计算当前播放位置
        const playPosition = this.calculatePlayPosition(points, currentTime, trajectory.duration || 60, trajectory.isReverse);
        
        // 创建或更新播放标记
        if (!this.playMarker) {
            this.playMarker = {
                x: playPosition.x,
                y: playPosition.y,
                radius: 8,
                color: '#00FF00'
            };
        } else {
            this.playMarker.x = playPosition.x;
            this.playMarker.y = playPosition.y;
        }
        
        // 绘制播放标记（绿色圆点）
        ctx.beginPath();
        ctx.arc(this.playMarker.x, this.playMarker.y, this.playMarker.radius, 0, Math.PI * 2);
        ctx.fillStyle = this.playMarker.color;
        ctx.fill();
        
        // 恢复绘图状态
        ctx.restore();
    }
    
    // 播放所有轨迹
    playAllTrajectories() {
        // 如果已经在播放，先停止当前播放
        if (this.isPlaying) {
            this.stopPlayback();
        }
        
        // 获取所有轨迹
        const trajectories = this.canvas.trajectories;
        if (!trajectories || trajectories.length === 0) {
            console.warn('没有可用的轨迹进行播放');
            return;
        }
        
        console.log(`开始播放所有轨迹，总数: ${trajectories.length}`);
        
        // 设置播放状态
        this.isPlaying = true;
        this.playingTrajectories = [...trajectories]; // 复制轨迹数组
        this.playStartTime = performance.now();
        this.currentPlayTime = 0;
        
        // 更新UI状态 - 轨迹工具栏按钮
        if (this.playTrajectoryBtn) this.playTrajectoryBtn.style.display = 'none';
        if (this.stopTrajectoryBtn) this.stopTrajectoryBtn.style.display = 'inline-block';
        
        // 更新UI状态 - 全局按钮
        if (this.globalPlayBtn) this.globalPlayBtn.style.display = 'none';
        if (this.globalStopBtn) this.globalStopBtn.style.display = 'inline-block';
        if (this.playBtn) this.playBtn.style.display = 'none';
        if (this.stopBtn) this.stopBtn.style.display = 'inline-block';
        
        // 获取当前通道ID
        const channelId = this.getCurrentChannelId();
        if (channelId) {
            // 调用后台API播放通道下所有轨迹
            this.callPlayChannelApi(channelId);
        } else {
            // 如果无法获取通道ID，则尝试获取场景ID
            const sceneId = this.getCurrentSceneId();
            if (sceneId) {
                // 调用后台API播放场景下所有轨迹
                this.callPlaySceneApi(sceneId);
            } else {
                console.warn('无法获取通道ID或场景ID，无法播放轨迹');
                alert('无法获取通道信息，请先选择通道');
                return;
            }
        }
        
        // 开始动画循环
        this.animateTrajectory();
    }
    
    // 获取当前通道ID
    getCurrentChannelId() {
        // 尝试从全局应用实例获取当前通道
        if (window.app && window.app.currentChannel && window.app.currentChannel.id) {
            return window.app.currentChannel.id;
        }
        
        // 尝试从轨迹数据中获取通道ID
        if (this.canvas && this.canvas.trajectories && this.canvas.trajectories.length > 0) {
            const firstTrajectory = this.canvas.trajectories[0];
            if (firstTrajectory && firstTrajectory.channelId) {
                return firstTrajectory.channelId;
            }
        }
        
        // 尝试从URL参数获取通道ID
        const urlParams = new URLSearchParams(window.location.search);
        const channelId = urlParams.get('channelId');
        if (channelId) return channelId;
        
        return null;
    }
    
    // 获取当前场景ID
    getCurrentSceneId() {
        // 尝试从URL获取场景ID
        const urlParams = new URLSearchParams(window.location.search);
        const sceneId = urlParams.get('sceneId');
        if (sceneId) return sceneId;
        
        // 尝试从页面元素获取场景ID
        const sceneElement = document.getElementById('currentSceneId');
        if (sceneElement && sceneElement.value) return sceneElement.value;
        
        // 尝试从第一个轨迹的通道获取场景ID
        if (this.canvas && this.canvas.trajectories && this.canvas.trajectories.length > 0) {
            const firstTrajectory = this.canvas.trajectories[0];
            if (firstTrajectory && firstTrajectory.channelId) {
                // 这里需要通过API获取通道信息，然后获取场景ID
                // 由于这是异步操作，这里简化处理
                return null;
            }
        }
        
        return null;
    }
    
    // 调用API播放单个轨迹
    async callPlayTrajectoryApi(trajectoryId) {
        if (!trajectoryId) {
            console.error('轨迹ID为空，无法调用播放API');
            return false;
        }
        
        console.log(`调用API播放轨迹: ${trajectoryId}`);
        
        try {
            const response = await fetch('/api/TrajectoryPlayback/play', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    trajectoryId: trajectoryId
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                console.log(`轨迹 ${trajectoryId} 播放API调用成功:`, data.message);
                
                // 更新UI状态
                const playBtn = document.getElementById('playBtn');
                const stopBtn = document.getElementById('stopBtn');
                if (playBtn) {
                    playBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放中';
                    playBtn.disabled = true;
                }
                if (stopBtn) {
                    stopBtn.disabled = false;
                }
                
                return true;
            } else {
                console.error(`轨迹 ${trajectoryId} 播放API调用失败:`, data.message);
                return false;
            }
        } catch (error) {
            console.error(`轨迹 ${trajectoryId} 播放API调用出错:`, error);
            return false;
        }
    }
    
    // 调用API播放通道下所有轨迹
    callPlayChannelApi(channelId) {
        if (!channelId) {
            console.error('通道ID为空，无法调用播放API');
            return;
        }
        
        console.log(`调用API播放通道 ${channelId} 下所有轨迹`);
        
        fetch('/api/TrajectoryPlayback/play-channel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                channelId: channelId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`通道 ${channelId} 轨迹播放API调用成功:`, data.message);
            } else {
                console.error(`通道 ${channelId} 轨迹播放API调用失败:`, data.message);
            }
        })
        .catch(error => {
            console.error(`通道 ${channelId} 轨迹播放API调用出错:`, error);
        });
    }
    
    // 调用API播放场景下所有轨迹
    callPlaySceneApi(sceneId) {
        if (!sceneId) {
            console.error('场景ID为空，无法调用播放API');
            return;
        }
        
        console.log(`调用API播放场景 ${sceneId} 下所有轨迹`);
        
        fetch('/api/TrajectoryPlayback/play-scene', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sceneId: sceneId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`场景 ${sceneId} 轨迹播放API调用成功:`, data.message);
            } else {
                console.error(`场景 ${sceneId} 轨迹播放API调用失败:`, data.message);
            }
        })
        .catch(error => {
            console.error(`场景 ${sceneId} 轨迹播放API调用出错:`, error);
        });
    }
    
    // 调用API停止所有轨迹播放
    async callStopAllTrajectoryApi() {
        console.log('调用API停止所有轨迹播放');
        
        try {
            const response = await fetch('/api/TrajectoryPlayback/stop-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                console.log('停止所有轨迹播放API调用成功:', data.message);
                
                // 更新UI状态
                const playBtn = document.getElementById('playBtn');
                const stopBtn = document.getElementById('stopBtn');
                if (playBtn) {
                    playBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放';
                    playBtn.disabled = false;
                }
                if (stopBtn) {
                    stopBtn.innerHTML = '<i class="fas fa-stop me-1"></i>停止';
                    stopBtn.disabled = true;
                }
                
                return true;
            } else {
                console.error('停止所有轨迹播放API调用失败:', data.message);
                return false;
            }
        } catch (error) {
            console.error('停止所有轨迹播放API调用出错:', error);
            throw error;
        }
    }
}