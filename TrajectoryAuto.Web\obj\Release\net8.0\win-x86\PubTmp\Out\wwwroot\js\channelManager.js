class ChannelManager {
    constructor(app) {
        this.app = app;
        this.channels = [];
        this.currentChannel = null;
        this.selectedChannelId = null;
    }

    // 加载通道列表
    async loadChannels(sceneId) {
        if (!sceneId) {
            console.error('未提供场景ID，无法加载通道');
            return [];
        }
        
        console.log(`开始为场景 ${sceneId} 加载通道`);
        
        try {
            // 重置当前通道和选中状态
            this.currentChannel = null;
            this.selectedChannelId = null;
            
            // 同步重置全局应用对象的通道
            this.app.currentChannel = null;
            window.app.currentChannel = null;
            
            const response = await fetch(`/api/channels/scene/${sceneId}`);
            if (!response.ok) {
                console.error(`加载通道失败，状态码: ${response.status}`);
                return [];
            }
            
            this.channels = await response.json();
            console.log(`场景 ${sceneId} 加载到通道数量:`, this.channels.length);
            
            // 同步通道数据到全局应用对象
            this.app.channels = this.channels;
            window.app.channels = this.channels;
            
            // 清空通道列表并重新渲染
            this.renderChannels();
            
            // 如果有通道，默认选择第一个
            if (this.channels && this.channels.length > 0) {
                console.log('默认选择第一个通道:', this.channels[0].name, this.channels[0].id);
                
                try {
                    // 选择第一个通道
                    await this.selectChannel(this.channels[0]);
                    
                    // 确保轨迹加载并重绘
                    if (this.app.currentChannel && this.app.currentChannel.id) {
                        console.log('开始加载通道轨迹:', this.app.currentChannel.id);
                        try {
                            // 直接加载轨迹
                            const trajectories = await this.app.loadTrajectories(this.app.currentChannel.id);
                            console.log('轨迹加载完成，数量:', trajectories.length);
                            
                            // 确保轨迹数据同步到画布管理器
                            if (window.canvasManager) {
                                console.log('同步轨迹数据到画布管理器');
                                window.canvasManager.trajectories = [...trajectories];
                                
                                // 重绘画布
                                console.log('重绘画布显示轨迹');
                                window.canvasManager.redrawCanvas();
                            }
                        } catch (trajError) {
                            console.error('加载轨迹时出错:', trajError);
                        }
                    }
                } catch (selectError) {
                    console.error('选择通道失败:', selectError);
                }
            } else {
                console.warn(`场景 ${sceneId} 没有可用的通道`);
            }
            
            return this.channels;
        } catch (error) {
            console.error('加载通道失败:', error);
            return [];
        }
    }

    // 渲染通道列表
    renderChannels() {
        const channelList = document.getElementById('channelList');
        if (!channelList) return;

        channelList.innerHTML = '';
        this.channels.forEach(channel => {
            const channelItem = document.createElement('div');
            channelItem.className = 'channel-item';
            channelItem.dataset.id = channel.id;
            
            // 根据当前选中的通道ID设置样式，而不是依赖channel.isActive属性
            if (this.selectedChannelId === channel.id) {
                channelItem.classList.add('selected');
                channelItem.classList.add('active');
            }

            channelItem.innerHTML = `
                <h4>${channel.name || '未命名通道'}</h4>
                <p>通道号: ${channel.channelNumber || 'N/A'}</p>
                <p>IP: ${channel.ipAddress || 'N/A'}:${channel.port || 'N/A'}</p>
                <div class="channel-color" style="background-color: ${channel.color || '#FF0000'}"></div>
            `;

            channelList.appendChild(channelItem);
        });
    }

    // 选择通道
    async selectChannel(channel) {
        if (!channel) {
            console.error('选择通道失败：通道对象为空');
            return;
        }

        console.log('开始选择通道:', channel.id, channel.name);
        this.currentChannel = channel;
        this.selectedChannelId = channel.id;
        
        // 同步更新app的currentChannel
        this.app.currentChannel = channel;
        window.app.currentChannel = channel;
        
        // 确保全局通道数组中包含当前通道
        if (this.app.channels && Array.isArray(this.app.channels)) {
            // 检查当前通道是否已存在于全局通道数组中
            const existingChannelIndex = this.app.channels.findIndex(c => c.id === channel.id);
            if (existingChannelIndex >= 0) {
                // 更新现有通道
                this.app.channels[existingChannelIndex] = channel;
                window.app.channels[existingChannelIndex] = channel;
            } else {
                // 添加新通道
                this.app.channels.push(channel);
                window.app.channels.push(channel);
            }
            console.log('已更新全局通道数组，当前数量:', this.app.channels.length);
        } else {
            // 初始化全局通道数组
            this.app.channels = [channel];
            window.app.channels = [channel];
            console.log('已初始化全局通道数组');
        }
        
        // 更新UI状态，高亮选中的通道
        document.querySelectorAll('.channel-item').forEach(item => {
            item.classList.remove('selected');
            item.classList.remove('active');
        });
        
        const selectedItem = document.querySelector(`.channel-item[data-id="${channel.id}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
            selectedItem.classList.add('active');
            console.log('更新UI状态，高亮选中通道:', channel.id);
        } else {
            console.warn('找不到通道对应的UI元素:', channel.id);
        }
        
        console.log('已选择通道:', channel);
        console.log('app.currentChannel:', this.app.currentChannel);
        console.log('window.app.currentChannel:', window.app.currentChannel);
        
        // 更新UI状态
        const channelItems = document.querySelectorAll('.channel-item');
        channelItems.forEach(item => {
            // 清除所有通道的选中和激活状态
            item.classList.remove('selected');
            item.classList.remove('active');
            
            // 给当前选中的通道添加选中和激活状态
            if (item.dataset.id === channel.id) {
                item.classList.add('selected');
                item.classList.add('active');
            }
        });
        
        // 重置画布状态，但保留背景图片
        if (window.canvasManager) {
            console.log('重置画布状态，保留背景图片');
            
            // 保存当前背景图片
            const currentBackgroundImage = window.canvasManager.backgroundImage;
            
            // 重置画布状态
            window.canvasManager.currentPath = [];
            window.canvasManager.isDrawing = false;
            window.canvasManager.isDrawingShape = false;
            window.canvasManager.shapeStartPoint = null;
            
            // 如果当前场景有背景图片，则保留它
            if (currentBackgroundImage || this.app.sceneManager?.currentScene?.backgroundImagePath) {
                // 使用当前背景图片或从场景中加载
                if (!currentBackgroundImage && this.app.sceneManager?.currentScene?.backgroundImagePath) {
                    // 从场景中加载背景图片
                    window.canvasManager.setBackground(this.app.sceneManager.currentScene.backgroundImagePath);
                    console.log('从场景加载背景图片:', this.app.sceneManager.currentScene.backgroundImagePath);
                } else {
                    // 保留当前背景图片
                    window.canvasManager.backgroundImage = currentBackgroundImage;
                    console.log('保留当前背景图片');
                }
            }
            window.canvasManager.shapeCurrentPoint = null;
            window.canvasManager.polygonPoints = null;
            
            // 重新应用防止选中的设置
            window.canvasManager.preventCanvasSelection();
            
            // 强制重绘画布，确保白色背景
            const canvas = document.getElementById('trajectoryCanvas');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
            }
        }

        // 激活通道
        try {
            const sceneId = this.app.sceneManager?.currentScene?.id;
            if (!sceneId) {
                console.error('未选择场景，无法激活通道');
                return;
            }

            console.log(`开始激活通道 ${channel.id} 在场景 ${sceneId} 中`);
            const response = await fetch(`/api/channels/${channel.id}/activate?sceneId=${sceneId}`, {
                method: 'POST'
            });

            if (!response.ok) {
                console.error(`激活通道失败: 状态码 ${response.status}`);
                return;
            }

            console.log('激活通道成功，开始加载轨迹');
            
            // 直接使用app的loadTrajectories方法加载轨迹
            try {
                const trajectories = await this.app.loadTrajectories(channel.id);
                console.log(`轨迹加载完成，数量: ${trajectories.length}`);
                
                // 确保轨迹数据同步到画布管理器
                if (window.canvasManager) {
                    console.log('同步轨迹数据到画布管理器');
                    window.canvasManager.trajectories = [...trajectories];
                    
                    // 轨迹加载完成后立即重绘画布
                    console.log('开始重绘画布显示轨迹');
                    window.canvasManager.redrawCanvas();
                    
                    // 默认选中"选择"工具
                    if (this.app.selectTool && typeof this.app.selectTool === 'function') {
                        console.log('通道切换后，默认选中选择工具');
                        this.app.selectTool('select');
                    } else if (window.app && window.app.selectTool && typeof window.app.selectTool === 'function') {
                        console.log('通道切换后，默认选中选择工具(通过window.app)');
                        window.app.selectTool('select');
                    }
                    
                    // 再次延迟重绘，确保轨迹显示
                    setTimeout(() => {
                        console.log('延迟重绘画布，确保轨迹显示');
                        window.canvasManager.redrawCanvas();
                    }, 200);
                } else {
                    console.error('画布管理器不存在，无法重绘画布');
                }
            } catch (trajectoryError) {
                console.error('加载轨迹失败:', trajectoryError);
                // 即使加载轨迹失败，仍然尝试重绘画布
                if (window.canvasManager) {
                    window.canvasManager.redrawCanvas();
                }
            }
        } catch (error) {
            console.error('激活通道失败:', error);
            // 不抛出错误，避免中断流程
            // 尝试重绘画布，显示当前状态
            if (window.canvasManager) {
                window.canvasManager.redrawCanvas();
            }
        }
    }

    // 编辑通道
    async editChannel(channelId) {
        try {
            const response = await fetch(`/api/channels/${channelId}`);
            if (!response.ok) {
                throw new Error('获取通道信息失败');
            }
            
            const channel = await response.json();
            this.showChannelModal(channel);
        } catch (error) {
            console.error('编辑通道出错:', error);
            alert('编辑通道时出错: ' + error.message);
        }
    }
    
    // 删除通道
    async deleteChannel(channelId) {
        try {
            if (!confirm('确定要删除这个通道吗？')) {
                return;
            }

            const response = await fetch(`/api/channels/${channelId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                // 清除选中状态
                this.selectedChannelId = null;
                document.getElementById('channelActions').style.display = 'none';
                
                // 重新加载通道列表
                if (this.app.sceneManager?.currentScene?.id) {
                    await this.loadChannels(this.app.sceneManager.currentScene.id);
                }
            } else {
                throw new Error('删除通道失败');
            }
        } catch (error) {
            console.error('删除通道出错:', error);
            alert('删除通道时出错: ' + error.message);
        }
    }
    
    // 显示通道模态框
    showChannelModal(channel = null) {
        console.log('显示通道模态框被调用，当前状态:', {
            selectedSceneId: this.app.sceneManager?.selectedSceneId,
            currentScene: this.app.sceneManager?.currentScene,
            scenes: this.app.sceneManager?.scenes?.length
        });
        
        // 先检查场景列表是否加载
        if (!this.app.sceneManager?.scenes || this.app.sceneManager.scenes.length === 0) {
            console.log('场景列表为空，尝试加载场景');
            // 尝试加载场景列表
            this.app.sceneManager.loadScenes().then(() => {
                // 加载后再次调用显示模态框
                this.showChannelModal(channel);
            }).catch(err => {
                console.error('加载场景列表失败:', err);
                alert('加载场景列表失败，请刷新页面重试');
            });
            return;
        }
        
        // 检查是否有场景被选中
        const hasSelectedScene = this.app.sceneManager?.selectedSceneId || this.app.sceneManager?.currentScene;
        
        if (!hasSelectedScene) {
            console.log('没有选中的场景');
            alert('请先选择一个场景');
            return;
        }
        
        // 确保 currentScene 与 selectedSceneId 一致
        if (this.app.sceneManager.selectedSceneId && (!this.app.sceneManager.currentScene || this.app.sceneManager.currentScene.id != this.app.sceneManager.selectedSceneId)) {
            const selectedScene = this.app.sceneManager.scenes.find(s => s.id == this.app.sceneManager.selectedSceneId);
            if (selectedScene) {
                this.app.sceneManager.currentScene = selectedScene;
                console.log('已更新当前场景与选中场景ID一致:', selectedScene);
            }
        }
        
        const modal = document.getElementById('channelModal');
        const form = document.getElementById('channelForm');
        const modalTitle = document.getElementById('channelModalTitle');
        
        if (!modal || !form || !modalTitle) return;
        
        if (channel) {
            // 编辑模式
            modalTitle.innerHTML = '<i class="fas fa-edit me-2"></i>编辑通道';
            
            console.log('编辑通道，设置表单值:', channel);
            
            // 设置表单值
            document.getElementById('channelName').value = channel.name || '';
            document.getElementById('channelNumber').value = channel.channelNumber || '';
            document.getElementById('channelIp').value = channel.ipAddress || '';
            document.getElementById('channelPort').value = channel.port || '';
            document.getElementById('channelColor').value = channel.color || '#FF0000';
            
            // 设置通道地址
            document.getElementById('Address').value = channel.address || '';
            
            // 设置Z轴相关值
            document.getElementById('UseZAxis').checked = channel.useZAxis || false;
            document.getElementById('ZAxisMin').value = channel.zAxisMin || 0.0;
            document.getElementById('ZAxisMax').value = channel.zAxisMax || 1.0;
            
            // 存储通道ID
            let idInput = document.getElementById('channelId');
            if (!idInput) {
                idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.id = 'channelId';
                idInput.name = 'id';
                form.insertBefore(idInput, form.firstChild);
            }
            idInput.value = channel.id;
        } else {
            // 新建模式
            modalTitle.innerHTML = '<i class="fas fa-plus-circle me-2"></i>新建通道';
            form.reset();
            
            // 设置默认通道号
            const nextChannelNumber = this.channels.length > 0 ? 
                Math.max(...this.channels.map(c => c.channelNumber || 0)) + 1 : 1;
            document.getElementById('channelNumber').value = nextChannelNumber;
            
            // 设置默认通道名称与通道编号相同
            document.getElementById('channelName').value = `通道${nextChannelNumber}`;
            
            // 设置默认通道地址为/source/{通道号}/
            document.getElementById('Address').value = `/source/${nextChannelNumber}/`;
            
            // 设置IP地址和端口与场景一致
            if (this.app.sceneManager?.currentScene) {
                const currentScene = this.app.sceneManager.currentScene;
                document.getElementById('channelIp').value = currentScene.serverIp || '127.0.0.1';
                document.getElementById('channelPort').value = currentScene.serverPort || '8080';
            } else {
                document.getElementById('channelIp').value = '127.0.0.1';
                document.getElementById('channelPort').value = '8080';
            }
            
            // 设置默认颜色
            document.getElementById('channelColor').value = '#FF0000';
            
            // 移除隐藏的ID字段（如果存在）
            const idInput = document.getElementById('channelId');
            if (idInput && idInput.parentNode) {
                form.removeChild(idInput);
            }
        }
        
        // 使用与场景模态框相同的显示方式
        console.log('设置通道模态框样式');
        
        // 设置模态框容器样式
        modal.style.display = 'block';
        modal.style.position = 'fixed';
        modal.style.zIndex = '9999';
        modal.style.left = '0';
        modal.style.top = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.overflow = 'auto';
        modal.style.backgroundColor = 'rgba(0,0,0,0.4)';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        
        // 设置模态框内容样式
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.opacity = '1';
            modalContent.style.visibility = 'visible';
            modalContent.style.zIndex = '10000';
            modalContent.style.position = 'relative';
            modalContent.style.margin = '10% auto';
            modalContent.style.width = '80%';
            modalContent.style.maxWidth = '600px';
            modalContent.style.backgroundColor = '#fff';
            modalContent.style.borderRadius = '5px';
            modalContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
            modalContent.style.overflow = 'hidden';
            
            // 处理模态框标题栏圆角问题
            const modalHeader = modalContent.querySelector('.modal-header');
            if (modalHeader) {
                modalHeader.style.borderTopLeftRadius = '5px';
                modalHeader.style.borderTopRightRadius = '5px';
                modalHeader.style.backgroundColor = '#f8f9fa';
                modalHeader.style.padding = '10px 15px';
                modalHeader.style.borderBottom = '1px solid #dee2e6';
            }
        }
        
        // 确保模态框在body下
        if (!modal.parentNode || modal.parentNode.nodeName.toLowerCase() !== 'body') {
            document.body.appendChild(modal);
        }
        
        // 添加点击事件测试
        modal.addEventListener('click', (e) => {
            console.log('点击了通道模态框', e.target);
        });
        
        // 强制重绘模态框
        modal.style.display = 'none';
        setTimeout(() => {
            modal.style.display = 'block';
            console.log('通道模态框强制重绘完成');
        }, 10);
    }

    // 隐藏通道模态框
    hideChannelModal() {
        console.log('隐藏通道模态框');
        const modal = document.getElementById('channelModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 保存通道
    async saveChannel() {
        const form = document.getElementById('channelForm');
        if (!form || !this.app.sceneManager?.currentScene) return;

        const formData = new FormData(form);
        // 获取表单数据
        const channelData = {
            // 如果是新建通道，不发送id字段，让后端自动生成
            name: formData.get('channelName') || `通道 ${formData.get('channelNumber')}`,
            channelNumber: parseInt(formData.get('channelNumber')) || 1,
            ipAddress: formData.get('channelIp') || '127.0.0.1',
            port: parseInt(formData.get('channelPort')) || 8080,
            color: formData.get('channelColor') || '#FF0000',
            isActive: false,
            // 从表单中获取Z轴相关设置
            useZAxis: formData.get('UseZAxis') === 'on',
            zAxisMin: parseFloat(formData.get('ZAxisMin')) || 0.0,
            zAxisMax: parseFloat(formData.get('ZAxisMax')) || 1.0,
            // 从表单中获取通道地址
            address: formData.get('Address') || ''
        };
        
        console.log('通道数据准备:', channelData);
        
        // 获取场景 ID
        const sceneIdStr = this.app.sceneManager.currentScene?.id || 
                          this.app.sceneManager.selectedSceneId || 
                          (this.app.sceneManager.scenes && this.app.sceneManager.scenes.length > 0 ? 
                           this.app.sceneManager.scenes[0].id : '');
        
        // 将场景 ID 设置到通道数据中
        if (!sceneIdStr) {
            alert('场景 ID 不能为空，请选择一个场景');
            return;
        }
        
        // 设置场景 ID
        channelData.sceneId = sceneIdStr;
        console.log('设置通道的场景 ID:', channelData.sceneId);
        
        // 如果是编辑现有通道，需要设置 id
        const existingId = formData.get('id');
        if (existingId) {
            channelData.id = existingId;
        }

        try {
            const url = channelData.id ? `/api/channels/${channelData.id}` : '/api/channels';
            const method = channelData.id ? 'PUT' : 'POST';
            
            console.log('发送通道数据:', JSON.stringify(channelData, null, 2));
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(channelData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('服务器响应错误:', response.status, errorText);
                throw new Error(`保存通道失败: ${response.status} ${errorText}`);
            }

            // 重新加载通道列表
            await this.loadChannels(this.app.sceneManager.currentScene.id);
            
            // 保存后选择当前编辑的通道，而不是默认选择第一个
            const savedChannelId = channelData.id;
            if (savedChannelId) {
                // 在重新加载的通道列表中找到当前编辑的通道
                const savedChannel = this.channels.find(c => c.id === savedChannelId);
                if (savedChannel) {
                    console.log('保存后选择当前编辑的通道:', savedChannel.name);
                    await this.selectChannel(savedChannel);
                }
            }
            
            this.hideChannelModal();
            
            return await response.json();
        } catch (error) {
            console.error('保存通道失败:', error);
            alert('保存通道失败: ' + error.message);
            throw error;
        }
    }
}
