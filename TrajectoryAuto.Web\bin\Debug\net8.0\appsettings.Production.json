{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "NewLife": "Error"}}, "Performance": {"EnableSQLiteOptimization": true, "BatchSize": 2000, "CacheTimeout": 600, "MaxConcurrentConnections": 200}, "AudioLightSync": {"EnableHighPerformanceMode": true, "MaxLatencyMs": 5, "BufferSize": 8192, "SampleRate": 48000}, "PlaybackPerformance": {"MaxConcurrentTasks": 100, "ChannelCapacity": 20000, "CleanupIntervalMinutes": 3, "MaxUdpLatencyMs": 50, "MemoryThresholdMB": 2000, "EnablePerformanceMonitoring": true, "PerformanceReportIntervalSeconds": 60}, "UdpConnectionPool": {"IdleTimeoutMinutes": 15, "MaxConnectionsPerType": 200, "AutoCleanupIntervalMinutes": 3, "EnableConnectionReuse": true, "ConnectionHealthCheckIntervalMinutes": 1, "MaxConnectionAge": 120, "PreferredConnectionType": "Channel"}}