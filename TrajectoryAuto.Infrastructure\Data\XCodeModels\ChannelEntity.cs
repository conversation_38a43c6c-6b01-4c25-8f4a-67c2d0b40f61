using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using NewLife.Data;
using XCode;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace TrajectoryAuto.Infrastructure.Data.XCodeModels;

/// <summary>通道实体 - 高性能音频灯光联动优化</summary>
[BindTable("Channels", Description = "通道表", ConnName = "TrajectoryAuto", DbType = DatabaseType.SQLite)]
public partial class ChannelEntity : Entity<ChannelEntity>
{
    #region 属性
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 50)]
    [BindColumn("Id", "编号", "")]
    public String Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }
    private String _Id = "";

    /// <summary>通道名称</summary>
    [DisplayName("通道名称")]
    [Description("通道名称")]
    [DataObjectField(false, false, false, 100)]
    [BindColumn("Name", "通道名称", "", Master = true)]
    public String Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }
    private String _Name = "";

    /// <summary>通道编号</summary>
    [DisplayName("通道编号")]
    [Description("通道编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ChannelNumber", "通道编号", "")]
    public Int32 ChannelNumber { get => _ChannelNumber; set { if (OnPropertyChanging("ChannelNumber", value)) { _ChannelNumber = value; OnPropertyChanged("ChannelNumber"); } } }
    private Int32 _ChannelNumber;

    /// <summary>通道地址</summary>
    [DisplayName("通道地址")]
    [Description("通道地址")]
    [DataObjectField(false, false, true, 100)]
    [BindColumn("Address", "通道地址", "")]
    public String? Address { get => _Address; set { if (OnPropertyChanging("Address", value)) { _Address = value; OnPropertyChanged("Address"); } } }
    private String? _Address;

    /// <summary>IP地址</summary>
    [DisplayName("IP地址")]
    [Description("IP地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("IpAddress", "IP地址", "")]
    public String? IpAddress { get => _IpAddress; set { if (OnPropertyChanging("IpAddress", value)) { _IpAddress = value; OnPropertyChanged("IpAddress"); } } }
    private String? _IpAddress;

    /// <summary>端口</summary>
    [DisplayName("端口")]
    [Description("端口")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Port", "端口", "")]
    public Int32 Port { get => _Port; set { if (OnPropertyChanging("Port", value)) { _Port = value; OnPropertyChanged("Port"); } } }
    private Int32 _Port;

    /// <summary>颜色</summary>
    [DisplayName("颜色")]
    [Description("颜色")]
    [DataObjectField(false, false, true, 20)]
    [BindColumn("Color", "颜色", "")]
    public String? Color { get => _Color; set { if (OnPropertyChanging("Color", value)) { _Color = value; OnPropertyChanged("Color"); } } }
    private String? _Color;

    /// <summary>是否激活</summary>
    [DisplayName("是否激活")]
    [Description("是否激活")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("IsActive", "是否激活", "")]
    public Boolean IsActive { get => _IsActive; set { if (OnPropertyChanging("IsActive", value)) { _IsActive = value; OnPropertyChanged("IsActive"); } } }
    private Boolean _IsActive;
    
    /// <summary>是否使用Z轴</summary>
    [DisplayName("是否使用Z轴")]
    [Description("是否使用Z轴")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UseZAxis", "是否使用Z轴", "")]
    public Boolean UseZAxis { get => _UseZAxis; set { if (OnPropertyChanging("UseZAxis", value)) { _UseZAxis = value; OnPropertyChanged("UseZAxis"); } } }
    private Boolean _UseZAxis = false;
    
    /// <summary>Z轴数据范围最小值</summary>
    [DisplayName("Z轴数据范围最小值")]
    [Description("Z轴数据范围最小值")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ZAxisMin", "Z轴数据范围最小值", "")]
    public Double ZAxisMin { get => _ZAxisMin; set { if (OnPropertyChanging("ZAxisMin", value)) { _ZAxisMin = value; OnPropertyChanged("ZAxisMin"); } } }
    private Double _ZAxisMin = 0.0;
    
    /// <summary>Z轴数据范围最大值</summary>
    [DisplayName("Z轴数据范围最大值")]
    [Description("Z轴数据范围最大值")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ZAxisMax", "Z轴数据范围最大值", "")]
    public Double ZAxisMax { get => _ZAxisMax; set { if (OnPropertyChanging("ZAxisMax", value)) { _ZAxisMax = value; OnPropertyChanged("ZAxisMax"); } } }
    private Double _ZAxisMax = 1.0;

    /// <summary>场景编号</summary>
    [DisplayName("场景编号")]
    [Description("场景编号")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("SceneId", "场景编号", "")]
    public String SceneId { get => _SceneId; set { if (OnPropertyChanging("SceneId", value)) { _SceneId = value; OnPropertyChanged("SceneId"); } } }
    private String _SceneId = "";

    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }
    private DateTime _CreateTime;

    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }
    private DateTime _UpdateTime;
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get
        {
            switch (name)
            {
                case "Id": return _Id;
                case "Name": return _Name;
                case "ChannelNumber": return _ChannelNumber;
                case "Address": return _Address;
                case "IpAddress": return _IpAddress;
                case "Port": return _Port;
                case "Color": return _Color;
                case "IsActive": return _IsActive;
                case "UseZAxis": return _UseZAxis;
                case "ZAxisMin": return _ZAxisMin;
                case "ZAxisMax": return _ZAxisMax;
                case "SceneId": return _SceneId;
                case "CreateTime": return _CreateTime;
                case "UpdateTime": return _UpdateTime;
                default: return base[name];
            }
        }
        set
        {
            switch (name)
            {
                case "Id": _Id = Convert.ToString(value) ?? ""; break;
                case "Name": _Name = Convert.ToString(value) ?? ""; break;
                case "ChannelNumber": _ChannelNumber = Convert.ToInt32(value); break;
                case "Address": _Address = Convert.ToString(value); break;
                case "IpAddress": _IpAddress = Convert.ToString(value); break;
                case "Port": _Port = Convert.ToInt32(value); break;
                case "Color": _Color = Convert.ToString(value); break;
                case "IsActive": _IsActive = Convert.ToBoolean(value); break;
                case "UseZAxis": _UseZAxis = Convert.ToBoolean(value); break;
                case "ZAxisMin": _ZAxisMin = Convert.ToDouble(value); break;
                case "ZAxisMax": _ZAxisMax = Convert.ToDouble(value); break;
                case "SceneId": _SceneId = Convert.ToString(value) ?? ""; break;
                case "CreateTime": _CreateTime = Convert.ToDateTime(value); break;
                case "UpdateTime": _UpdateTime = Convert.ToDateTime(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 字段名
    /// <summary>取得通道实体字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>通道名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>通道编号</summary>
        public static readonly Field ChannelNumber = FindByName("ChannelNumber");

        /// <summary>通道地址</summary>
        public static readonly Field Address = FindByName("Address");

        /// <summary>IP地址</summary>
        public static readonly Field IpAddress = FindByName("IpAddress");

        /// <summary>端口</summary>
        public static readonly Field Port = FindByName("Port");

        /// <summary>颜色</summary>
        public static readonly Field Color = FindByName("Color");

        /// <summary>是否激活</summary>
        public static readonly Field IsActive = FindByName("IsActive");
        
        /// <summary>是否使用Z轴</summary>
        public static readonly Field UseZAxis = FindByName("UseZAxis");
        
        /// <summary>Z轴数据范围最小值</summary>
        public static readonly Field ZAxisMin = FindByName("ZAxisMin");
        
        /// <summary>Z轴数据范围最大值</summary>
        public static readonly Field ZAxisMax = FindByName("ZAxisMax");

        /// <summary>场景编号</summary>
        public static readonly Field SceneId = FindByName("SceneId");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }
    #endregion

    #region 对象操作
    static ChannelEntity()
    {
        Meta.Modules.Add<TimeModule>();
    }

    public override void Valid(Boolean isNew)
    {
        if (!HasDirty) return;
        base.Valid(isNew);

        if (String.IsNullOrEmpty(Name)) throw new ArgumentNullException(nameof(Name), "通道名称不能为空！");
        if (ChannelNumber <= 0) throw new ArgumentException("通道编号必须大于0！");
        if (String.IsNullOrEmpty(SceneId)) throw new ArgumentNullException(nameof(SceneId), "场景ID不能为空！");
    }
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static ChannelEntity? FindById(String id)
    {
        if (String.IsNullOrEmpty(id)) return null;
        return Find(_.Id == id);
    }

    /// <summary>根据场景ID查找所有通道</summary>
    public static IList<ChannelEntity> FindBySceneId(String sceneId)
    {
        if (String.IsNullOrEmpty(sceneId)) return new List<ChannelEntity>();
        return FindAll(_.SceneId == sceneId).OrderBy(e => e.ChannelNumber).ToList();
    }

    /// <summary>批量设置通道状态</summary>
    public static Int32 SetActiveChannel(String sceneId, String activeChannelId)
    {
        using var trans = Meta.CreateTrans();
        
        // 先将所有通道设为非活动
        var count = Update(_.IsActive == false, _.SceneId == sceneId);
        
        // 再设置指定通道为活动
        if (!String.IsNullOrEmpty(activeChannelId))
        {
            Update(_.IsActive == true, _.Id == activeChannelId & _.SceneId == sceneId);
        }
        
        trans.Commit();
        return count;
    }
    #endregion

    #region 业务操作
    public TrajectoryAuto.Core.Entities.Channel ToBusinessEntity()
    {
        return new TrajectoryAuto.Core.Entities.Channel
        {
            Id = Guid.Parse(Id),
            Name = Name,
            ChannelNumber = ChannelNumber,
            Address = Address ?? $"/source/{ChannelNumber}/",
            IpAddress = IpAddress ?? "",
            Port = Port,
            Color = Color ?? "",
            IsActive = IsActive,
            UseZAxis = UseZAxis,
            ZAxisMin = ZAxisMin,
            ZAxisMax = ZAxisMax,
            SceneId = Guid.Parse(SceneId),
            CreatedAt = CreateTime
        };
    }

    public void FromBusinessEntity(TrajectoryAuto.Core.Entities.Channel channel)
    {
        Id = channel.Id.ToString();
        Name = channel.Name;
        ChannelNumber = channel.ChannelNumber;
        Address = channel.Address;
        IpAddress = channel.IpAddress;
        Port = channel.Port;
        Color = channel.Color;
        IsActive = channel.IsActive;
        UseZAxis = channel.UseZAxis;
        ZAxisMin = channel.ZAxisMin;
        ZAxisMax = channel.ZAxisMax;
        SceneId = channel.SceneId.ToString();
    }
    #endregion
}