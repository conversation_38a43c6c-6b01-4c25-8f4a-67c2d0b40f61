/* 科技感增强的自定义样式 */
:root {
    --tech-primary: #00a8ff;
    --tech-secondary: #0097e6;
    --tech-accent: #00d2d3;
    --tech-dark: #2f3542;
    --tech-light: #f1f2f6;
    --tech-highlight: #00d2d3;
    --tech-gradient: linear-gradient(135deg, #00a8ff 0%, #00d2d3 100%);
    --tech-shadow: 0 4px 12px rgba(0, 168, 255, 0.2);
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f7fa;
}

/* 科技感头部 */
.app-header {
    background: var(--tech-dark);
    background-image: linear-gradient(to right, rgba(0, 168, 255, 0.1) 1px, transparent 1px),
                      linear-gradient(to bottom, rgba(0, 168, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    color: white;
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    height: 150px;
    margin-bottom: 5px;
    padding: 1.5rem 2rem;
}

.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--tech-gradient);
    z-index: 2;
}

.app-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
}

.app-header h1 {
    font-size: 1.3rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    margin: 0;
    display: flex;
    align-items: center;
    position: relative;
}

.app-header h1 i {
    color: var(--tech-highlight);
    margin-right: 10px;
    font-size: 1.2rem;
}

.tech-highlight {
    color: var(--tech-highlight);
    font-weight: 600;
    margin-left: 5px;
    position: relative;
}

.tech-highlight::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--tech-highlight);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.app-header h1:hover .tech-highlight::after {
    transform: scaleX(1);
    transform-origin: left;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-control-group {
    display: flex;
    gap: 0.3rem;
    margin-left: 0.5rem;
}

.control-group .btn {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    white-space: nowrap;
}

.header-con {
    display: flex;
    gap: 0.3rem;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 0.25rem;
}

/* 科技感按钮 */
.header-controls .btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 210, 211, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.header-controls .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.header-controls .btn:hover {
    background: var(--tech-primary);
    border-color: var(--tech-highlight);
    box-shadow: 0 0 15px rgba(0, 210, 211, 0.4);
    transform: translateY(-1px);
}

.header-controls .btn:hover::before {
    left: 100%;
}

.header-controls .btn i {
    margin-right: 5px;
    font-size: 0.9rem;
}

.tool-btn {
    padding: 0.35rem 0.7rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    margin-right: 0.3rem;
    white-space: nowrap;
}

.tool-btn i {
    margin-right: 0.3rem;
    font-size: 0.8rem;
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
    padding: 0.4rem 0.75rem;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.05);
    font-size: 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator i {
    color: #ff6b6b;
    margin-right: 5px;
    font-size: 0.7rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.5;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.5;
    }
}

/* 主内容区域与头部的间隔 */
.app-main {
    margin-top: 5px;
    padding-top: 0;
    position: relative;
}

.app-main::before {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 98%;
    height: 1px;
    background: var(--tech-gradient);
    opacity: 0.5;
}

/* 科技感面板 */
.scene-panel, .channel-panel {
    background: white;
    border: 1px solid rgba(0, 168, 255, 0.1);
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.panel-header {
    background: #f8f9fa;
    border-bottom: 1px solid rgba(0, 168, 255, 0.2);
    padding: 0.75rem 1rem;
}

.panel-header h2 {
    font-size: 1rem;
    color: var(--tech-dark);
    position: relative;
    padding-left: 15px;
}

.panel-header h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: var(--tech-gradient);
    border-radius: 2px;
}

/* 科技感工具按钮 */
.tool-btn {
    background: white;
    border: 1px solid #e8ecf0;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.tool-btn:hover {
    border-color: var(--tech-primary);
    box-shadow: 0 2px 8px rgba(0, 168, 255, 0.15);
    transform: translateY(-1px);
}

.tool-btn.active {
    background: var(--tech-gradient);
    color: white;
    border-color: var(--tech-primary);
}

/* 画布区域 */
.canvas-panel {
    background: white;
    border: 1px solid rgba(0, 168, 255, 0.1);
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

#trajectoryCanvas {
    border: 1px solid rgba(0, 168, 255, 0.2);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.coordinate-display {
    background: rgba(47, 53, 66, 0.8);
    border: 1px solid var(--tech-primary);
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    font-family: 'Consolas', monospace;
    font-size: 0.8rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .app-header {
        padding: 0.5rem 1rem;
        height: 50px;
    }
    
    .app-header h1 {
        font-size: 1.1rem;
    }
    
    .header-controls .btn {
        padding: 0.4rem 0.75rem;
        font-size: 0.8rem;
    }
}