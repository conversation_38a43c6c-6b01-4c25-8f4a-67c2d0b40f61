using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TrajectoryAuto.Core.Interfaces;

namespace TrajectoryAuto.Infrastructure.Services
{
    /// <summary>
    /// 轨迹UDP通信服务
    /// </summary>
    public class TrajectoryUdpCommunicationService
    {
        private readonly ILogger<TrajectoryUdpCommunicationService> _logger;
        private readonly IUdpConnectionPoolManager _connectionPoolManager;
        private readonly DirectUdpService _directUdpService;
        
        // 是否使用直接UDP服务（不使用连接池）
        private bool _useDirectUdp = true;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="connectionPoolManager">UDP连接池管理器</param>
        /// <param name="directUdpService">直接UDP服务</param>
        public TrajectoryUdpCommunicationService(
            ILogger<TrajectoryUdpCommunicationService> logger,
            IUdpConnectionPoolManager connectionPoolManager,
            DirectUdpService directUdpService)
        {
            _logger = logger;
            _connectionPoolManager = connectionPoolManager;
            _directUdpService = directUdpService;
        }

        /// <summary>
        /// 发送轨迹数据
        /// </summary>
        /// <param name="trajectoryId">轨迹ID</param>
        /// <param name="channelId">通道ID</param>
        /// <param name="sceneId">场景ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <param name="port">端口号</param>
        /// <param name="data">数据</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> SendTrajectoryDataAsync(int trajectoryId, int channelId, int sceneId, string ipAddress, int port, byte[] data)
        {
            try
            {
                _logger.LogInformation($"发送轨迹数据: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}, IP={ipAddress}, 端口={port}, 数据长度={data.Length}");
                
                if (_useDirectUdp)
                {
                    // 使用直接UDP服务发送数据
                    bool result = await _directUdpService.SendTrajectoryDataAsync(ipAddress, port, data);
                    
                    if (!result)
                    {
                        _logger.LogWarning($"直接UDP服务发送轨迹数据失败: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    else
                    {
                        _logger.LogInformation($"直接UDP服务发送轨迹数据成功: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    
                    return result;
                }
                else
                {
                    // 使用连接池发送数据
                    // 创建目标端点
                    var endpoint = new IPEndPoint(IPAddress.Parse(ipAddress), port);
                    
                    // 获取或创建UDP连接
                    var connection = await _connectionPoolManager.GetTrajectoryConnectionAsync(trajectoryId, channelId, sceneId, endpoint);
                    
                    // 发送数据
                    bool result = await _connectionPoolManager.SendDataAsync(connection, data);
                    
                    if (!result)
                    {
                        _logger.LogWarning($"发送轨迹数据失败: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"发送轨迹数据时出错: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                return false;
            }
        }

        /// <summary>
        /// 发送通道数据
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="sceneId">场景ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <param name="port">端口号</param>
        /// <param name="data">数据</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> SendChannelDataAsync(int channelId, int sceneId, string ipAddress, int port, byte[] data)
        {
            try
            {
                _logger.LogInformation($"发送通道数据: 通道ID={channelId}, 场景ID={sceneId}, IP={ipAddress}, 端口={port}, 数据长度={data.Length}");
                
                if (_useDirectUdp)
                {
                    // 使用直接UDP服务发送数据
                    bool result = await _directUdpService.SendTrajectoryDataAsync(ipAddress, port, data);
                    
                    if (!result)
                    {
                        _logger.LogWarning($"直接UDP服务发送通道数据失败: 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    else
                    {
                        _logger.LogInformation($"直接UDP服务发送通道数据成功: 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    
                    return result;
                }
                else
                {
                    // 使用连接池发送数据
                    // 创建目标端点
                    var endpoint = new IPEndPoint(IPAddress.Parse(ipAddress), port);
                    
                    // 获取或创建UDP连接
                    var connection = await _connectionPoolManager.GetChannelConnectionAsync(channelId, sceneId, endpoint);
                    
                    // 发送数据
                    bool result = await _connectionPoolManager.SendDataAsync(connection, data);
                    
                    if (!result)
                    {
                        _logger.LogWarning($"发送通道数据失败: 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"发送通道数据时出错: 通道ID={channelId}, 场景ID={sceneId}");
                return false;
            }
        }

        /// <summary>
        /// 停止轨迹数据发送
        /// </summary>
        /// <param name="trajectoryId">轨迹ID</param>
        /// <returns>操作任务</returns>
        public async Task StopTrajectoryDataSendingAsync(int trajectoryId)
        {
            try
            {
                // 获取连接信息（不增加引用计数）
                var connections = await Task.Run(() => 
                    _connectionPoolManager.GetConnectionPoolStatsAsync().Result.TrajectoryConnectionCount > 0);
                
                if (connections)
                {
                    _logger.LogInformation($"正在停止轨迹数据发送: 轨迹ID={trajectoryId}");
                    await _connectionPoolManager.ReleaseTrajectoryConnectionAsync(trajectoryId);
                    _logger.LogInformation($"已停止轨迹数据发送: 轨迹ID={trajectoryId}");
                }
                else
                {
                    _logger.LogInformation($"没有找到轨迹连接: 轨迹ID={trajectoryId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止轨迹数据发送时出错: 轨迹ID={trajectoryId}");
            }
        }

        /// <summary>
        /// 停止通道数据发送
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <returns>操作任务</returns>
        public async Task StopChannelDataSendingAsync(int channelId)
        {
            try
            {
                _logger.LogInformation($"正在停止通道数据发送: 通道ID={channelId}");
                await _connectionPoolManager.ReleaseChannelConnectionAsync(channelId);
                _logger.LogInformation($"已停止通道数据发送: 通道ID={channelId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止通道数据发送时出错: 通道ID={channelId}");
            }
        }

        /// <summary>
        /// 停止场景数据发送
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <returns>操作任务</returns>
        public async Task StopSceneDataSendingAsync(int sceneId)
        {
            try
            {
                _logger.LogInformation($"正在停止场景数据发送: 场景ID={sceneId}");
                await _connectionPoolManager.ReleaseAllSceneConnectionsAsync(sceneId);
                _logger.LogInformation($"已停止场景数据发送: 场景ID={sceneId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止场景数据发送时出错: 场景ID={sceneId}");
            }
        }

        /// <summary>
        /// 停止通道及其轨迹数据发送
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <returns>操作任务</returns>
        public async Task StopChannelAndTrajectoryDataSendingAsync(int channelId)
        {
            try
            {
                _logger.LogInformation($"正在停止通道及其轨迹数据发送: 通道ID={channelId}");
                await _connectionPoolManager.ReleaseAllChannelConnectionsAsync(channelId);
                _logger.LogInformation($"已停止通道及其轨迹数据发送: 通道ID={channelId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止通道及其轨迹数据发送时出错: 通道ID={channelId}");
            }
        }

        /// <summary>
        /// 清理空闲连接
        /// </summary>
        /// <param name="idleTimeoutMinutes">空闲超时时间（分钟）</param>
        /// <returns>清理的连接数量</returns>
        public async Task<int> CleanupIdleConnectionsAsync(int idleTimeoutMinutes = 10)
        {
            return await _connectionPoolManager.CleanupIdleConnectionsAsync(idleTimeoutMinutes);
        }

        /// <summary>
        /// 获取连接池统计信息
        /// </summary>
        /// <returns>连接池统计</returns>
        public async Task<UdpConnectionPoolStats> GetConnectionPoolStatsAsync()
        {
            return await _connectionPoolManager.GetConnectionPoolStatsAsync();
        }
    }
}