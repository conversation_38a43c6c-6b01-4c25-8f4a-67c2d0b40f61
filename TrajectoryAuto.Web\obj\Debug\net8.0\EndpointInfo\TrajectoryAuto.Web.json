{"openapi": "3.0.1", "info": {"title": "TrajectoryAuto.Web", "version": "1.0"}, "paths": {"/api/Channels/scene/{sceneId}": {"get": {"tags": ["Channels"], "parameters": [{"name": "sceneId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Channel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Channel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Channel"}}}}}}}}, "/api/Channels/{id}": {"get": {"tags": ["Channels"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Channel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Channel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Channel"}}}}}}, "put": {"tags": ["Channels"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Channel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Channel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Channel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Channel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Channel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Channel"}}}}}}, "delete": {"tags": ["Channels"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Channels": {"post": {"tags": ["Channels"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Channel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Channel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Channel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Channel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Channel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Channel"}}}}}}}, "/api/Channels/{channelId}/activate": {"post": {"tags": ["Channels"], "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "sceneId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PlaybackPerformance/stats": {"get": {"tags": ["PlaybackPerformance"], "responses": {"200": {"description": "OK"}}}}, "/api/PlaybackPerformance/cleanup": {"post": {"tags": ["PlaybackPerformance"], "responses": {"200": {"description": "OK"}}}}, "/api/PlaybackPerformance/system-resources": {"get": {"tags": ["PlaybackPerformance"], "responses": {"200": {"description": "OK"}}}}, "/api/PlaybackPerformance/recommendations": {"get": {"tags": ["PlaybackPerformance"], "responses": {"200": {"description": "OK"}}}}, "/api/Scenes": {"get": {"tags": ["Scenes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Scene"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Scene"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Scene"}}}}}}}, "post": {"tags": ["Scenes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scene"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Scene"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Scene"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Scene"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Scene"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Scene"}}}}}}}, "/api/Scenes/{id}": {"get": {"tags": ["Scenes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Scene"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Scene"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Scene"}}}}}}, "put": {"tags": ["Scenes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scene"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Scene"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Scene"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Scene"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Scene"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Scene"}}}}}}, "delete": {"tags": ["Scenes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Scenes/{id}/background": {"post": {"tags": ["Scenes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/SimpleUdp/send": {"post": {"tags": ["SimpleUdp"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendCoordinateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendCoordinateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendCoordinateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/SimpleUdp/stop-scene/{sceneId}": {"post": {"tags": ["SimpleUdp"], "parameters": [{"name": "sceneId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/SimpleUdp/stop-channel/{channelId}": {"post": {"tags": ["SimpleUdp"], "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/SimpleUdp/stop-trajectory/{trajectoryId}": {"post": {"tags": ["SimpleUdp"], "parameters": [{"name": "trajectoryId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/SimpleUdp/stop-all": {"post": {"tags": ["SimpleUdp"], "responses": {"200": {"description": "OK"}}}}, "/api/Trajectories/channel/{channelId}": {"get": {"tags": ["Trajectories"], "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Trajectory"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Trajectory"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Trajectory"}}}}}}}}, "/api/Trajectories/{id}": {"get": {"tags": ["Trajectories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}}}}}, "put": {"tags": ["Trajectories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}}}}}, "delete": {"tags": ["Trajectories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Trajectories": {"post": {"tags": ["Trajectories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Trajectory"}}}}}}}, "/api/Trajectories/channel/{channelId}/clear": {"delete": {"tags": ["Trajectories"], "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Trajectories/{id}/play": {"post": {"tags": ["Trajectories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Trajectories/{id}/stop": {"post": {"tags": ["Trajectories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Trajectories/{id}/status": {"get": {"tags": ["Trajectories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/play": {"post": {"tags": ["TrajectoryPlayback"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlayTrajectoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlayTrajectoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PlayTrajectoryRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/play-channel": {"post": {"tags": ["TrajectoryPlayback"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlayChannelRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlayChannelRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PlayChannelRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/play-scene": {"post": {"tags": ["TrajectoryPlayback"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlaySceneRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PlaySceneRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PlaySceneRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/stop-scene/{sceneId}": {"post": {"tags": ["TrajectoryPlayback"], "parameters": [{"name": "sceneId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/stop-channel/{channelId}": {"post": {"tags": ["TrajectoryPlayback"], "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/stop-all": {"post": {"tags": ["TrajectoryPlayback"], "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/status": {"get": {"tags": ["TrajectoryPlayback"], "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/status/scene/{sceneId}": {"get": {"tags": ["TrajectoryPlayback"], "parameters": [{"name": "sceneId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TrajectoryPlayback/status/channel/{channelId}": {"get": {"tags": ["TrajectoryPlayback"], "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Udp/send": {"post": {"tags": ["Udp"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UdpSendRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UdpSendRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UdpSendRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/UdpConnectionPool/stats": {"get": {"tags": ["UdpConnectionPool"], "responses": {"200": {"description": "OK"}}}}, "/api/UdpConnectionPool/status": {"get": {"tags": ["UdpConnectionPool"], "responses": {"200": {"description": "OK"}}}}, "/api/UdpConnectionPool/cleanup": {"post": {"tags": ["UdpConnectionPool"], "parameters": [{"name": "idleTimeoutMinutes", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/UdpConnectionPool/release-scene/{sceneId}": {"post": {"tags": ["UdpConnectionPool"], "parameters": [{"name": "sceneId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UdpConnectionPool/release-channel/{channelId}": {"post": {"tags": ["UdpConnectionPool"], "parameters": [{"name": "channelId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UdpConnectionPool/release-trajectory/{trajectoryId}": {"post": {"tags": ["UdpConnectionPool"], "parameters": [{"name": "trajectoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UdpConnectionPool/recommendation": {"get": {"tags": ["UdpConnectionPool"], "parameters": [{"name": "sceneId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "channelCount", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "trajectoryCount", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UdpConnectionPool/performance-report": {"get": {"tags": ["UdpConnectionPool"], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"Channel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "useZAxis": {"type": "boolean"}, "zAxisMin": {"type": "number", "format": "double"}, "zAxisMax": {"type": "number", "format": "double"}, "channelNumber": {"type": "integer", "format": "int32"}, "address": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}, "port": {"type": "integer", "format": "int32"}, "color": {"type": "string", "nullable": true}, "sceneId": {"type": "string", "format": "uuid"}, "scene": {"$ref": "#/components/schemas/Scene"}, "trajectories": {"type": "array", "items": {"$ref": "#/components/schemas/Trajectory"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "LineStyle": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "PlayChannelRequest": {"type": "object", "properties": {"channelId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "PlaySceneRequest": {"type": "object", "properties": {"sceneId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "PlayTrajectoryRequest": {"type": "object", "properties": {"trajectoryId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "Scene": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "width": {"type": "integer", "format": "int32"}, "height": {"type": "integer", "format": "int32"}, "serverIp": {"type": "string", "nullable": true}, "serverPort": {"type": "integer", "format": "int32"}, "backgroundImagePath": {"type": "string", "nullable": true}, "originX": {"type": "number", "format": "double"}, "originY": {"type": "number", "format": "double"}, "pixelToMeterRatio": {"type": "number", "format": "double"}, "scaleX": {"type": "number", "format": "double"}, "scaleY": {"type": "number", "format": "double"}, "invertY": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "channels": {"type": "array", "items": {"$ref": "#/components/schemas/Channel"}, "nullable": true}}, "additionalProperties": false}, "SendCoordinateRequest": {"type": "object", "properties": {"sceneId": {"type": "string", "format": "uuid"}, "channelId": {"type": "string", "format": "uuid"}, "trajectoryId": {"type": "string", "format": "uuid", "nullable": true}, "serverIp": {"type": "string", "nullable": true}, "serverPort": {"type": "integer", "format": "int32"}, "channelNumber": {"type": "integer", "format": "int32"}, "channelAddress": {"type": "string", "nullable": true}, "x": {"type": "number", "format": "double"}, "y": {"type": "number", "format": "double"}, "z": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Trajectory": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/TrajectoryType"}, "duration": {"type": "number", "format": "double"}, "isLoop": {"type": "boolean"}, "loopCount": {"type": "integer", "format": "int32"}, "isReverse": {"type": "boolean"}, "useZAxis": {"type": "boolean"}, "zAxisMin": {"type": "number", "format": "double"}, "zAxisMax": {"type": "number", "format": "double"}, "points": {"type": "array", "items": {"$ref": "#/components/schemas/TrajectoryPoint"}, "nullable": true}, "channelId": {"type": "string", "format": "uuid"}, "channel": {"$ref": "#/components/schemas/Channel"}, "createdAt": {"type": "string", "format": "date-time"}, "lineColor": {"type": "string", "nullable": true}, "lineWidth": {"type": "integer", "format": "int32"}, "lineStyle": {"$ref": "#/components/schemas/LineStyle"}, "lineOpacity": {"type": "number", "format": "double"}, "showPoints": {"type": "boolean"}, "pointSize": {"type": "integer", "format": "int32"}, "pointColor": {"type": "string", "nullable": true}, "showEndpoints": {"type": "boolean"}, "startPointColor": {"type": "string", "nullable": true}, "endPointColor": {"type": "string", "nullable": true}, "enableShadow": {"type": "boolean"}, "shadowColor": {"type": "string", "nullable": true}, "shadowBlur": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TrajectoryPoint": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "x": {"type": "number", "format": "double"}, "y": {"type": "number", "format": "double"}, "timestamp": {"type": "number", "format": "double"}, "order": {"type": "integer", "format": "int32"}, "trajectoryId": {"type": "string", "format": "uuid"}, "trajectory": {"$ref": "#/components/schemas/Trajectory"}}, "additionalProperties": false}, "TrajectoryType": {"enum": [1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "UdpSendRequest": {"type": "object", "properties": {"ip": {"type": "string", "nullable": true}, "port": {"type": "integer", "format": "int32"}, "data": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}