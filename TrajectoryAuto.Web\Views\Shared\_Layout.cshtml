<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - 舞台轨迹自动化系统</title>
    <link rel="stylesheet" href="~/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/style.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/trajectory.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/custom.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/modal.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/trajectory-actions.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/playback.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/all.min.css" asp-append-version="true">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1><i class="fas fa-route me-2"></i>舞台轨迹<span class="tech-highlight">自动化系统</span></h1>
            <div class="header-controls">
                <button id="connectBtn" class="btn">
                    <i class="fas fa-plug me-1"></i>连接服务器
                </button>
                <span id="connectionStatus" class="status-indicator">
                    <i class="fas fa-circle me-1"></i>未连接
                </span>
            </div>
        </header>
        
        <main>
            @RenderBody()
        </main>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/lib/signalr/signalr.min.js"></script>
    @* <script src="~/js/modalTest.js" asp-append-version="true"></script> *@
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>