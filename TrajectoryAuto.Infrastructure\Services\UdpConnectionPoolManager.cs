using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TrajectoryAuto.Core.Interfaces;

namespace TrajectoryAuto.Infrastructure.Services
{
    /// <summary>
    /// UDP连接池管理器实现
    /// </summary>
    public class UdpConnectionPoolManager : IUdpConnectionPoolManager
    {
        private readonly ILogger<UdpConnectionPoolManager> _logger;
        private readonly ConcurrentDictionary<string, UdpConnectionInfo> _connections;
        private readonly Timer _cleanupTimer;
        private bool _disposed = false;

        private readonly int _idleTimeoutMinutes;
        private readonly int _maxConnectionsPerType;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public UdpConnectionPoolManager(ILogger<UdpConnectionPoolManager> logger)
            : this(logger, 10, 100)
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="idleTimeoutMinutes">空闲连接超时时间（分钟）</param>
        /// <param name="maxConnectionsPerType">每种类型的最大连接数</param>
        public UdpConnectionPoolManager(ILogger<UdpConnectionPoolManager> logger, int idleTimeoutMinutes, int maxConnectionsPerType)
        {
            _logger = logger;
            _idleTimeoutMinutes = idleTimeoutMinutes;
            _maxConnectionsPerType = maxConnectionsPerType;
            _connections = new ConcurrentDictionary<string, UdpConnectionInfo>();
            
            // 创建定时清理任务，根据配置的空闲超时时间执行
            _cleanupTimer = new Timer(async _ => await CleanupIdleConnectionsAsync(), null, 
                TimeSpan.FromMinutes(idleTimeoutMinutes), TimeSpan.FromMinutes(idleTimeoutMinutes));
            
            _logger.LogInformation($"UDP连接池管理器已初始化，空闲超时时间：{idleTimeoutMinutes}分钟，每种类型最大连接数：{maxConnectionsPerType}");
        }

        /// <summary>
        /// 生成连接ID
        /// </summary>
        private string GenerateConnectionId(int? trajectoryId, int channelId, int sceneId, IPEndPoint endpoint, UdpConnectionType type)
        {
            if (type == UdpConnectionType.Trajectory && trajectoryId.HasValue)
            {
                return $"traj_{trajectoryId}_{channelId}_{sceneId}_{endpoint.Address}_{endpoint.Port}";
            }
            else
            {
                return $"chan_{channelId}_{sceneId}_{endpoint.Address}_{endpoint.Port}";
            }
        }

        /// <summary>
        /// 获取或创建轨迹级别的UDP连接
        /// </summary>
        public async Task<UdpConnectionInfo> GetTrajectoryConnectionAsync(int trajectoryId, int channelId, int sceneId, IPEndPoint targetEndpoint)
        {
            string connectionId = GenerateConnectionId(trajectoryId, channelId, sceneId, targetEndpoint, UdpConnectionType.Trajectory);
            
            return await Task.Run(() =>
            {
                var connectionInfo = _connections.GetOrAdd(connectionId, _ =>
                {
                    _logger.LogInformation($"创建新的轨迹UDP连接: {connectionId}");
                    var udpClient = new UdpClient();
                    
                    var connectionInfo = new UdpConnectionInfo
                    {
                        ConnectionId = connectionId,
                        UdpClient = udpClient,
                        TargetEndpoint = targetEndpoint,
                        ConnectionType = UdpConnectionType.Trajectory,
                        SceneId = sceneId,
                        ChannelId = channelId,
                        TrajectoryId = trajectoryId,
                        CreatedAt = DateTime.Now,
                        LastUsedAt = DateTime.Now,
                        ReferenceCount = 1
                    };
                    
                    return connectionInfo;
                });
                
                // 重要：重置引用计数，确保连接可用
                if (connectionInfo.ReferenceCount <= 0)
                {
                    _logger.LogInformation($"重新激活轨迹UDP连接: {connectionId}");
                    
                    // 重新创建UDP客户端，确保连接有效
                    try
                    {
                        connectionInfo.UdpClient?.Close();
                        connectionInfo.UdpClient?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"关闭旧UDP客户端时出错: {connectionId}");
                    }
                    
                    connectionInfo.UdpClient = new UdpClient();
                    connectionInfo.ReferenceCount = 1;
                    connectionInfo.LastUsedAt = DateTime.Now;
                    _logger.LogInformation($"已为轨迹UDP连接创建新的UDP客户端: {connectionId}");
                }
                
                return connectionInfo;
            });
        }

        /// <summary>
        /// 获取或创建通道级别的UDP连接
        /// </summary>
        public async Task<UdpConnectionInfo> GetChannelConnectionAsync(int channelId, int sceneId, IPEndPoint targetEndpoint)
        {
            string connectionId = GenerateConnectionId(null, channelId, sceneId, targetEndpoint, UdpConnectionType.Channel);
            
            return await Task.Run(() =>
            {
                var connectionInfo = _connections.GetOrAdd(connectionId, _ =>
                {
                    _logger.LogInformation($"创建新的通道UDP连接: {connectionId}");
                    var udpClient = new UdpClient();
                    
                    var connectionInfo = new UdpConnectionInfo
                    {
                        ConnectionId = connectionId,
                        UdpClient = udpClient,
                        TargetEndpoint = targetEndpoint,
                        ConnectionType = UdpConnectionType.Channel,
                        SceneId = sceneId,
                        ChannelId = channelId,
                        TrajectoryId = null,
                        CreatedAt = DateTime.Now,
                        LastUsedAt = DateTime.Now,
                        ReferenceCount = 1
                    };
                    
                    return connectionInfo;
                });
                
                // 重要：重置引用计数，确保连接可用
                if (connectionInfo.ReferenceCount <= 0)
                {
                    _logger.LogInformation($"重新激活通道UDP连接: {connectionId}");
                    
                    // 重新创建UDP客户端，确保连接有效
                    try
                    {
                        connectionInfo.UdpClient?.Close();
                        connectionInfo.UdpClient?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"关闭旧UDP客户端时出错: {connectionId}");
                    }
                    
                    connectionInfo.UdpClient = new UdpClient();
                    connectionInfo.ReferenceCount = 1;
                    connectionInfo.LastUsedAt = DateTime.Now;
                    _logger.LogInformation($"已为通道UDP连接创建新的UDP客户端: {connectionId}");
                }
                
                return connectionInfo;
            });
        }

        /// <summary>
        /// 释放轨迹级别的UDP连接
        /// </summary>
        public async Task ReleaseTrajectoryConnectionAsync(int trajectoryId)
        {
            await Task.Run(() =>
            {
                var connections = _connections.Where(kvp => 
                    kvp.Value.ConnectionType == UdpConnectionType.Trajectory && 
                    kvp.Value.TrajectoryId == trajectoryId).ToList();
                
                foreach (var connection in connections)
                {
                    if (connection.Value.ReferenceCount > 0)
                    {
                        connection.Value.ReferenceCount--;
                    }
                    
                    _logger.LogInformation($"释放轨迹UDP连接: {connection.Key}, 剩余引用: {connection.Value.ReferenceCount}");
                    
                    // 如果引用计数为0，关闭UDP客户端但不从连接池中移除
                    if (connection.Value.ReferenceCount <= 0)
                    {
                        try
                        {
                            // 关闭UDP客户端，确保资源被释放
                            connection.Value.UdpClient?.Close();
                            connection.Value.UdpClient?.Dispose();
                            _logger.LogInformation($"轨迹UDP连接引用计数为0，已关闭UDP客户端: {connection.Key}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"关闭轨迹UDP客户端时出错: {connection.Key}");
                        }
                    }
                }
            });
        }

        /// <summary>
        /// 释放通道级别的UDP连接
        /// </summary>
        public async Task ReleaseChannelConnectionAsync(int channelId)
        {
            await Task.Run(() =>
            {
                var connections = _connections.Where(kvp => 
                    kvp.Value.ConnectionType == UdpConnectionType.Channel && 
                    kvp.Value.ChannelId == channelId).ToList();
                
                foreach (var connection in connections)
                {
                    if (connection.Value.ReferenceCount > 0)
                    {
                        connection.Value.ReferenceCount--;
                    }
                    
                    _logger.LogInformation($"释放通道UDP连接: {connection.Key}, 剩余引用: {connection.Value.ReferenceCount}");
                    
                    // 如果引用计数为0，关闭UDP客户端但不从连接池中移除
                    if (connection.Value.ReferenceCount <= 0)
                    {
                        try
                        {
                            // 关闭UDP客户端，确保资源被释放
                            connection.Value.UdpClient?.Close();
                            connection.Value.UdpClient?.Dispose();
                            _logger.LogInformation($"通道UDP连接引用计数为0，已关闭UDP客户端: {connection.Key}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"关闭通道UDP客户端时出错: {connection.Key}");
                        }
                    }
                }
            });
        }

        /// <summary>
        /// 释放场景下所有连接
        /// </summary>
        public async Task ReleaseAllSceneConnectionsAsync(int sceneId)
        {
            await Task.Run(() =>
            {
                var connections = _connections.Where(kvp => kvp.Value.SceneId == sceneId).ToList();
                
                foreach (var connection in connections)
                {
                    // 设置引用计数为0
                    connection.Value.ReferenceCount = 0;
                    _logger.LogInformation($"释放场景UDP连接: {connection.Key}");
                    
                    // 关闭UDP客户端，确保资源被释放
                    try
                    {
                        connection.Value.UdpClient?.Close();
                        connection.Value.UdpClient?.Dispose();
                        _logger.LogInformation($"场景UDP连接已关闭UDP客户端: {connection.Key}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"关闭场景UDP客户端时出错: {connection.Key}");
                    }
                }
            });
        }

        /// <summary>
        /// 释放通道下所有轨迹连接
        /// </summary>
        public async Task ReleaseAllChannelConnectionsAsync(int channelId)
        {
            await Task.Run(() =>
            {
                var connections = _connections.Where(kvp => 
                    (kvp.Value.ConnectionType == UdpConnectionType.Trajectory || 
                     kvp.Value.ConnectionType == UdpConnectionType.Channel) && 
                    kvp.Value.ChannelId == channelId).ToList();
                
                foreach (var connection in connections)
                {
                    // 设置引用计数为0
                    connection.Value.ReferenceCount = 0;
                    _logger.LogInformation($"释放通道相关UDP连接: {connection.Key}");
                    
                    // 关闭UDP客户端，确保资源被释放
                    try
                    {
                        connection.Value.UdpClient?.Close();
                        connection.Value.UdpClient?.Dispose();
                        _logger.LogInformation($"通道相关UDP连接已关闭UDP客户端: {connection.Key}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"关闭通道相关UDP客户端时出错: {connection.Key}");
                    }
                }
            });
        }

        /// <summary>
        /// 清理空闲连接
        /// </summary>
        public async Task<int> CleanupIdleConnectionsAsync(int idleTimeoutMinutes = 10)
        {
            int cleanedCount = 0;
            
            await Task.Run(() =>
            {
                var now = DateTime.Now;
                var idleThreshold = now.AddMinutes(-idleTimeoutMinutes);
                
                var idleConnections = _connections.Where(kvp => 
                    kvp.Value.ReferenceCount <= 0 && 
                    kvp.Value.LastUsedAt < idleThreshold).ToList();
                
                foreach (var connection in idleConnections)
                {
                    try
                    {
                        connection.Value.UdpClient.Close();
                        connection.Value.UdpClient.Dispose();
                        
                        if (_connections.TryRemove(connection.Key, out _))
                        {
                            cleanedCount++;
                            _logger.LogInformation($"清理空闲UDP连接: {connection.Key}, 空闲时间: {(now - connection.Value.LastUsedAt).TotalMinutes:F1}分钟");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"清理UDP连接时出错: {connection.Key}");
                    }
                }
                
                _logger.LogInformation($"UDP连接池清理完成, 清理了{cleanedCount}个空闲连接, 剩余{_connections.Count}个连接");
            });
            
            return cleanedCount;
        }

        /// <summary>
        /// 获取连接池统计信息
        /// </summary>
        public async Task<UdpConnectionPoolStats> GetConnectionPoolStatsAsync()
        {
            return await Task.Run(() =>
            {
                var now = DateTime.Now;
                var connections = _connections.Values.ToList();
                
                var stats = new UdpConnectionPoolStats
                {
                    ChannelConnectionCount = connections.Count(c => c.ConnectionType == UdpConnectionType.Channel),
                    TrajectoryConnectionCount = connections.Count(c => c.ConnectionType == UdpConnectionType.Trajectory),
                    ActiveConnectionCount = connections.Count(c => c.ReferenceCount > 0),
                    IdleConnectionCount = connections.Count(c => c.ReferenceCount <= 0),
                    TotalSendCount = connections.Sum(c => c.SendStats.TotalSendCount),
                    TotalBytesSent = connections.Sum(c => c.SendStats.TotalBytesSent),
                    AverageConnectionAgeMinutes = connections.Any() 
                        ? connections.Average(c => (now - c.CreatedAt).TotalMinutes) 
                        : 0,
                    PoolEfficiency = connections.Any() 
                        ? (double)connections.Sum(c => c.SendStats.TotalSendCount) / Math.Max(1, connections.Count) 
                        : 0
                };
                
                return stats;
            });
        }

        /// <summary>
        /// 发送数据到指定连接
        /// </summary>
        public async Task<bool> SendDataAsync(UdpConnectionInfo connectionInfo, byte[] data)
        {
            if (connectionInfo == null || data == null || data.Length == 0)
            {
                return false;
            }
            
            // 检查连接是否已停止使用（引用计数为0）
            if (connectionInfo.ReferenceCount <= 0)
            {
                _logger.LogWarning($"尝试向已停止的连接发送数据: {connectionInfo.ConnectionId}");
                return false;
            }
            
            // 检查UDP客户端是否为null或已被释放
            if (connectionInfo.UdpClient == null)
            {
                _logger.LogWarning($"UDP客户端为null，无法发送数据: {connectionInfo.ConnectionId}");
                
                // 尝试重新创建UDP客户端
                try
                {
                    _logger.LogInformation($"尝试重新创建UDP客户端: {connectionInfo.ConnectionId}");
                    connectionInfo.UdpClient = new UdpClient();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"重新创建UDP客户端失败: {connectionInfo.ConnectionId}");
                    return false;
                }
            }
            
            try
            {
                var startTime = DateTime.Now;
                int bytesSent = await connectionInfo.UdpClient.SendAsync(data, data.Length, connectionInfo.TargetEndpoint);
                var endTime = DateTime.Now;
                
                // 更新统计信息
                connectionInfo.LastUsedAt = endTime;
                connectionInfo.SendStats.TotalSendCount++;
                connectionInfo.SendStats.TotalBytesSent += bytesSent;
                connectionInfo.SendStats.LastSendTime = endTime;
                
                // 更新平均延迟（使用指数移动平均）
                double latencyMs = (endTime - startTime).TotalMilliseconds;
                if (connectionInfo.SendStats.AverageLatencyMs == 0)
                {
                    connectionInfo.SendStats.AverageLatencyMs = latencyMs;
                }
                else
                {
                    connectionInfo.SendStats.AverageLatencyMs = 
                        0.9 * connectionInfo.SendStats.AverageLatencyMs + 0.1 * latencyMs;
                }
                
                return bytesSent == data.Length;
            }
            catch (ObjectDisposedException)
            {
                _logger.LogWarning($"UDP客户端已被释放，尝试重新创建: {connectionInfo.ConnectionId}");
                
                // UDP客户端已被释放，尝试重新创建
                try
                {
                    connectionInfo.UdpClient = new UdpClient();
                    
                    // 使用新创建的UDP客户端重试发送
                    int bytesSent = await connectionInfo.UdpClient.SendAsync(data, data.Length, connectionInfo.TargetEndpoint);
                    var endTime = DateTime.Now;
                    
                    // 更新统计信息
                    connectionInfo.LastUsedAt = endTime;
                    connectionInfo.SendStats.TotalSendCount++;
                    connectionInfo.SendStats.TotalBytesSent += bytesSent;
                    connectionInfo.SendStats.LastSendTime = endTime;
                    
                    _logger.LogInformation($"使用重新创建的UDP客户端发送数据成功: {connectionInfo.ConnectionId}");
                    
                    return bytesSent == data.Length;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"重新创建UDP客户端并发送数据失败: {connectionInfo.ConnectionId}");
                    connectionInfo.SendStats.FailedSendCount++;
                    return false;
                }
            }
            catch (SocketException ex)
            {
                _logger.LogError(ex, $"发送UDP数据时发生Socket错误: {connectionInfo.ConnectionId}, 错误码: {ex.SocketErrorCode}");
                connectionInfo.SendStats.FailedSendCount++;
                
                // 对于某些Socket错误，尝试重新创建UDP客户端
                if (ex.SocketErrorCode == SocketError.ConnectionReset || 
                    ex.SocketErrorCode == SocketError.NotConnected ||
                    ex.SocketErrorCode == SocketError.NetworkDown)
                {
                    try
                    {
                        _logger.LogInformation($"尝试重新创建UDP客户端: {connectionInfo.ConnectionId}");
                        connectionInfo.UdpClient?.Close();
                        connectionInfo.UdpClient?.Dispose();
                        connectionInfo.UdpClient = new UdpClient();
                    }
                    catch (Exception innerEx)
                    {
                        _logger.LogError(innerEx, $"重新创建UDP客户端失败: {connectionInfo.ConnectionId}");
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"发送UDP数据失败: {connectionInfo.ConnectionId}");
                connectionInfo.SendStats.FailedSendCount++;
                return false;
            }
        }

        /// <summary>
        /// 批量发送数据
        /// </summary>
        public async Task<int> SendBatchDataAsync(UdpConnectionInfo connectionInfo, IList<byte[]> dataList)
        {
            if (connectionInfo == null || dataList == null || dataList.Count == 0)
            {
                return 0;
            }
            
            // 检查连接是否已停止使用（引用计数为0）
            if (connectionInfo.ReferenceCount <= 0)
            {
                _logger.LogWarning($"尝试向已停止的连接批量发送数据: {connectionInfo.ConnectionId}");
                return 0;
            }
            
            // 检查UDP客户端是否为null
            if (connectionInfo.UdpClient == null)
            {
                _logger.LogWarning($"UDP客户端为null，尝试重新创建: {connectionInfo.ConnectionId}");
                try
                {
                    connectionInfo.UdpClient = new UdpClient();
                    _logger.LogInformation($"为批量发送重新创建UDP客户端成功: {connectionInfo.ConnectionId}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"为批量发送重新创建UDP客户端失败: {connectionInfo.ConnectionId}");
                    return 0;
                }
            }
            
            int successCount = 0;
            
            foreach (var data in dataList)
            {
                if (await SendDataAsync(connectionInfo, data))
                {
                    successCount++;
                }
            }
            
            return successCount;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                _cleanupTimer?.Dispose();
                
                foreach (var connection in _connections.Values)
                {
                    try
                    {
                        connection.UdpClient?.Close();
                        connection.UdpClient?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"关闭UDP连接时出错: {connection.ConnectionId}");
                    }
                }
                
                _connections.Clear();
            }

            _disposed = true;
        }
    }
}