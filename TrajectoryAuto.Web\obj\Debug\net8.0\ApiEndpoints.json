[{"ContainingType": "TrajectoryAuto.Web.Controllers.ChannelsController", "Method": "CreateChannel", "RelativePath": "api/Channels", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "channel", "Type": "TrajectoryAuto.Core.Entities.Channel", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Channel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ChannelsController", "Method": "SetActiveChannel", "RelativePath": "api/Channels/{channelId}/activate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "channelId", "Type": "System.Guid", "IsRequired": true}, {"Name": "sceneId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ChannelsController", "Method": "GetChannel", "RelativePath": "api/Channels/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Channel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ChannelsController", "Method": "UpdateChannel", "RelativePath": "api/Channels/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "channel", "Type": "TrajectoryAuto.Core.Entities.Channel", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Channel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ChannelsController", "Method": "DeleteChannel", "RelativePath": "api/Channels/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ChannelsController", "Method": "GetChannelsByScene", "RelativePath": "api/Channels/scene/{sceneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sceneId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[TrajectoryAuto.Core.Entities.Channel, TrajectoryAuto.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.PlaybackPerformanceController", "Method": "ManualCleanup", "RelativePath": "api/PlaybackPerformance/cleanup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.PlaybackPerformanceController", "Method": "GetPerformanceRecommendations", "RelativePath": "api/PlaybackPerformance/recommendations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.PlaybackPerformanceController", "Method": "GetPerformanceStats", "RelativePath": "api/PlaybackPerformance/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.PlaybackPerformanceController", "Method": "GetSystemResources", "RelativePath": "api/PlaybackPerformance/system-resources", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ScenesController", "Method": "GetAllScenes", "RelativePath": "api/Scenes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[TrajectoryAuto.Core.Entities.Scene, TrajectoryAuto.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ScenesController", "Method": "CreateScene", "RelativePath": "api/Scenes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "scene", "Type": "TrajectoryAuto.Core.Entities.Scene", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Scene", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ScenesController", "Method": "GetScene", "RelativePath": "api/Scenes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Scene", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ScenesController", "Method": "UpdateScene", "RelativePath": "api/Scenes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "scene", "Type": "TrajectoryAuto.Core.Entities.Scene", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Scene", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ScenesController", "Method": "DeleteScene", "RelativePath": "api/Scenes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.ScenesController", "Method": "UploadBackgroundImage", "RelativePath": "api/Scenes/{id}/background", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.SimpleUdpController", "Method": "SendCoordinateData", "RelativePath": "api/SimpleUdp/send", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TrajectoryAuto.Web.Controllers.SendCoordinateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.SimpleUdpController", "Method": "StopAllUdp", "RelativePath": "api/SimpleUdp/stop-all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.SimpleUdpController", "Method": "StopChannelUdp", "RelativePath": "api/SimpleUdp/stop-channel/{channelId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "channelId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.SimpleUdpController", "Method": "StopSceneUdp", "RelativePath": "api/SimpleUdp/stop-scene/{sceneId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "sceneId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.SimpleUdpController", "Method": "StopTrajectoryUdp", "RelativePath": "api/SimpleUdp/stop-trajectory/{trajectoryId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "trajectoryId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "CreateTrajectory", "RelativePath": "api/Trajectories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "trajectory", "Type": "TrajectoryAuto.Core.Entities.Trajectory", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Trajectory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "GetTrajectory", "RelativePath": "api/Trajectories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Trajectory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "UpdateTrajectory", "RelativePath": "api/Trajectories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "trajectory", "Type": "TrajectoryAuto.Core.Entities.Trajectory", "IsRequired": true}], "ReturnTypes": [{"Type": "TrajectoryAuto.Core.Entities.Trajectory", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "DeleteTrajectory", "RelativePath": "api/Trajectories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "PlayTrajectory", "RelativePath": "api/Trajectories/{id}/play", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "GetPlaybackStatus", "RelativePath": "api/Trajectories/{id}/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "StopTrajectory", "RelativePath": "api/Trajectories/{id}/stop", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "GetTrajectoriesByChannel", "RelativePath": "api/Trajectories/channel/{channelId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "channelId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[TrajectoryAuto.Core.Entities.Trajectory, TrajectoryAuto.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoriesController", "Method": "ClearChannelTrajectories", "RelativePath": "api/Trajectories/channel/{channelId}/clear", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "channelId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "PlayTrajectory", "RelativePath": "api/TrajectoryPlayback/play", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TrajectoryAuto.Web.Controllers.PlayTrajectoryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "PlayChannelTrajectories", "RelativePath": "api/TrajectoryPlayback/play-channel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TrajectoryAuto.Web.Controllers.PlayChannelRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "PlaySceneTrajectories", "RelativePath": "api/TrajectoryPlayback/play-scene", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TrajectoryAuto.Web.Controllers.PlaySceneRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "GetPlaybackStatus", "RelativePath": "api/TrajectoryPlayback/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "GetChannelPlaybackStatus", "RelativePath": "api/TrajectoryPlayback/status/channel/{channelId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "channelId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "GetScenePlaybackStatus", "RelativePath": "api/TrajectoryPlayback/status/scene/{sceneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sceneId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "StopAllPlayback", "RelativePath": "api/TrajectoryPlayback/stop-all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "StopChannelPlayback", "RelativePath": "api/TrajectoryPlayback/stop-channel/{channelId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "channelId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.TrajectoryPlaybackController", "Method": "StopScenePlayback", "RelativePath": "api/TrajectoryPlayback/stop-scene/{sceneId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "sceneId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpController", "Method": "SendUdpData", "RelativePath": "api/Udp/send", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TrajectoryAuto.Web.Controllers.UdpSendRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpConnectionPoolController", "Method": "CleanupIdleConnections", "RelativePath": "api/UdpConnectionPool/cleanup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "idleTimeoutMinutes", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpConnectionPoolController", "Method": "GetPerformanceReport", "RelativePath": "api/UdpConnectionPool/performance-report", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpConnectionPoolController", "Method": "GetConnectionUsageRecommendation", "RelativePath": "api/UdpConnectionPool/recommendation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sceneId", "Type": "System.Int32", "IsRequired": false}, {"Name": "channelCount", "Type": "System.Int32", "IsRequired": false}, {"Name": "trajectoryCount", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpConnectionPoolController", "Method": "ReleaseChannelConnections", "RelativePath": "api/UdpConnectionPool/release-channel/{channelId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "channelId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpConnectionPoolController", "Method": "ReleaseSceneConnections", "RelativePath": "api/UdpConnectionPool/release-scene/{sceneId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "sceneId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpConnectionPoolController", "Method": "ReleaseTrajectoryConnection", "RelativePath": "api/UdpConnectionPool/release-trajectory/{trajectoryId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "trajectoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpConnectionPoolController", "Method": "GetConnectionPoolStats", "RelativePath": "api/UdpConnectionPool/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "TrajectoryAuto.Web.Controllers.UdpConnectionPoolController", "Method": "GetConnectionPoolStatus", "RelativePath": "api/UdpConnectionPool/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}]