/* 播放状态样式 */
.playback-status {
    margin-top: 8px;
    padding: 5px 10px;
    border-radius: 4px;
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    font-size: 0.85rem;
    text-align: center;
    transition: all 0.3s ease;
    display: inline-block;
    border-left: 3px solid #28a745;
}

.playback-status.error {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border-left: 3px solid #dc3545;
}

/* 播放按钮状态 */
#playSceneBtn:disabled {
    background: linear-gradient(135deg, #1e7e34 0%, #19b377 100%);
    opacity: 0.8;
    cursor: not-allowed;
}

#stopSceneBtn:disabled {
    background: linear-gradient(135deg, #bd2130 0%, #c71666 100%);
    opacity: 0.6;
    cursor: not-allowed;
}

/* 播放状态动画 */
@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

#playSceneBtn:disabled i {
    animation: pulse 1.5s infinite;
}