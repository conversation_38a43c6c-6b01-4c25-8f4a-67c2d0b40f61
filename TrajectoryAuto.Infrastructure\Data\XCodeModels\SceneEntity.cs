using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using NewLife.Data;
using XCode;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace TrajectoryAuto.Infrastructure.Data.XCodeModels;

/// <summary>场景实体 - 使用XCode ORM优化性能</summary>
[BindTable("Scenes", Description = "场景表", ConnName = "TrajectoryAuto", DbType = DatabaseType.SQLite)]
public partial class SceneEntity : Entity<SceneEntity>
{
    #region 属性
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 50)]
    [BindColumn("Id", "编号", "")]
    public String Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }
    private String _Id = "";

    /// <summary>场景名称</summary>
    [DisplayName("场景名称")]
    [Description("场景名称")]
    [DataObjectField(false, false, false, 100)]
    [BindColumn("Name", "场景名称", "", Master = true)]
    public String Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }
    private String _Name = "";

    /// <summary>场景宽度</summary>
    [DisplayName("场景宽度")]
    [Description("场景宽度")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Width", "场景宽度", "")]
    public Int32 Width { get => _Width; set { if (OnPropertyChanging("Width", value)) { _Width = value; OnPropertyChanged("Width"); } } }
    private Int32 _Width;

    /// <summary>场景高度</summary>
    [DisplayName("场景高度")]
    [Description("场景高度")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Height", "场景高度", "")]
    public Int32 Height { get => _Height; set { if (OnPropertyChanging("Height", value)) { _Height = value; OnPropertyChanged("Height"); } } }
    private Int32 _Height;

    /// <summary>服务器IP</summary>
    [DisplayName("服务器IP")]
    [Description("服务器IP")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("ServerIp", "服务器IP", "")]
    public String? ServerIp { get => _ServerIp; set { if (OnPropertyChanging("ServerIp", value)) { _ServerIp = value; OnPropertyChanged("ServerIp"); } } }
    private String? _ServerIp;

    /// <summary>服务器端口</summary>
    [DisplayName("服务器端口")]
    [Description("服务器端口")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ServerPort", "服务器端口", "")]
    public Int32 ServerPort { get => _ServerPort; set { if (OnPropertyChanging("ServerPort", value)) { _ServerPort = value; OnPropertyChanged("ServerPort"); } } }
    private Int32 _ServerPort;

    /// <summary>背景图片路径</summary>
    [DisplayName("背景图片路径")]
    [Description("背景图片路径")]
    [DataObjectField(false, false, true, 500)]
    [BindColumn("BackgroundImagePath", "背景图片路径", "")]
    public String? BackgroundImagePath { get => _BackgroundImagePath; set { if (OnPropertyChanging("BackgroundImagePath", value)) { _BackgroundImagePath = value; OnPropertyChanged("BackgroundImagePath"); } } }
    private String? _BackgroundImagePath;

    /// <summary>坐标原点X</summary>
    [DisplayName("坐标原点X")]
    [Description("坐标原点X")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("OriginX", "坐标原点X", "")]
    public Double OriginX { get => _OriginX; set { if (OnPropertyChanging("OriginX", value)) { _OriginX = value; OnPropertyChanged("OriginX"); } } }
    private Double _OriginX;

    /// <summary>坐标原点Y</summary>
    [DisplayName("坐标原点Y")]
    [Description("坐标原点Y")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("OriginY", "坐标原点Y", "")]
    public Double OriginY { get => _OriginY; set { if (OnPropertyChanging("OriginY", value)) { _OriginY = value; OnPropertyChanged("OriginY"); } } }
    private Double _OriginY;

    /// <summary>像素到米转换比例</summary>
    [DisplayName("像素到米转换比例")]
    [Description("像素到米转换比例")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("PixelToMeterRatio", "像素到米转换比例", "")]
    public Double PixelToMeterRatio { get => _PixelToMeterRatio; set { if (OnPropertyChanging("PixelToMeterRatio", value)) { _PixelToMeterRatio = value; OnPropertyChanged("PixelToMeterRatio"); } } }
    private Double _PixelToMeterRatio;

    /// <summary>X轴缩放比例</summary>
    [DisplayName("X轴缩放比例")]
    [Description("X轴缩放比例")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ScaleX", "X轴缩放比例", "")]
    public Double ScaleX { get => _ScaleX; set { if (OnPropertyChanging("ScaleX", value)) { _ScaleX = value; OnPropertyChanged("ScaleX"); } } }
    private Double _ScaleX = 1.0;

    /// <summary>Y轴缩放比例</summary>
    [DisplayName("Y轴缩放比例")]
    [Description("Y轴缩放比例")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("ScaleY", "Y轴缩放比例", "")]
    public Double ScaleY { get => _ScaleY; set { if (OnPropertyChanging("ScaleY", value)) { _ScaleY = value; OnPropertyChanged("ScaleY"); } } }
    private Double _ScaleY = 1.0;

    /// <summary>Y轴是否反转</summary>
    [DisplayName("Y轴是否反转")]
    [Description("Y轴是否反转")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("InvertY", "Y轴是否反转", "")]
    public Boolean InvertY { get => _InvertY; set { if (OnPropertyChanging("InvertY", value)) { _InvertY = value; OnPropertyChanged("InvertY"); } } }
    private Boolean _InvertY;

    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }
    private DateTime _CreateTime;

    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }
    private DateTime _UpdateTime;
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get
        {
            switch (name)
            {
                case "Id": return _Id;
                case "Name": return _Name;
                case "Width": return _Width;
                case "Height": return _Height;
                case "ServerIp": return _ServerIp;
                case "ServerPort": return _ServerPort;
                case "BackgroundImagePath": return _BackgroundImagePath;
                case "OriginX": return _OriginX;
                case "OriginY": return _OriginY;
                case "PixelToMeterRatio": return _PixelToMeterRatio;
                case "ScaleX": return _ScaleX;
                case "ScaleY": return _ScaleY;
                case "InvertY": return _InvertY;
                case "CreateTime": return _CreateTime;
                case "UpdateTime": return _UpdateTime;
                default: return base[name];
            }
        }
        set
        {
            switch (name)
            {
                case "Id": _Id = Convert.ToString(value) ?? ""; break;
                case "Name": _Name = Convert.ToString(value) ?? ""; break;
                case "Width": _Width = Convert.ToInt32(value); break;
                case "Height": _Height = Convert.ToInt32(value); break;
                case "ServerIp": _ServerIp = Convert.ToString(value); break;
                case "ServerPort": _ServerPort = Convert.ToInt32(value); break;
                case "BackgroundImagePath": _BackgroundImagePath = Convert.ToString(value); break;
                case "OriginX": _OriginX = Convert.ToDouble(value); break;
                case "OriginY": _OriginY = Convert.ToDouble(value); break;
                case "PixelToMeterRatio": _PixelToMeterRatio = Convert.ToDouble(value); break;
                case "ScaleX": _ScaleX = Convert.ToDouble(value); break;
                case "ScaleY": _ScaleY = Convert.ToDouble(value); break;
                case "InvertY": _InvertY = Convert.ToBoolean(value); break;
                case "CreateTime": _CreateTime = Convert.ToDateTime(value); break;
                case "UpdateTime": _UpdateTime = Convert.ToDateTime(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 字段名
    /// <summary>取得场景实体字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>场景名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>场景宽度</summary>
        public static readonly Field Width = FindByName("Width");

        /// <summary>场景高度</summary>
        public static readonly Field Height = FindByName("Height");

        /// <summary>服务器IP</summary>
        public static readonly Field ServerIp = FindByName("ServerIp");

        /// <summary>服务器端口</summary>
        public static readonly Field ServerPort = FindByName("ServerPort");

        /// <summary>背景图片路径</summary>
        public static readonly Field BackgroundImagePath = FindByName("BackgroundImagePath");

        /// <summary>坐标原点X</summary>
        public static readonly Field OriginX = FindByName("OriginX");

        /// <summary>坐标原点Y</summary>
        public static readonly Field OriginY = FindByName("OriginY");

        /// <summary>像素到米转换比例</summary>
        public static readonly Field PixelToMeterRatio = FindByName("PixelToMeterRatio");

        /// <summary>X轴缩放比例</summary>
        public static readonly Field ScaleX = FindByName("ScaleX");

        /// <summary>Y轴缩放比例</summary>
        public static readonly Field ScaleY = FindByName("ScaleY");

        /// <summary>Y轴是否反转</summary>
        public static readonly Field InvertY = FindByName("InvertY");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }
    #endregion

    #region 对象操作
    static SceneEntity()
    {
        // 时间模块，自动处理创建时间和更新时间
        Meta.Modules.Add<TimeModule>();
    }

    /// <summary>验证并修补数据，通过抛出异常的方式提示验证失败。</summary>
    /// <param name="isNew">是否插入</param>
    public override void Valid(Boolean isNew)
    {
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return;

        // 建议先调用基类方法，基类方法会做一些统一处理
        base.Valid(isNew);

        // 在这里添加字段验证，如果验证失败，直接抛出异常
        if (String.IsNullOrEmpty(Name)) throw new ArgumentNullException(nameof(Name), "场景名称不能为空！");
        if (Width <= 0) throw new ArgumentException("场景宽度必须大于0！");
        if (Height <= 0) throw new ArgumentException("场景高度必须大于0！");
    }
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static SceneEntity? FindById(String id)
    {
        if (String.IsNullOrEmpty(id)) return null;
        return Find(_.Id == id);
    }

    /// <summary>根据名称查找</summary>
    /// <param name="name">名称</param>
    /// <returns>实体列表</returns>
    public static IList<SceneEntity> FindAllByName(String name)
    {
        return FindAll(_.Name == name);
    }
    #endregion

    #region 业务操作
    /// <summary>转换为业务实体</summary>
    public TrajectoryAuto.Core.Entities.Scene ToBusinessEntity()
    {
        return new TrajectoryAuto.Core.Entities.Scene
        {
            Id = Guid.Parse(Id),
            Name = Name,
            Width = Width,
            Height = Height,
            ServerIp = ServerIp ?? "",
            ServerPort = ServerPort,
            BackgroundImagePath = BackgroundImagePath,
            OriginX = OriginX,
            OriginY = OriginY,
            PixelToMeterRatio = PixelToMeterRatio,
            ScaleX = ScaleX,
            ScaleY = ScaleY,
            InvertY = InvertY,
            CreatedAt = CreateTime,
            UpdatedAt = UpdateTime
        };
    }

    /// <summary>从业务实体更新</summary>
    public void FromBusinessEntity(TrajectoryAuto.Core.Entities.Scene scene)
    {
        Id = scene.Id.ToString();
        Name = scene.Name;
        Width = scene.Width;
        Height = scene.Height;
        ServerIp = scene.ServerIp;
        ServerPort = scene.ServerPort;
        BackgroundImagePath = scene.BackgroundImagePath;
        OriginX = scene.OriginX;
        OriginY = scene.OriginY;
        PixelToMeterRatio = scene.PixelToMeterRatio;
        ScaleX = scene.ScaleX;
        ScaleY = scene.ScaleY;
        InvertY = scene.InvertY;
    }
    #endregion
}