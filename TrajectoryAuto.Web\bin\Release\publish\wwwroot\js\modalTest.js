/**
 * 模态框测试脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('模态框测试脚本已加载');
    
    // 添加测试按钮
    const testButton = document.createElement('button');
    testButton.id = 'testModalBtn';
    testButton.className = 'btn btn-info';
    testButton.innerHTML = '<i class="fas fa-vial me-1"></i>测试模态框';
    testButton.style.position = 'fixed';
    testButton.style.bottom = '20px';
    testButton.style.right = '20px';
    testButton.style.zIndex = '9000';
    
    document.body.appendChild(testButton);
    
    // 添加点击事件
    testButton.addEventListener('click', function() {
        console.log('测试按钮被点击');
        
        // 获取模态框元素
        const modal = document.getElementById('trajectoryModal');
        
        if (!modal) {
            console.error('找不到trajectoryModal元素');
            alert('找不到trajectoryModal元素');
            return;
        }
        
        console.log('模态框当前display:', getComputedStyle(modal).display);
        
        // 尝试不同的显示方式
        try {
            // 方法1：直接设置display
            modal.style.display = 'flex';
            console.log('已设置模态框display为flex');
            
            // 方法2：添加show类
            modal.classList.add('show');
            console.log('已添加show类');
            
            // 方法3：使用jQuery（如果可用）
            if (window.jQuery) {
                console.log('使用jQuery显示模态框');
                jQuery(modal).show();
            }
            
            // 检查模态框是否可见
            setTimeout(() => {
                console.log('模态框当前display:', getComputedStyle(modal).display);
                console.log('模态框当前visibility:', getComputedStyle(modal).visibility);
                console.log('模态框当前opacity:', getComputedStyle(modal).opacity);
            }, 100);
        } catch (error) {
            console.error('显示模态框时出错:', error);
            alert('显示模态框时出错: ' + error.message);
        }
    });
    
    // 添加关闭模态框的测试按钮
    const closeTestButton = document.createElement('button');
    closeTestButton.id = 'closeTestModalBtn';
    closeTestButton.className = 'btn btn-warning';
    closeTestButton.innerHTML = '<i class="fas fa-times me-1"></i>关闭模态框';
    closeTestButton.style.position = 'fixed';
    closeTestButton.style.bottom = '20px';
    closeTestButton.style.right = '150px';
    closeTestButton.style.zIndex = '9000';
    
    document.body.appendChild(closeTestButton);
    
    // 添加点击事件
    closeTestButton.addEventListener('click', function() {
        console.log('关闭按钮被点击');
        
        // 获取模态框元素
        const modal = document.getElementById('trajectoryModal');
        
        if (!modal) {
            console.error('找不到trajectoryModal元素');
            alert('找不到trajectoryModal元素');
            return;
        }
        
        // 尝试不同的关闭方式
        try {
            // 方法1：直接设置display
            modal.style.display = 'none';
            console.log('已设置模态框display为none');
            
            // 方法2：移除show类
            modal.classList.remove('show');
            console.log('已移除show类');
            
            // 方法3：使用jQuery（如果可用）
            if (window.jQuery) {
                console.log('使用jQuery隐藏模态框');
                jQuery(modal).hide();
            }
        } catch (error) {
            console.error('关闭模态框时出错:', error);
            alert('关闭模态框时出错: ' + error.message);
        }
    });
});