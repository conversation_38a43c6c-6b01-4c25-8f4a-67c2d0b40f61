using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Core.Interfaces
{
    /// <summary>
    /// 增强通信服务接口
    /// 提供基于UDP连接池的通信功能
    /// </summary>
    public interface IEnhancedCommunicationService
    {
        /// <summary>
        /// 使用场景级别连接发送坐标数据
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <param name="serverIp">服务器IP地址</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="coordinateData">坐标数据</param>
        /// <returns>是否发送成功</returns>
        Task<bool> SendCoordinateDataWithSceneConnectionAsync(int sceneId, string serverIp, int serverPort, CoordinateData coordinateData);

        /// <summary>
        /// 使用通道级别连接发送坐标数据
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <param name="sceneId">场景ID</param>
        /// <param name="serverIp">服务器IP地址</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="coordinateData">坐标数据</param>
        /// <returns>是否发送成功</returns>
        Task<bool> SendCoordinateDataWithChannelConnectionAsync(int channelId, int sceneId, string serverIp, int serverPort, CoordinateData coordinateData);

        /// <summary>
        /// 使用轨迹级别连接发送坐标数据
        /// </summary>
        /// <param name="trajectoryId">轨迹ID</param>
        /// <param name="channelId">通道ID</param>
        /// <param name="sceneId">场景ID</param>
        /// <param name="serverIp">服务器IP地址</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="coordinateData">坐标数据</param>
        /// <returns>是否发送成功</returns>
        Task<bool> SendCoordinateDataWithTrajectoryConnectionAsync(int trajectoryId, int channelId, int sceneId, string serverIp, int serverPort, CoordinateData coordinateData);

        /// <summary>
        /// 批量发送坐标数据
        /// </summary>
        /// <param name="connectionType">连接类型</param>
        /// <param name="trajectoryId">轨迹ID（如果使用轨迹级别连接）</param>
        /// <param name="channelId">通道ID</param>
        /// <param name="sceneId">场景ID</param>
        /// <param name="serverIp">服务器IP地址</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="coordinateDataList">坐标数据列表</param>
        /// <returns>成功发送的数据包数量</returns>
        Task<int> SendBatchCoordinateDataAsync(UdpConnectionType connectionType, int? trajectoryId, int channelId, int sceneId, string serverIp, int serverPort, List<CoordinateData> coordinateDataList);

        /// <summary>
        /// 停止轨迹级别连接
        /// </summary>
        /// <param name="trajectoryId">轨迹ID</param>
        /// <returns>操作任务</returns>
        Task StopTrajectoryConnectionAsync(int trajectoryId);

        /// <summary>
        /// 停止通道级别连接
        /// </summary>
        /// <param name="channelId">通道ID</param>
        /// <returns>操作任务</returns>
        Task StopChannelConnectionAsync(int channelId);

        /// <summary>
        /// 停止场景级别连接
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <returns>操作任务</returns>
        Task StopSceneConnectionAsync(int sceneId);

        /// <summary>
        /// 启动实时通信
        /// </summary>
        /// <param name="serverIp">服务器IP地址</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="sceneId">场景ID（可选）</param>
        /// <param name="channelId">通道ID（可选）</param>
        /// <returns>是否成功</returns>
        Task<bool> StartRealTimeCommunicationAsync(string serverIp, int serverPort, int sceneId = 0, int channelId = 0);

        /// <summary>
        /// 停止实时通信
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> StopRealTimeCommunicationAsync();

        /// <summary>
        /// 获取连接使用建议
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <param name="channelCount">通道数量</param>
        /// <param name="trajectoryCount">轨迹数量</param>
        /// <returns>连接使用建议</returns>
        Task<ConnectionUsageRecommendation> GetConnectionUsageRecommendationAsync(int sceneId, int channelCount, int trajectoryCount);

        /// <summary>
        /// 清理空闲连接
        /// </summary>
        /// <param name="idleTimeoutMinutes">空闲超时时间（分钟）</param>
        /// <returns>清理的连接数量</returns>
        Task<int> CleanupIdleConnectionsAsync(int idleTimeoutMinutes = 10);

        /// <summary>
        /// 获取连接池统计信息
        /// </summary>
        /// <returns>连接池统计</returns>
        Task<UdpConnectionPoolStats> GetConnectionPoolStatsAsync();
    }

    /// <summary>
    /// 连接使用建议
    /// </summary>
    public class ConnectionUsageRecommendation
    {
        /// <summary>
        /// 推荐的连接类型
        /// </summary>
        public UdpConnectionType RecommendedConnectionType { get; set; }

        /// <summary>
        /// 建议理由
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 预计连接数量
        /// </summary>
        public int EstimatedConnectionCount { get; set; }

        /// <summary>
        /// 是否需要批量发送
        /// </summary>
        public bool ShouldUseBatchSending { get; set; }

        /// <summary>
        /// 建议的批量大小
        /// </summary>
        public int RecommendedBatchSize { get; set; } = 10;
    }
}