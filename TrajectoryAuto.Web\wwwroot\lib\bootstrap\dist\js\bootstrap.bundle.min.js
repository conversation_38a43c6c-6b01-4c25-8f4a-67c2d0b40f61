/*! Bootstrap v5.3.0 - 简化版本用于轨迹自动化系统 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.bootstrap = {}));
})(this, (function (exports) { 'use strict';

  // 简化的Bootstrap功能
  const Bootstrap = {
    // Modal 功能
    Modal: class {
      constructor(element, options = {}) {
        this.element = element;
        this.options = options;
      }
      
      show() {
        this.element.style.display = 'block';
        this.element.classList.add('show');
        document.body.classList.add('modal-open');
      }
      
      hide() {
        this.element.style.display = 'none';
        this.element.classList.remove('show');
        document.body.classList.remove('modal-open');
      }
      
      static getInstance(element) {
        return element._bsModal || new Bootstrap.Modal(element);
      }
    },
    
    // Dropdown 功能
    Dropdown: class {
      constructor(element, options = {}) {
        this.element = element;
        this.options = options;
        this.menu = element.nextElementSibling;
      }
      
      toggle() {
        if (this.menu.classList.contains('show')) {
          this.hide();
        } else {
          this.show();
        }
      }
      
      show() {
        this.menu.classList.add('show');
      }
      
      hide() {
        this.menu.classList.remove('show');
      }
      
      static getInstance(element) {
        return element._bsDropdown || new Bootstrap.Dropdown(element);
      }
    },
    
    // Alert 功能
    Alert: class {
      constructor(element) {
        this.element = element;
      }
      
      close() {
        this.element.remove();
      }
      
      static getInstance(element) {
        return element._bsAlert || new Bootstrap.Alert(element);
      }
    }
  };

  // 自动初始化
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化下拉菜单
    document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(function(element) {
      element.addEventListener('click', function(e) {
        e.preventDefault();
        const dropdown = Bootstrap.Dropdown.getInstance(element);
        dropdown.toggle();
      });
    });
    
    // 初始化模态框
    document.querySelectorAll('[data-bs-toggle="modal"]').forEach(function(element) {
      element.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(element.getAttribute('data-bs-target'));
        if (target) {
          const modal = Bootstrap.Modal.getInstance(target);
          modal.show();
        }
      });
    });
    
    // 模态框关闭按钮
    document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(function(element) {
      element.addEventListener('click', function() {
        const modal = element.closest('.modal');
        if (modal) {
          Bootstrap.Modal.getInstance(modal).hide();
        }
      });
    });
    
    // Alert 关闭按钮
    document.querySelectorAll('[data-bs-dismiss="alert"]').forEach(function(element) {
      element.addEventListener('click', function() {
        const alert = element.closest('.alert');
        if (alert) {
          Bootstrap.Alert.getInstance(alert).close();
        }
      });
    });
  });

  // 导出到全局
  if (typeof window !== 'undefined') {
    window.bootstrap = Bootstrap;
  }

  exports.Alert = Bootstrap.Alert;
  exports.Dropdown = Bootstrap.Dropdown;
  exports.Modal = Bootstrap.Modal;

}));