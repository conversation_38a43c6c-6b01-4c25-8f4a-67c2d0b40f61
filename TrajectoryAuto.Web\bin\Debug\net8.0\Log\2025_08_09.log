﻿#Software: TrajectoryAuto.Web
#ProcessID: 7480 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:18:53.0150000
#Date: 2025-08-09
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:13:48.750  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:13:48.760  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:13:48.762  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:13:48.762  1 N - TrajectoryAuto.Web 
17:13:48.762  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:13:48.763  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:13:48.763  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:13:52.232 29 Y TP [TrajectoryAuto]待检查数据表：Scenes
17:13:52.302 29 Y TP [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:13:52.407 29 Y TP [TrajectoryAuto] select * from sqlite_master
17:13:52.416 29 Y TP Data Source=D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:13:52.416 29 Y TP [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:13:52.490 29 Y TP [TrajectoryAuto] Select * From Scenes
17:13:59.569 29 Y TP [TrajectoryAuto]待检查数据表：Channels
17:13:59.570 29 Y TP [TrajectoryAuto] select * from sqlite_master
17:13:59.582 29 Y TP [TrajectoryAuto] Select * From Channels Where SceneId='becf6abe-c309-4e47-a056-755e7c19eab5'
17:13:59.652 22 Y TP [TrajectoryAuto] Update Channels Set IsActive=0 Where SceneId='becf6abe-c309-4e47-a056-755e7c19eab5'
17:13:59.659 22 Y TP [TrajectoryAuto] Update Channels Set IsActive=1 Where Id='1ee82b26-6f4b-46cc-a5ae-998250ad2776' And SceneId='becf6abe-c309-4e47-a056-755e7c19eab5'
17:13:59.690 29 Y TP [TrajectoryAuto]待检查数据表：Trajectories
17:13:59.695 29 Y TP [TrajectoryAuto] select * from sqlite_master
17:13:59.708 29 Y TP [TrajectoryAuto] Select * From Trajectories Where ChannelId='1ee82b26-6f4b-46cc-a5ae-998250ad2776'
17:13:59.726 29 Y TP [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:13:59.730 29 Y TP [TrajectoryAuto] select * from sqlite_master
17:13:59.739 29 Y TP [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='1bd3ad29-f3bc-4ad7-9063-9b2a75625567'
17:13:59.882 22 Y TP [TrajectoryAuto] Select * From Trajectories Where ChannelId='1ee82b26-6f4b-46cc-a5ae-998250ad2776'
17:13:59.895 22 Y TP [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='1bd3ad29-f3bc-4ad7-9063-9b2a75625567'
17:14:01.604 22 Y TP [TrajectoryAuto] Select * From Channels Where Id='1ee82b26-6f4b-46cc-a5ae-998250ad2776'
17:14:01.622 22 Y TP [TrajectoryAuto] Select * From Trajectories Where ChannelId='1ee82b26-6f4b-46cc-a5ae-998250ad2776'
17:14:01.639 22 Y TP [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='1bd3ad29-f3bc-4ad7-9063-9b2a75625567'
17:14:01.812 22 Y TP [TrajectoryAuto] Select * From Scenes Where Id='becf6abe-c309-4e47-a056-755e7c19eab5'
17:14:05.282 22 Y TP [TrajectoryAuto] Select * From Trajectories Where ChannelId='7e7800e7-cf8c-470f-aacc-aba9118cae3e'
17:14:05.287 29 Y TP [TrajectoryAuto] Select * From Channels Where Id='b56f6783-91d9-4011-809b-1893397e9352'
17:14:05.347 29 Y TP [TrajectoryAuto] Select * From Scenes Where Id='becf6abe-c309-4e47-a056-755e7c19eab5'
17:14:05.351 22 Y TP [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='c7c46fb9-9f3b-40c6-9202-9fc5b996c127'
17:14:05.452 22 Y TP [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='0baf688c-0790-43c0-9cb4-ad40b4f8ad5d'
17:14:05.637 22 Y TP [TrajectoryAuto] Select * From Channels Where Id='7e7800e7-cf8c-470f-aacc-aba9118cae3e'
17:14:05.657 22 Y TP [TrajectoryAuto] Select * From Scenes Where Id='becf6abe-c309-4e47-a056-755e7c19eab5'

#Software: TrajectoryAuto.Web
#ProcessID: 23524 x64
#AppDomain: TrajectoryAuto.Web
#FileName: D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.exe
#BaseDirectory: D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\
#CurrentDirectory: D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\TrajectoryAuto.Web.dll
#ApplicationType: Web
#CLR: 8.0.13, .NET 8.0.13
#OS: Microsoft Windows NT 10.0.19045.0, DESKTOP-S0UD4US/admin
#CPU: 8
#GC: IsServerGC=True, LatencyMode=Interactive
#ThreadPool: Min=32/32, Max=32767/1000, Available=32765/1000
#SystemStarted: 01:35:36.8430000
#Date: 2025-08-09
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:30:32.565  1 N - NewLife.Core v10.10.2024.0501 Build 2024-05-01 .NET 8.0
17:30:32.580  1 N - NewLife组件核心库 ©2002-2024 NewLife
17:30:32.581  1 N - TrajectoryAuto.Web v1.0.0 Build 2000-01-01 .NET 8.0
17:30:32.582  1 N - TrajectoryAuto.Web 
17:30:32.582  1 N - XCode v11.11.2024.0303 Build 2024-03-03 .NET Standard 2.1
17:30:32.582  1 N - NewLife数据中间件 ©2002-2024 NewLife
17:30:32.582  1 N - 当前配置为输出SQL日志，如果觉得日志过多，可以修改配置关闭[Config/XCode.config:ShowSQL=false]。
17:30:35.256  9 Y TP [TrajectoryAuto]待检查数据表：Scenes
17:30:35.307  9 Y TP [System.Data.SQLite.SQLite]驱动 D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\System.Data.SQLite.dll 版本v1.0.118.0
17:30:35.408  9 Y TP [TrajectoryAuto] select * from sqlite_master
17:30:35.417  9 Y TP Data Source=D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal
17:30:35.418  9 Y TP [TrajectoryAuto] DataSource=D:\Project\TrajectoryAuto\code_claude\TrajectoryAuto.Web\bin\Debug\net8.0\trajectory.db Database= User=
17:30:35.492  9 Y TP [TrajectoryAuto] Select * From Scenes
17:30:37.137 26 Y TP [TrajectoryAuto]待检查数据表：Channels
17:30:37.138 26 Y TP [TrajectoryAuto] select * from sqlite_master
17:30:37.148 26 Y TP [TrajectoryAuto] Select * From Channels Where SceneId='becf6abe-c309-4e47-a056-755e7c19eab5'
17:30:37.211 26 Y TP [TrajectoryAuto] Update Channels Set IsActive=0 Where SceneId='becf6abe-c309-4e47-a056-755e7c19eab5'
17:30:37.218 26 Y TP [TrajectoryAuto] Update Channels Set IsActive=1 Where Id='1ee82b26-6f4b-46cc-a5ae-998250ad2776' And SceneId='becf6abe-c309-4e47-a056-755e7c19eab5'
17:30:37.265  9 Y TP [TrajectoryAuto]待检查数据表：Trajectories
17:30:37.266  9 Y TP [TrajectoryAuto] select * from sqlite_master
17:30:37.281  9 Y TP [TrajectoryAuto] Select * From Trajectories Where ChannelId='1ee82b26-6f4b-46cc-a5ae-998250ad2776'
17:30:37.304  9 Y TP [TrajectoryAuto]待检查数据表：TrajectoryPoints
17:30:37.306  9 Y TP [TrajectoryAuto] select * from sqlite_master
17:30:37.317  9 Y TP [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='1bd3ad29-f3bc-4ad7-9063-9b2a75625567'
17:30:37.394  9 Y TP [TrajectoryAuto] Select * From Trajectories Where ChannelId='1ee82b26-6f4b-46cc-a5ae-998250ad2776'
17:30:37.406  9 Y TP [TrajectoryAuto] Select * From TrajectoryPoints Where TrajectoryId='1bd3ad29-f3bc-4ad7-9063-9b2a75625567'
