using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Net.Sockets;
using System.Text;

namespace TrajectoryAuto.Web.Controllers;

/// <summary>
/// UDP通信控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class UdpController : ControllerBase
{
    private readonly ILogger<UdpController> _logger;

    public UdpController(ILogger<UdpController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 发送UDP数据
    /// </summary>
    /// <param name="request">UDP发送请求</param>
    /// <returns>发送结果</returns>
    [HttpPost("send")]
    public async Task<IActionResult> SendUdpData([FromBody] UdpSendRequest request)
    {
        if (request == null || string.IsNullOrEmpty(request.Ip) || request.Port <= 0 || string.IsNullOrEmpty(request.Data))
        {
            return BadRequest("无效的请求参数");
        }

        try
        {
            _logger.LogInformation($"发送UDP数据: {request.Data} 到 {request.Ip}:{request.Port}");
            
            // 创建UDP客户端
            using (var udpClient = new UdpClient())
            {
                // 将字符串转换为字节数组
                byte[] datagram = Encoding.UTF8.GetBytes(request.Data);
                
                // 发送数据
                await udpClient.SendAsync(
                    datagram, 
                    datagram.Length, 
                    new IPEndPoint(IPAddress.Parse(request.Ip), request.Port)
                );
            }

            return Ok(new { success = true, message = "UDP数据发送成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError($"发送UDP数据失败: {ex.Message}");
            return StatusCode(500, new { success = false, message = $"发送UDP数据失败: {ex.Message}" });
        }
    }
}

/// <summary>
/// UDP发送请求模型
/// </summary>
public class UdpSendRequest
{
    /// <summary>
    /// 目标IP地址
    /// </summary>
    public string Ip { get; set; }
    
    /// <summary>
    /// 目标端口
    /// </summary>
    public int Port { get; set; }
    
    /// <summary>
    /// 要发送的数据
    /// </summary>
    public string Data { get; set; }
}
