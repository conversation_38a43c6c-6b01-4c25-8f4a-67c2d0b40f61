// playbackManager.js - 处理场景和轨迹播放功能
class PlaybackManager {
    constructor(app) {
        this.app = app;
        this.isPlaying = false;
        this.currentSceneId = null;
        this.playbackStatus = null;
        this.eventsBound = false; // 标记事件是否已绑定
        this.init();
    }

    init() {
        console.log('初始化PlaybackManager');
        
        // 立即设置按钮状态
        this.ensureButtonsEnabled();
        
        // 只绑定一次事件
        this.setupEventListeners();
        this.setupSignalRHandlers();
        
        // 延迟再次检查按钮状态，但不重新绑定事件
        setTimeout(() => {
            this.ensureButtonsEnabled();
        }, 500);
    }
    
    // 新增方法：确保按钮始终可用
    ensureButtonsEnabled() {
        console.log('确保场景控制按钮始终可用');
        const playSceneBtn = document.getElementById('playSceneBtn');
        const stopSceneBtn = document.getElementById('stopSceneBtn');
        
        if (playSceneBtn) {
            playSceneBtn.disabled = false;
        }
        
        if (stopSceneBtn) {
            stopSceneBtn.disabled = false;
        }
    }

    setupEventListeners() {
        // 防止重复绑定事件
        if (this.eventsBound) {
            console.log('事件已绑定，跳过重复绑定');
            return;
        }
        
        console.log('首次绑定播放和停止按钮事件');
        
        // 直接绑定按钮事件，不再使用重试机制
        this.bindButtonEvents();
        
        // 标记事件已绑定
        this.eventsBound = true;
    }
    
    // 新增方法：绑定按钮事件
    bindButtonEvents() {
        // 检查是否已经绑定过事件，避免重复绑定
        if (this._buttonsBound) {
            console.log('按钮事件已绑定，跳过重复绑定');
            return;
        }
        
        console.log('绑定播放和停止按钮事件');
        
        // 场景播放按钮点击事件
        const playSceneBtn = document.getElementById('playSceneBtn');
        if (playSceneBtn) {
            console.log('找到playSceneBtn，绑定点击事件');
            
            // 直接使用onclick，不再创建新按钮
            playSceneBtn.onclick = (e) => {
                console.log('点击了播放场景按钮');
                if (e) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                this.playScene();
                return false; // 阻止默认行为
            };
        } else {
            console.warn('未找到playSceneBtn按钮');
        }
        
        // 停止场景播放按钮点击事件
        const stopSceneBtn = document.getElementById('stopSceneBtn');
        if (stopSceneBtn) {
            console.log('找到stopSceneBtn，绑定点击事件');
            
            // 直接使用onclick，不再创建新按钮
            stopSceneBtn.onclick = (e) => {
                console.log('点击了停止播放按钮');
                if (e) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                this.stopPlayback();
                return false; // 阻止默认行为
            };
        } else {
            console.warn('未找到stopSceneBtn按钮');
        }
        
        // 标记按钮已绑定事件
        this._buttonsBound = true;
    }

    setupSignalRHandlers() {
        // 确保app.connection已经定义
        if (!this.app || !this.app.connection) {
            console.error('SignalR连接未初始化');
            setTimeout(() => this.setupSignalRHandlers(), 1000); // 延迟重试
            return;
        }

        // 监听轨迹播放状态变化
        this.app.connection.on("TrajectoryPlaybackChanged", (data) => {
            console.log("轨迹播放状态变化:", data);
            this.updatePlaybackStatus(data);
        });

        // 监听播放状态响应
        this.app.connection.on("PlaybackStatusResponse", (data) => {
            console.log("播放状态响应:", data);
            this.updatePlaybackStatus(data);
        });
    }

    async playScene() {
        // 获取当前选中的场景ID
        const sceneId = this.app.selectedSceneId || this.app.currentScene?.id;
        if (!sceneId) {
            alert('请先选择一个场景');
            return;
        }

        // 在播放前先检查状态，如果前端认为正在播放，先尝试停止
        if (this.isPlaying) {
            console.log('前端状态显示正在播放，先尝试停止当前播放');
            try {
                await this.stopPlayback(false); // 不显示提示
                // 等待一小段时间确保停止操作生效
                await new Promise(resolve => setTimeout(resolve, 300));
            } catch (error) {
                console.error('停止当前播放失败:', error);
                // 即使停止失败，也继续尝试播放
            }
        }

        try {
            console.log('准备发送播放场景请求，场景ID:', sceneId);
            // 更新播放状态显示
            this.updatePlaybackStatusDisplay('正在启动场景播放...');
            
            // 调用API播放场景
            const response = await fetch('/api/TrajectoryPlayback/play-scene', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sceneId: sceneId
                })
            });

            console.log('收到播放场景响应:', response.status);
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '播放场景失败');
            }

            const result = await response.json();
            console.log('播放场景结果:', result);
            
            // 更新状态
            this.isPlaying = true;
            this.currentSceneId = sceneId;
            this.updatePlaybackStatusDisplay(`正在播放场景`);
            
            // 只更新按钮文本，不改变按钮可用状态
            this.updateButtonText(true);
            // 确保按钮始终可用
            this.ensureButtonsEnabled();
        } catch (error) {
            console.error('播放场景错误:', error);
            this.updatePlaybackStatusDisplay(`播放场景失败: ${error.message}`, true);
            this.isPlaying = false;
            // 只更新按钮文本，不改变按钮可用状态
            this.updateButtonText(false);
            // 确保按钮始终可用
            this.ensureButtonsEnabled();
        }
    }

    async stopPlayback(showAlert = false) { // 默认不显示确认对话框
        console.log('stopPlayback方法被调用, showAlert=', showAlert);
        
        // 不再检查是否在播放，总是执行停止操作
        console.log('执行停止操作，不管当前是否在播放');
        
        try {
            // 更新播放状态显示
            this.updatePlaybackStatusDisplay('正在停止播放...');
            
            // 获取当前场景ID
            const sceneId = this.currentSceneId || this.app.selectedSceneId || this.app.currentScene?.id;
            
            let response;
            if (sceneId) {
                console.log(`发送停止场景 ${sceneId} 播放请求到API`);
                // 调用API停止场景播放
                response = await fetch(`/api/TrajectoryPlayback/stop-scene/${sceneId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
            } else {
                console.warn('无法获取场景ID，使用stop-all作为备选');
                // 如果无法获取场景ID，则调用stop-all作为备选
                response = await fetch('/api/TrajectoryPlayback/stop-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
            }

            console.log('收到API响应:', response.status);
            if (!response.ok) {
                let errorMessage = '停止播放失败';
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorMessage;
                } catch (e) {
                    console.error('解析错误响应失败:', e);
                }
                throw new Error(errorMessage);
            }

            let result;
            try {
                result = await response.json();
                console.log('停止播放结果:', result);
            } catch (e) {
                console.error('解析响应JSON失败:', e);
                result = { success: true, message: '播放已停止' };
            }
            
            // 更新状态
            this.isPlaying = false;
            this.currentSceneId = null;
            this.updatePlaybackStatusDisplay('播放已停止');
            
            // 只更新按钮文本，不改变按钮可用状态
            this.updateButtonText(false);
            // 确保按钮始终可用
            this.ensureButtonsEnabled();
            
            // 3秒后清除状态信息
            setTimeout(() => {
                if (!this.isPlaying) {
                    this.updatePlaybackStatusDisplay('');
                }
            }, 3000);
            
            // 不再显示提示对话框
            console.log('停止播放操作已完成');
        } catch (error) {
            console.error('停止播放错误:', error);
            this.updatePlaybackStatusDisplay(`停止播放失败: ${error.message}`, true);
            
            // 即使停止失败，也重置前端状态
            this.isPlaying = false;
            this.currentSceneId = null;
            // 只更新按钮文本，不改变按钮可用状态
            this.updateButtonText(false);
            // 确保按钮始终可用
            this.ensureButtonsEnabled();
            
            // 不再显示提示对话框
            console.error(`停止播放失败: ${error.message}`);
        }
    }

    updatePlaybackStatus(data) {
        // 更新内部状态
        this.playbackStatus = data;
        
        // 根据播放状态更新UI
        if (data.status === 'Playing') {
            this.isPlaying = true;
            this.updatePlaybackStatusDisplay(`正在播放轨迹: ${data.trajectoryId}`);
            // 只更新按钮文本，不改变按钮可用状态
            this.updateButtonText(true);
        } else if (data.status === 'Stopped') {
            this.isPlaying = false;
            this.updatePlaybackStatusDisplay('播放已停止');
            // 只更新按钮文本，不改变按钮可用状态
            this.updateButtonText(false);
            
            // 3秒后清除状态信息
            setTimeout(() => {
                if (!this.isPlaying) {
                    this.updatePlaybackStatusDisplay('');
                }
            }, 3000);
        }
        
        // 确保按钮始终可用
        this.ensureButtonsEnabled();
    }
    
    // 新增方法：只更新按钮文本，不改变按钮可用状态
    updateButtonText(isPlaying) {
        const playSceneBtn = document.getElementById('playSceneBtn');
        
        if (playSceneBtn) {
            if (isPlaying) {
                playSceneBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放中';
            } else {
                playSceneBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放';
            }
        }
    }

    updatePlaybackStatusDisplay(message, isError = false) {
        // 在页面上显示播放状态
        const statusElement = document.createElement('div');
        statusElement.className = isError ? 'playback-status error' : 'playback-status';
        statusElement.textContent = message;
        
        // 查找状态显示区域
        const container = document.querySelector('.control-group');
        if (container) {
            // 移除旧的状态显示
            const oldStatus = container.querySelector('.playback-status');
            if (oldStatus) {
                container.removeChild(oldStatus);
            }
            
            // 添加新的状态显示
            if (message) {
                container.appendChild(statusElement);
            }
        }
    }

    updateButtonState(isPlaying) {
        // 更新播放和停止按钮的状态 - 只更新文本，不禁用按钮
        const playSceneBtn = document.getElementById('playSceneBtn');
        const stopSceneBtn = document.getElementById('stopSceneBtn');
        
        if (playSceneBtn) {
            // 不再禁用按钮，只更新文本
            playSceneBtn.disabled = false;
            if (isPlaying) {
                playSceneBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放中';
            } else {
                playSceneBtn.innerHTML = '<i class="fas fa-play me-1"></i>播放';
            }
        }
        
        if (stopSceneBtn) {
            // 不再禁用按钮
            stopSceneBtn.disabled = false;
        }
    }
}

// 等待DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，准备初始化PlaybackManager');
    
    // 全局变量，用于跟踪是否已经初始化
    window.playbackManagerInitialized = false;
    
    // 等待app对象初始化完成
    const initPlaybackManager = () => {
        // 防止重复初始化
        if (window.playbackManagerInitialized) {
            console.log('PlaybackManager已初始化，跳过重复初始化');
            return;
        }
        
        console.log('尝试初始化PlaybackManager，检查window.app:', window.app);
        if (window.app) {
            console.log('window.app已存在，创建PlaybackManager实例');
            window.playbackManager = new PlaybackManager(window.app);
            window.playbackManagerInitialized = true;
        } else {
            console.log('window.app尚未初始化，100ms后重试');
            setTimeout(initPlaybackManager, 100);
        }
    };
    
    // 立即尝试初始化，如果失败则延迟重试
    initPlaybackManager();
    
    // 为了确保初始化，添加一个备用的延迟初始化
    setTimeout(() => {
        if (!window.playbackManagerInitialized) {
            console.log('备用初始化：PlaybackManager尚未初始化，强制创建实例');
            window.playbackManager = new PlaybackManager(window.app || {});
            window.playbackManagerInitialized = true;
        }
    }, 2000);
});
