namespace TrajectoryAuto.Core.Models;

/// <summary>
/// 坐标数据传输模型
/// </summary>
public class CoordinateData
{
    /// <summary>
    /// 通道编号
    /// </summary>
    public int ChannelNumber { get; set; }
    
    /// <summary>
    /// X坐标（米）
    /// </summary>
    public double X { get; set; }
    
    /// <summary>
    /// Y坐标（米）
    /// </summary>
    public double Y { get; set; }
    
    /// <summary>
    /// Z坐标（米）
    /// </summary>
    public double Z { get; set; }
    
    /// <summary>
    /// 通道地址
    /// </summary>
    public string? ChannelAddress { get; set; }
    
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 获取格式化的坐标数据字符串
    /// </summary>
    /// <returns>格式化的坐标数据字符串</returns>
    public string GetFormattedCoordinateString()
    {
        return $"{ChannelAddress}{X.ToString("F2")},{Y.ToString("F2")},{Z.To<PERSON>tring("F2")}";
    }
    
    /// <summary>
    /// 转换为JSON字符串
    /// </summary>
    /// <returns></returns>
    public string ToJson()
    {
        return System.Text.Json.JsonSerializer.Serialize(this);
    }
}

/// <summary>
/// 轨迹播放状态
/// </summary>
public enum PlaybackStatus
{
    /// <summary>
    /// 停止
    /// </summary>
    Stopped = 0,
    
    /// <summary>
    /// 播放中
    /// </summary>
    Playing = 1,
    
    /// <summary>
    /// 暂停
    /// </summary>
    Paused = 2
}