using Microsoft.Extensions.DependencyInjection;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Infrastructure.Services;

namespace TrajectoryAuto.Infrastructure.Extensions
{
    /// <summary>
    /// 服务集合扩展
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加简单UDP管理器
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSimpleUdpManager(this IServiceCollection services)
        {
            services.AddSingleton<SimpleUdpManager>();
            return services;
        }
        
        /// <summary>
        /// 添加所有通信相关服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddAllCommunicationServices(this IServiceCollection services)
        { 
            // 添加增强通信服务
            services.AddScoped<IEnhancedCommunicationService, EnhancedCommunicationService>();
            
            // 添加轨迹UDP通信服务
            services.AddScoped<TrajectoryUdpCommunicationService>();
            
            // 添加直接UDP服务（不使用连接池）
            services.AddScoped<DirectUdpService>();
            
            // 添加UDP连接池管理器
            services.AddSingleton<IUdpConnectionPoolManager, UdpConnectionPoolManager>();
            
            // 添加通信服务
            services.AddScoped<ICommunicationService, CommunicationService>();
            
            // 添加简单UDP管理器
            services.AddSimpleUdpManager();
            
            return services;
        }
        
        /// <summary>
        /// 添加增强通信服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddEnhancedCommunicationService(this IServiceCollection services)
        { 
            // 添加增强通信服务
            services.AddScoped<IEnhancedCommunicationService, EnhancedCommunicationService>();
            
            return services;
        } 
    }
}
