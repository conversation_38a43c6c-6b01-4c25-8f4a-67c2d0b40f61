{"Kestrel": {"Endpoints": {"Http": {"Url": "http://*:85"}}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "NewLife": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=trajectory.db;Cache Size=10000;Journal Mode=WAL;Synchronous=Normal"}, "UploadPath": "wwwroot/uploads", "Performance": {"EnableSQLiteOptimization": true, "BatchSize": 1000, "CacheTimeout": 300, "MaxConcurrentConnections": 100}, "AudioLightSync": {"EnableHighPerformanceMode": true, "MaxLatencyMs": 10, "BufferSize": 4096, "SampleRate": 48000}, "PlaybackPerformance": {"MaxConcurrentTasks": 50, "ChannelCapacity": 10000, "CleanupIntervalMinutes": 5, "MaxUdpLatencyMs": 100, "MemoryThresholdMB": 1000, "EnablePerformanceMonitoring": true, "PerformanceReportIntervalSeconds": 30}, "UdpConnectionPool": {"IdleTimeoutMinutes": 10, "MaxConnectionsPerType": 100, "AutoCleanupIntervalMinutes": 5, "EnableConnectionReuse": true, "ConnectionHealthCheckIntervalMinutes": 2, "MaxConnectionAge": 60, "PreferredConnectionType": "Channel"}}