using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Core.Models;

namespace TrajectoryAuto.Infrastructure.Services
{
    /// <summary>
    /// 增强通信服务实现
    /// </summary>
    public class EnhancedCommunicationService : IEnhancedCommunicationService
    {
        private readonly ILogger<EnhancedCommunicationService> _logger;
        private readonly IUdpConnectionPoolManager _connectionPoolManager;
        private readonly ICommunicationService _communicationService;
        private readonly DirectUdpService _directUdpService;
        
        // 当前活动的连接信息
        private int _currentSceneId;
        private int _currentChannelId;
        private string _currentServerIp = string.Empty;
        private int _currentServerPort;
        private bool _isRealTimeMode = false;
        
        // 是否使用直接UDP服务（不使用连接池）
        private bool _useDirectUdp = true;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="connectionPoolManager">UDP连接池管理器</param>
        /// <param name="communicationService">基础通信服务</param>
        /// <param name="directUdpService">直接UDP服务</param>
        public EnhancedCommunicationService(
            ILogger<EnhancedCommunicationService> logger,
            IUdpConnectionPoolManager connectionPoolManager,
            ICommunicationService communicationService,
            DirectUdpService directUdpService)
        {
            _logger = logger;
            _connectionPoolManager = connectionPoolManager;
            _communicationService = communicationService;
            _directUdpService = directUdpService;
        }

        /// <summary>
        /// 使用场景级别连接发送坐标数据
        /// </summary>
        public async Task<bool> SendCoordinateDataWithSceneConnectionAsync(int sceneId, string serverIp, int serverPort, CoordinateData coordinateData)
        {
            // 场景级别连接已不再支持，改用通道级别连接
            _logger.LogWarning("场景级别连接已不再支持，将使用通道级别连接代替");
            return await SendCoordinateDataWithChannelConnectionAsync(0, sceneId, serverIp, serverPort, coordinateData);
        }

        /// <summary>
        /// 使用通道级别连接发送坐标数据
        /// </summary>
        public async Task<bool> SendCoordinateDataWithChannelConnectionAsync(int channelId, int sceneId, string serverIp, int serverPort, CoordinateData coordinateData)
        {
            try
            {
                _logger.LogInformation($"发送通道级别数据: 通道ID={channelId}, 场景ID={sceneId}, IP={serverIp}, 端口={serverPort}");
                
                if (_useDirectUdp)
                {
                    // 使用直接UDP服务发送数据
                    bool result = await _directUdpService.SendCoordinateDataAsync(serverIp, serverPort, coordinateData);
                    
                    if (!result)
                    {
                        _logger.LogWarning($"直接UDP服务发送通道数据失败: 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    else
                    {
                        _logger.LogDebug($"直接UDP服务发送通道数据成功: 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    
                    return result;
                }
                else
                {
                    // 使用连接池发送数据
                    // 创建目标端点
                    var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                    
                    // 获取或创建通道级别连接
                    var connection = await _connectionPoolManager.GetChannelConnectionAsync(channelId, sceneId, endpoint);
                    
                    // 使用格式化的坐标数据字符串
                    string formattedData = coordinateData.GetFormattedCoordinateString();
                    var data = Encoding.UTF8.GetBytes(formattedData);
                    
                    // 发送数据
                    bool result = await _connectionPoolManager.SendDataAsync(connection, data);
                    
                    if (!result)
                    {
                        _logger.LogWarning($"通道级别连接发送数据失败: 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"通道级别连接发送数据出错: 通道ID={channelId}, 场景ID={sceneId}");
                return false;
            }
        }

        /// <summary>
        /// 使用轨迹级别连接发送坐标数据
        /// </summary>
        public async Task<bool> SendCoordinateDataWithTrajectoryConnectionAsync(int trajectoryId, int channelId, int sceneId, string serverIp, int serverPort, CoordinateData coordinateData)
        {
            try
            {
                _logger.LogInformation($"发送轨迹级别数据: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}, IP={serverIp}, 端口={serverPort}");
                
                if (_useDirectUdp)
                {
                    // 使用直接UDP服务发送数据
                    bool result = await _directUdpService.SendCoordinateDataAsync(serverIp, serverPort, coordinateData);
                    
                    if (!result)
                    {
                        _logger.LogWarning($"直接UDP服务发送轨迹数据失败: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    else
                    {
                        _logger.LogDebug($"直接UDP服务发送轨迹数据成功: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    
                    return result;
                }
                else
                {
                    // 使用连接池发送数据
                    // 创建目标端点
                    var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                    
                    // 获取或创建轨迹级别连接
                    var connection = await _connectionPoolManager.GetTrajectoryConnectionAsync(trajectoryId, channelId, sceneId, endpoint);
                    
                    // 使用格式化的坐标数据字符串
                    string formattedData = coordinateData.GetFormattedCoordinateString();
                    var data = Encoding.UTF8.GetBytes(formattedData);
                    
                    // 发送数据
                    bool result = await _connectionPoolManager.SendDataAsync(connection, data);
                    
                    if (!result)
                    {
                        _logger.LogWarning($"轨迹级别连接发送数据失败: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                    }
                    
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"轨迹级别连接发送数据出错: 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                return false;
            }
        }

        /// <summary>
        /// 批量发送坐标数据
        /// </summary>
        public async Task<int> SendBatchCoordinateDataAsync(UdpConnectionType connectionType, int? trajectoryId, int channelId, int sceneId, string serverIp, int serverPort, List<CoordinateData> coordinateDataList)
        {
            if (coordinateDataList == null || coordinateDataList.Count == 0)
            {
                return 0;
            }
            
            try
            {
                _logger.LogInformation($"批量发送数据: 连接类型={connectionType}, 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}, 数据量={coordinateDataList.Count}");
                
                if (_useDirectUdp)
                {
                    // 使用直接UDP服务发送数据
                    int successCount = 0;
                    foreach (var coordinateData in coordinateDataList)
                    {
                        bool result = await _directUdpService.SendCoordinateDataAsync(serverIp, serverPort, coordinateData);
                        if (result)
                        {
                            successCount++;
                        }
                    }
                    
                    _logger.LogInformation($"直接UDP服务批量发送数据: 总计={coordinateDataList.Count}, 成功={successCount}");
                    
                    return successCount;
                }
                else
                {
                    // 使用连接池发送数据
                    // 创建目标端点
                    var endpoint = new IPEndPoint(IPAddress.Parse(serverIp), serverPort);
                    
                    // 根据连接类型获取连接
                    UdpConnectionInfo connection;
                    switch (connectionType)
                    {
                        case UdpConnectionType.Trajectory when trajectoryId.HasValue:
                            connection = await _connectionPoolManager.GetTrajectoryConnectionAsync(trajectoryId.Value, channelId, sceneId, endpoint);
                            break;
                        case UdpConnectionType.Channel:
                        default:
                            connection = await _connectionPoolManager.GetChannelConnectionAsync(channelId, sceneId, endpoint);
                            break;
                    }
                    
                    // 准备数据列表
                    var dataList = new List<byte[]>();
                    foreach (var coordinateData in coordinateDataList)
                    {
                        string formattedData = coordinateData.GetFormattedCoordinateString();
                        var data = Encoding.UTF8.GetBytes(formattedData);
                        dataList.Add(data);
                    }
                    
                    // 批量发送数据
                    int successCount = await _connectionPoolManager.SendBatchDataAsync(connection, dataList);
                    
                    _logger.LogDebug($"批量发送数据: 总计={coordinateDataList.Count}, 成功={successCount}, 连接类型={connectionType}");
                    
                    return successCount;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"批量发送数据失败: 连接类型={connectionType}, 轨迹ID={trajectoryId}, 通道ID={channelId}, 场景ID={sceneId}");
                return 0;
            }
        }

        /// <summary>
        /// 停止轨迹级别连接
        /// </summary>
        public async Task StopTrajectoryConnectionAsync(int trajectoryId)
        {
            try
            {
                await _connectionPoolManager.ReleaseTrajectoryConnectionAsync(trajectoryId);
                _logger.LogInformation($"已停止轨迹级别连接: 轨迹ID={trajectoryId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止轨迹级别连接失败: 轨迹ID={trajectoryId}");
            }
        }

        /// <summary>
        /// 停止通道级别连接
        /// </summary>
        public async Task StopChannelConnectionAsync(int channelId)
        {
            try
            {
                await _connectionPoolManager.ReleaseChannelConnectionAsync(channelId);
                _logger.LogInformation($"已停止通道级别连接: 通道ID={channelId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止通道级别连接失败: 通道ID={channelId}");
            }
        }

        /// <summary>
        /// 停止场景级别连接
        /// </summary>
        public async Task StopSceneConnectionAsync(int sceneId)
        {
            try
            {
                await _connectionPoolManager.ReleaseAllSceneConnectionsAsync(sceneId);
                _logger.LogInformation($"已停止场景级别连接: 场景ID={sceneId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止场景级别连接失败: 场景ID={sceneId}");
            }
        }

        /// <summary>
        /// 启动实时通信
        /// </summary>
        public async Task<bool> StartRealTimeCommunicationAsync(string serverIp, int serverPort, int sceneId = 0, int channelId = 0)
        {
            try
            {
                // 保存当前连接信息
                _currentSceneId = sceneId > 0 ? sceneId : _currentSceneId;
                _currentChannelId = channelId > 0 ? channelId : _currentChannelId;
                _currentServerIp = serverIp;
                _currentServerPort = serverPort;
                
                _logger.LogInformation($"启动实时通信: {serverIp}:{serverPort}, 场景ID: {_currentSceneId}, 通道ID: {_currentChannelId}");
                
                // 同时设置基础通信服务的状态，传递场景ID和通道ID
                var baseResult = await _communicationService.StartRealTimeCommunicationAsync(serverIp, serverPort, _currentSceneId, _currentChannelId);
                
                _isRealTimeMode = true;
                
                _logger.LogInformation($"已启动实时通信: {serverIp}:{serverPort}, 场景ID: {_currentSceneId}, 通道ID: {_currentChannelId}");
                
                return baseResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"启动实时通信失败: {serverIp}:{serverPort}");
                return false;
            }
        }

        /// <summary>
        /// 停止实时通信
        /// </summary>
        public async Task<bool> StopRealTimeCommunicationAsync()
        {
            try
            {
                // 同时停止基础通信服务
                var baseResult = await _communicationService.StopRealTimeCommunicationAsync();
                
                // 确保当前场景和通道的连接也被正确释放
                if (_currentSceneId > 0)
                {
                    try
                    {
                        await _connectionPoolManager.ReleaseAllSceneConnectionsAsync(_currentSceneId);
                        _logger.LogInformation($"已释放场景连接: 场景ID={_currentSceneId}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"释放场景连接时出错: {ex.Message}");
                    }
                }
                
                if (_currentChannelId > 0)
                {
                    try
                    {
                        await _connectionPoolManager.ReleaseChannelConnectionAsync(_currentChannelId);
                        _logger.LogInformation($"已释放通道连接: 通道ID={_currentChannelId}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"释放通道连接时出错: {ex.Message}");
                    }
                }
                
                _isRealTimeMode = false;
                
                _logger.LogInformation("已停止实时通信");
                
                return baseResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止实时通信失败");
                _isRealTimeMode = false;
                return false;
            }
        }

        /// <summary>
        /// 获取连接使用建议
        /// </summary>
        public async Task<ConnectionUsageRecommendation> GetConnectionUsageRecommendationAsync(int sceneId, int channelCount, int trajectoryCount)
        {
            // 获取当前连接池状态
            var stats = await _connectionPoolManager.GetConnectionPoolStatsAsync();
            
            // 根据轨迹数量和通道数量决定使用哪种连接类型
            if (trajectoryCount > 10)
            {
                // 轨迹数量较多，使用通道级别连接
                return new ConnectionUsageRecommendation
                {
                    RecommendedConnectionType = UdpConnectionType.Channel,
                    Reason = $"轨迹数量较多({trajectoryCount}个)，使用通道级别连接可以减少连接数量",
                    EstimatedConnectionCount = channelCount,
                    ShouldUseBatchSending = trajectoryCount > 20,
                    RecommendedBatchSize = 5
                };
            }
            else if (trajectoryCount <= 5)
            {
                // 轨迹数量较少，使用轨迹级别连接
                return new ConnectionUsageRecommendation
                {
                    RecommendedConnectionType = UdpConnectionType.Trajectory,
                    Reason = $"轨迹数量较少({trajectoryCount}个)，使用轨迹级别连接可以提高发送效率",
                    EstimatedConnectionCount = trajectoryCount,
                    ShouldUseBatchSending = false,
                    RecommendedBatchSize = 1
                };
            }
            else
            {
                // 中等数量轨迹，根据当前连接池状态决定
                if (stats.TotalConnectionCount > 20)
                {
                    // 连接数量已经较多，使用通道级别连接
                    return new ConnectionUsageRecommendation
                    {
                        RecommendedConnectionType = UdpConnectionType.Channel,
                        Reason = $"当前连接池已有{stats.TotalConnectionCount}个连接，使用通道级别连接避免连接过多",
                        EstimatedConnectionCount = channelCount,
                        ShouldUseBatchSending = true,
                        RecommendedBatchSize = 3
                    };
                }
                else
                {
                    // 连接数量较少，使用轨迹级别连接
                    return new ConnectionUsageRecommendation
                    {
                        RecommendedConnectionType = UdpConnectionType.Trajectory,
                        Reason = $"当前连接池有{stats.TotalConnectionCount}个连接，可以使用轨迹级别连接",
                        EstimatedConnectionCount = trajectoryCount,
                        ShouldUseBatchSending = false,
                        RecommendedBatchSize = 1
                    };
                }
            }
        }

        /// <summary>
        /// 清理空闲连接
        /// </summary>
        public async Task<int> CleanupIdleConnectionsAsync(int idleTimeoutMinutes = 10)
        {
            return await _connectionPoolManager.CleanupIdleConnectionsAsync(idleTimeoutMinutes);
        }

        /// <summary>
        /// 获取连接池统计信息
        /// </summary>
        public async Task<UdpConnectionPoolStats> GetConnectionPoolStatsAsync()
        {
            return await _connectionPoolManager.GetConnectionPoolStatsAsync();
        }
    }
}