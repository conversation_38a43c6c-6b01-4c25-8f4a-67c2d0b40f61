using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using TrajectoryAuto.Core.Entities;
using TrajectoryAuto.Core.Interfaces;
using TrajectoryAuto.Infrastructure.Data.XCodeModels;

namespace TrajectoryAuto.Infrastructure.Services;

/// <summary>
/// 场景服务实现 - 使用NewLife.XCode优化音频灯光联动性能
/// </summary>
public class XCodeSceneService : ISceneService
{
    private readonly string _uploadPath;

    public XCodeSceneService()
    {
        _uploadPath = "wwwroot/uploads";
    }

    public async Task<List<Scene>> GetAllScenesAsync()
    {
        // XCode的异步查询，提升并发性能
        return await Task.Run(() =>
        {
            var entities = SceneEntity.FindAll().OrderByDescending(e => e.CreateTime).ToList();
            return entities.Select(e => e.ToBusinessEntity()).ToList();
        });
    }

    public async Task<Scene?> GetSceneByIdAsync(Guid id)
    {
        return await Task.Run(() =>
        {
            var entity = SceneEntity.FindById(id.ToString());
            if (entity == null) return null;

            var scene = entity.ToBusinessEntity();
            
            // 延迟加载通道信息，避免不必要的查询
            var channels = ChannelEntity.FindBySceneId(id.ToString());
            scene.Channels = channels.Select(c => c.ToBusinessEntity()).ToList();
            
            return scene;
        });
    }

    public async Task<Scene> CreateSceneAsync(Scene scene)
    {
        return await Task.Run(() =>
        {
            var entity = new SceneEntity();
            entity.FromBusinessEntity(scene);
            entity.Insert();
            
            return entity.ToBusinessEntity();
        });
    }

    public async Task<Scene> UpdateSceneAsync(Scene scene)
    {
        return await Task.Run(() =>
        {
            var entity = SceneEntity.FindById(scene.Id.ToString());
            if (entity == null)
                throw new ArgumentException("场景不存在");

            entity.FromBusinessEntity(scene);
            entity.Update();
            
            return entity.ToBusinessEntity();
        });
    }

    public async Task<bool> DeleteSceneAsync(Guid id)
    {
        return await Task.Run(() =>
        {
            var entity = SceneEntity.FindById(id.ToString());
            if (entity == null) return false;

            // 删除背景图片文件
            if (!string.IsNullOrEmpty(entity.BackgroundImagePath))
            {
                var filePath = Path.Combine(_uploadPath, entity.BackgroundImagePath);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }

            // XCode会自动处理级联删除
            return entity.Delete() > 0;
        });
    }

    public async Task<string> UploadBackgroundImageAsync(Guid sceneId, Stream imageStream, string fileName)
    {
        return await Task.Run(async () =>
        {
            var entity = SceneEntity.FindById(sceneId.ToString());
            if (entity == null)
                throw new ArgumentException("场景不存在");

            // 确保上传目录存在
            if (!Directory.Exists(_uploadPath))
            {
                Directory.CreateDirectory(_uploadPath);
            }

            // 生成唯一文件名
            var fileExtension = Path.GetExtension(fileName);
            var uniqueFileName = $"{sceneId}_{Guid.NewGuid()}{fileExtension}";
            var filePath = Path.Combine(_uploadPath, uniqueFileName);

            // 保存文件
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                await imageStream.CopyToAsync(fileStream);
            }

            // 删除旧的背景图片
            if (!string.IsNullOrEmpty(entity.BackgroundImagePath))
            {
                var oldFilePath = Path.Combine(_uploadPath, entity.BackgroundImagePath);
                if (File.Exists(oldFilePath))
                {
                    File.Delete(oldFilePath);
                }
            }

            // 更新场景背景图片路径
            entity.BackgroundImagePath = uniqueFileName;
            entity.Update();

            return uniqueFileName;
        });
    }
}